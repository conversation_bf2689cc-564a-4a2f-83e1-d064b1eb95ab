"""
Configuration Manager Mo<PERSON>le
Handles loading, saving, and managing configuration for Cybex
"""

import os
import yaml
import logging
from typing import Any, Dict, Optional, Tuple, List
from pathlib import Path


class ConfigManager:
    """
    Manages configuration loading, validation, and persistence
    """
    
    def __init__(self, config_path: str = "cybex/config/cybex_config.yaml"):
        """Initialize configuration manager"""
        self.config_path = Path(config_path)
        self.config_data: Dict[str, Any] = {}
        self.default_config = self._get_default_config()
        
        # Ensure config directory exists
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Load configuration
        self.load_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration values"""
        return {
            'mode': 'chat',
            'confirm_level': 'strict',
            'system': 'auto',
            'critical_ops': [
                'rm', 'del', 'format', 'fdisk', 'apt remove', 
                'yum remove', 'net stop', 'systemctl stop', 
                'reg delete', 'diskpart'
            ],
            'ollama': {
                'model': 'gemma2:7b',
                'host': 'localhost',
                'port': 11434,
                'timeout': 30,
                'max_tokens': 3000
            },
            'logging': {
                'level': 'INFO',
                'file_path': 'logs/cybex.log',
                'max_file_size': '10MB',
                'backup_count': 5,
                'console_output': True
            },
            'security': {
                'enable_sandbox': True,
                'max_command_length': 1000,
                'blocked_commands': [
                    'sudo rm -rf /',
                    'format c:',
                    'del /f /s /q c:\\'
                ],
                'allowed_directories': [
                    '/home', '/tmp', 'C:\\Users', 'C:\\Temp'
                ]
            },
            'agent': {
                'max_steps': 10,
                'step_timeout': 60,
                'auto_confirm_safe': False,
                'plan_review_required': True
            },
            'ui': {
                'theme': 'dark',
                'show_timestamps': True,
                'command_history_size': 100,
                'auto_save_session': True
            },
            'enterprise': {
                'ghostops_mode': False,
                'virustotal_api_key': '',
                'cloud_backup_enabled': False,
                'auto_patching': False,
                'plugin_system': False
            }
        }
    
    def load_config(self) -> bool:
        """Load configuration from file"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = yaml.safe_load(f) or {}
            else:
                # Create default config file
                self.config_data = self.default_config.copy()
                self.save_config()
            
            # Merge with defaults to ensure all keys exist
            self._merge_with_defaults()
            return True
            
        except Exception as e:
            logging.error(f"Failed to load config: {e}")
            self.config_data = self.default_config.copy()
            return False
    
    def save_config(self) -> bool:
        """Save current configuration to file"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, indent=2)
            return True
        except Exception as e:
            logging.error(f"Failed to save config: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """Set configuration value using dot notation"""
        keys = key.split('.')
        config = self.config_data
        
        try:
            # Navigate to the parent of the target key
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # Set the value
            config[keys[-1]] = value
            return self.save_config()
            
        except Exception as e:
            logging.error(f"Failed to set config value {key}: {e}")
            return False
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Get entire configuration section"""
        return self.config_data.get(section, {})
    
    def update_section(self, section: str, values: Dict[str, Any]) -> bool:
        """Update entire configuration section"""
        try:
            if section not in self.config_data:
                self.config_data[section] = {}
            
            self.config_data[section].update(values)
            return self.save_config()
            
        except Exception as e:
            logging.error(f"Failed to update section {section}: {e}")
            return False
    
    def reset_to_defaults(self) -> bool:
        """Reset configuration to default values"""
        try:
            self.config_data = self.default_config.copy()
            return self.save_config()
        except Exception as e:
            logging.error(f"Failed to reset config: {e}")
            return False
    
    def validate_config(self) -> Tuple[bool, List[str]]:
        """Validate current configuration"""
        errors = []
        
        # Check required sections
        required_sections = ['ollama', 'logging', 'security']
        for section in required_sections:
            if section not in self.config_data:
                errors.append(f"Missing required section: {section}")
        
        # Validate mode
        valid_modes = ['chat', 'agent']
        if self.get('mode') not in valid_modes:
            errors.append(f"Invalid mode. Must be one of: {valid_modes}")
        
        # Validate confirmation level
        valid_levels = ['strict', 'moderate', 'minimal']
        if self.get('confirm_level') not in valid_levels:
            errors.append(f"Invalid confirm_level. Must be one of: {valid_levels}")
        
        return len(errors) == 0, errors
    
    def _merge_with_defaults(self) -> None:
        """Merge current config with defaults to ensure all keys exist"""
        def merge_dict(default: Dict, current: Dict) -> Dict:
            result = default.copy()
            for key, value in current.items():
                if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                    result[key] = merge_dict(result[key], value)
                else:
                    result[key] = value
            return result
        
        self.config_data = merge_dict(self.default_config, self.config_data)
