#!/usr/bin/env python3
"""
Enhanced AI Integration for CYBEX Enterprise
Integrates advanced prompt patterns from AGtechdesigne AI systems
"""

import json
import re
from typing import Dict, List, Any, Optional
from pathlib import Path

class EnhancedAIIntegration:
    """Enhanced AI integration with advanced prompt patterns"""
    
    def __init__(self):
        self.prompt_templates = self._load_prompt_templates()
        self.tool_schemas = self._load_tool_schemas()
        self.context_patterns = self._load_context_patterns()
    
    def _load_prompt_templates(self) -> Dict[str, str]:
        """Load prompt templates based on AGtechdesigne patterns"""
        return {
            "system_prompt": self._get_system_prompt(),
            "tool_calling": self._get_tool_calling_prompt(),
            "code_analysis": self._get_code_analysis_prompt(),
            "security_focus": self._get_security_focus_prompt(),
            "enterprise_communication": self._get_enterprise_communication_prompt()
        }
    
    def _get_system_prompt(self) -> str:
        """Enhanced system prompt based on AGtechdesigne patterns"""
        return """
You are CYBEX ENTERPRISE AI, a powerful agentic system assistant. You operate in CYBEX ENTERPRISE, providing professional-grade system administration and security analysis.

<enterprise_identity>
- Expert system administrator with 40+ years experience
- Security-focused with enterprise compliance knowledge
- Performance optimization specialist
- Natural language interface for complex operations
- Professional communication for business environments
</enterprise_identity>

<core_principles>
1. Security First: Always prioritize security implications
2. Professional Communication: Enterprise-appropriate language
3. Actionable Insights: Provide specific, implementable recommendations
4. Risk Assessment: Evaluate and communicate risks clearly
5. Business Impact: Consider operational and financial implications
6. Compliance Aware: Understand regulatory requirements
</core_principles>
"""
    
    def _get_tool_calling_prompt(self) -> str:
        """Tool calling patterns from AGtechdesigne"""
        return """
<tool_calling_enhanced>
Follow these advanced tool calling patterns:

1. **Schema Compliance**: Always follow tool schemas exactly
2. **Parameter Validation**: Verify all required parameters
3. **Natural Language**: Never mention tool names to users
4. **Proactive Execution**: Execute plans immediately when possible
5. **Context Awareness**: Use system context to inform decisions
6. **Error Recovery**: Handle tool failures gracefully
7. **Batch Operations**: Group related operations efficiently

Example transformations:
- "Run security audit" → Execute security_audit tool
- "Check system performance" → Execute performance_analysis tool
- "Scan network security" → Execute network_security_scan tool
- "Clean temporary files" → Execute cleanup_temp_files tool
</tool_calling_enhanced>
"""
    
    def _get_code_analysis_prompt(self) -> str:
        """Code analysis patterns from AGtechdesigne"""
        return """
<code_analysis_enhanced>
When analyzing code or system configurations:

1. **Minimal Context**: Show only relevant changes
2. **Clear Markers**: Use "// ... existing code ..." for unchanged sections
3. **Security Review**: Always check for security implications
4. **Performance Impact**: Assess performance implications
5. **Best Practices**: Recommend industry best practices
6. **Documentation**: Provide clear explanations

Format for code suggestions:
```language:path/to/file
// ... existing code ...
{{ enhanced_change_1 }}
// ... existing code ...
{{ enhanced_change_2 }}
// ... existing code ...
```
</code_analysis_enhanced>
"""
    
    def _get_security_focus_prompt(self) -> str:
        """Security-focused patterns"""
        return """
<security_focus_enhanced>
Security-first approach for all operations:

1. **Risk Assessment**: Evaluate security risks before actions
2. **Principle of Least Privilege**: Recommend minimal necessary permissions
3. **Audit Trail**: Document security-relevant actions
4. **Compliance**: Consider regulatory requirements
5. **Threat Modeling**: Think like an attacker
6. **Defense in Depth**: Recommend layered security

Security priority levels:
- 🔴 CRITICAL: Immediate action required
- 🟡 HIGH: Address within 24 hours
- 🟠 MEDIUM: Address within week
- 🟢 LOW: Address during maintenance window
</security_focus_enhanced>
"""
    
    def _get_enterprise_communication_prompt(self) -> str:
        """Enterprise communication patterns"""
        return """
<enterprise_communication_enhanced>
Professional communication standards:

1. **Executive Summary**: Start with key findings
2. **Technical Details**: Provide depth for technical teams
3. **Business Impact**: Explain operational implications
4. **Action Items**: Clear, prioritized recommendations
5. **Risk Communication**: Quantify and qualify risks
6. **Timeline**: Provide realistic implementation timelines

Response structure:
📊 **EXECUTIVE SUMMARY**
🔍 **TECHNICAL ANALYSIS** 
⚠️ **RISK ASSESSMENT**
✅ **RECOMMENDATIONS**
📅 **IMPLEMENTATION TIMELINE**
💼 **BUSINESS IMPACT**
</enterprise_communication_enhanced>
"""
    
    def _load_tool_schemas(self) -> Dict[str, Dict]:
        """Load enhanced tool schemas"""
        return {
            "security_audit": {
                "description": "Comprehensive security analysis with enterprise reporting",
                "parameters": {
                    "scope": {"type": "string", "default": "full"},
                    "compliance_framework": {"type": "string", "default": "general"},
                    "report_format": {"type": "string", "default": "executive"}
                }
            },
            "performance_analysis": {
                "description": "Real-time performance monitoring with business impact",
                "parameters": {
                    "metrics": {"type": "array", "default": ["cpu", "memory", "disk", "network"]},
                    "duration": {"type": "integer", "default": 60},
                    "alert_thresholds": {"type": "object", "default": {}}
                }
            },
            "network_security_scan": {
                "description": "Network vulnerability assessment with risk scoring",
                "parameters": {
                    "scan_type": {"type": "string", "default": "comprehensive"},
                    "target_range": {"type": "string", "default": "local"},
                    "risk_threshold": {"type": "string", "default": "medium"}
                }
            }
        }
    
    def _load_context_patterns(self) -> Dict[str, str]:
        """Load context awareness patterns"""
        return {
            "system_state": "Current system metrics and status",
            "recent_operations": "Recently executed operations and results",
            "security_posture": "Current security configuration and alerts",
            "performance_baseline": "Historical performance data for comparison",
            "compliance_status": "Current compliance status and requirements"
        }
    
    def enhance_prompt(self, base_prompt: str, context: Dict[str, Any]) -> str:
        """Enhance prompt with AGtechdesigne-style patterns"""
        enhanced_prompt = base_prompt
        
        # Add system context
        if context.get("system_info"):
            enhanced_prompt += f"\n<system_context>\n{context['system_info']}\n</system_context>"
        
        # Add security context
        if context.get("security_status"):
            enhanced_prompt += f"\n<security_context>\n{context['security_status']}\n</security_context>"
        
        # Add performance context
        if context.get("performance_metrics"):
            enhanced_prompt += f"\n<performance_context>\n{context['performance_metrics']}\n</performance_context>"
        
        # Add enterprise templates
        enhanced_prompt += "\n" + self.prompt_templates["tool_calling"]
        enhanced_prompt += "\n" + self.prompt_templates["security_focus"]
        enhanced_prompt += "\n" + self.prompt_templates["enterprise_communication"]
        
        return enhanced_prompt
    
    def parse_natural_language_command(self, command: str) -> Dict[str, Any]:
        """Parse natural language commands using AGtechdesigne patterns"""
        command_lower = command.lower()
        
        # Security commands
        if any(word in command_lower for word in ["security", "audit", "sicurezza"]):
            return {
                "tool": "security_audit",
                "intent": "security_analysis",
                "priority": "high",
                "parameters": {"scope": "full", "report_format": "executive"}
            }
        
        # Performance commands
        elif any(word in command_lower for word in ["performance", "prestazioni", "analizza"]):
            return {
                "tool": "performance_analysis", 
                "intent": "performance_monitoring",
                "priority": "medium",
                "parameters": {"metrics": ["cpu", "memory", "disk"], "duration": 60}
            }
        
        # Network commands
        elif any(word in command_lower for word in ["network", "rete", "scansiona"]):
            return {
                "tool": "network_security_scan",
                "intent": "network_analysis", 
                "priority": "high",
                "parameters": {"scan_type": "comprehensive"}
            }
        
        # Cleanup commands
        elif any(word in command_lower for word in ["clean", "pulisci", "temp"]):
            return {
                "tool": "cleanup_temp_files",
                "intent": "system_maintenance",
                "priority": "low",
                "parameters": {"cleanup_type": "temp", "dry_run": False}
            }

        # Web search commands
        elif any(word in command_lower for word in ["cerca", "search", "trova", "ricerca"]):
            # Extract search query
            search_terms = ["cerca", "search", "trova", "ricerca", "web", "online", "internet"]
            query = command_lower
            for term in search_terms:
                query = query.replace(term, "").strip()

            return {
                "tool": "web_search",
                "intent": "web_search",
                "priority": "medium",
                "parameters": {"query": query or command, "engine": "google", "max_results": 10}
            }

        # Web page fetch commands
        elif any(word in command_lower for word in ["apri", "open", "vai", "visita", "fetch"]):
            # Extract URL if present
            url_match = re.search(r'https?://[^\s]+', command)
            if url_match:
                url = url_match.group()
            else:
                # Try to extract domain-like patterns
                domain_match = re.search(r'([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}', command)
                url = domain_match.group() if domain_match else command.replace("apri ", "").replace("open ", "")

            return {
                "tool": "fetch_webpage",
                "intent": "web_fetch",
                "priority": "medium",
                "parameters": {"url": url, "use_browser": False}
            }

        # Web analysis commands
        elif any(word in command_lower for word in ["analizza", "analyze", "esamina", "studia"]) and any(word in command_lower for word in ["sito", "website", "pagina", "page"]):
            # Extract URL if present
            url_match = re.search(r'https?://[^\s]+', command)
            if url_match:
                url = url_match.group()
            else:
                domain_match = re.search(r'([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}', command)
                url = domain_match.group() if domain_match else ""

            # Determine analysis type
            analysis_type = "general"
            if any(word in command_lower for word in ["seo"]):
                analysis_type = "seo"
            elif any(word in command_lower for word in ["contenuto", "content", "qualità", "quality"]):
                analysis_type = "content"
            elif any(word in command_lower for word in ["tecnico", "technical", "performance"]):
                analysis_type = "technical"

            return {
                "tool": "analyze_webpage",
                "intent": "web_analysis",
                "priority": "medium",
                "parameters": {"url": url, "analysis_type": analysis_type}
            }
        
        # Default fallback
        return {
            "tool": "get_system_info",
            "intent": "information_gathering",
            "priority": "medium",
            "parameters": {}
        }
    
    def format_enterprise_response(self, tool_result: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Format response using enterprise communication patterns"""
        response = "📊 **CYBEX ENTERPRISE ANALYSIS**\n\n"
        
        # Executive Summary
        response += "**🎯 EXECUTIVE SUMMARY**\n"
        response += f"Operation: {context.get('operation', 'System Analysis')}\n"
        response += f"Status: {'✅ Completed' if tool_result.get('success') else '❌ Failed'}\n"
        response += f"Priority: {context.get('priority', 'Medium').upper()}\n\n"
        
        # Technical Analysis
        response += "**🔍 TECHNICAL ANALYSIS**\n"
        if tool_result.get('output'):
            response += f"{tool_result['output']}\n\n"
        
        # Risk Assessment
        response += "**⚠️ RISK ASSESSMENT**\n"
        response += self._generate_risk_assessment(tool_result, context)
        response += "\n"
        
        # Recommendations
        response += "**✅ RECOMMENDATIONS**\n"
        response += self._generate_recommendations(tool_result, context)
        response += "\n"
        
        # Next Steps
        response += "**📅 NEXT STEPS**\n"
        response += self._generate_next_steps(tool_result, context)
        
        return response
    
    def _generate_risk_assessment(self, tool_result: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate risk assessment based on results"""
        if not tool_result.get('success'):
            return "🔴 **HIGH RISK**: Operation failed - immediate attention required"
        
        risk_level = context.get('priority', 'medium').lower()
        if risk_level == 'high':
            return "🟡 **MEDIUM RISK**: Monitor closely and implement recommendations"
        elif risk_level == 'low':
            return "🟢 **LOW RISK**: Normal operations, routine maintenance recommended"
        else:
            return "🟠 **MODERATE RISK**: Address during next maintenance window"
    
    def _generate_recommendations(self, tool_result: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate actionable recommendations"""
        recommendations = []
        
        if tool_result.get('success'):
            recommendations.append("• Continue monitoring system metrics")
            recommendations.append("• Schedule regular maintenance windows")
            recommendations.append("• Document findings for compliance audit")
        else:
            recommendations.append("• Investigate root cause of failure")
            recommendations.append("• Implement corrective measures")
            recommendations.append("• Review system configuration")
        
        return "\n".join(recommendations)
    
    def _generate_next_steps(self, tool_result: Dict[str, Any], context: Dict[str, Any]) -> str:
        """Generate next steps based on results"""
        steps = []
        
        if context.get('intent') == 'security_analysis':
            steps.append("• Review security recommendations")
            steps.append("• Implement critical security fixes")
            steps.append("• Schedule follow-up security scan")
        elif context.get('intent') == 'performance_monitoring':
            steps.append("• Monitor performance trends")
            steps.append("• Optimize resource allocation")
            steps.append("• Plan capacity upgrades if needed")
        else:
            steps.append("• Review analysis results")
            steps.append("• Implement recommended changes")
            steps.append("• Schedule follow-up assessment")
        
        return "\n".join(steps)


def create_enhanced_ai_integration() -> EnhancedAIIntegration:
    """Factory function to create enhanced AI integration"""
    return EnhancedAIIntegration()


# Example usage
if __name__ == "__main__":
    ai_integration = create_enhanced_ai_integration()
    
    # Test natural language parsing
    command = "Esegui security audit completo"
    parsed = ai_integration.parse_natural_language_command(command)
    print(f"Parsed command: {json.dumps(parsed, indent=2)}")
    
    # Test response formatting
    mock_result = {
        "success": True,
        "output": "Security audit completed successfully. Found 3 recommendations."
    }
    mock_context = {
        "operation": "Security Audit",
        "priority": "high",
        "intent": "security_analysis"
    }
    
    formatted_response = ai_integration.format_enterprise_response(mock_result, mock_context)
    print(f"\nFormatted response:\n{formatted_response}")
