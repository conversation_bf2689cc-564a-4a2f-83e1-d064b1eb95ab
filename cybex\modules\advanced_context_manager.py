#!/usr/bin/env python3
"""
Advanced Context Manager for CYBEX Enterprise
Manages intelligent context awareness, session state, and predictive analysis
"""

import json
import time
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import psutil
import pickle
from collections import deque, defaultdict


@dataclass
class ContextSnapshot:
    """Snapshot of system context at a point in time"""
    timestamp: float
    system_metrics: Dict[str, Any]
    user_activity: Dict[str, Any]
    security_status: Dict[str, Any]
    performance_indicators: Dict[str, Any]
    active_operations: List[str]
    risk_assessment: str
    session_id: str


@dataclass
class UserSession:
    """User session tracking"""
    session_id: str
    start_time: float
    last_activity: float
    command_count: int
    preferred_tools: List[str]
    security_level: str
    interaction_patterns: Dict[str, Any]
    context_history: List[ContextSnapshot]


class AdvancedContextManager:
    """Advanced context management with predictive capabilities"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Context storage
        self.current_context = {}
        self.context_history = deque(maxlen=1000)  # Keep last 1000 context snapshots
        self.user_sessions = {}
        self.active_session_id = None
        
        # Predictive analysis
        self.pattern_analyzer = PatternAnalyzer()
        self.risk_assessor = RiskAssessor()
        self.performance_predictor = PerformancePredictor()
        
        # Context awareness settings
        self.context_update_interval = 30  # seconds
        self.max_session_duration = 24 * 3600  # 24 hours
        self.context_persistence_path = Path("cybex/data/context_cache.pkl")
        
        # Background monitoring
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Initialize context
        self._initialize_context()
        self._load_persistent_context()
        self._start_background_monitoring()
    
    def _initialize_context(self):
        """Initialize base context structure"""
        self.current_context = {
            'system': self._get_system_context(),
            'security': self._get_security_context(),
            'performance': self._get_performance_context(),
            'user': self._get_user_context(),
            'session': self._get_session_context(),
            'predictive': self._get_predictive_context(),
            'metadata': {
                'last_update': time.time(),
                'update_count': 0,
                'context_version': '2.0'
            }
        }
    
    def _get_system_context(self) -> Dict[str, Any]:
        """Get comprehensive system context"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory': {
                    'percent': psutil.virtual_memory().percent,
                    'available': psutil.virtual_memory().available,
                    'total': psutil.virtual_memory().total
                },
                'disk': {
                    'percent': psutil.disk_usage('/').percent,
                    'free': psutil.disk_usage('/').free,
                    'total': psutil.disk_usage('/').total
                },
                'network': {
                    'connections': len(psutil.net_connections()),
                    'io_counters': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {}
                },
                'processes': {
                    'count': len(psutil.pids()),
                    'top_cpu': self._get_top_processes_by_cpu(5),
                    'top_memory': self._get_top_processes_by_memory(5)
                },
                'boot_time': psutil.boot_time(),
                'uptime': time.time() - psutil.boot_time()
            }
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not get system context: {e}")
            return {'error': str(e), 'timestamp': time.time()}
    
    def _get_security_context(self) -> Dict[str, Any]:
        """Get security-related context"""
        return {
            'threat_level': self.risk_assessor.assess_current_threat_level(),
            'recent_security_events': self.risk_assessor.get_recent_security_events(),
            'security_policies_active': True,
            'last_security_scan': self.risk_assessor.get_last_scan_time(),
            'compliance_status': self.risk_assessor.get_compliance_status(),
            'active_security_measures': self.risk_assessor.get_active_measures()
        }
    
    def _get_performance_context(self) -> Dict[str, Any]:
        """Get performance-related context"""
        return {
            'current_load': self.performance_predictor.get_current_load(),
            'predicted_load': self.performance_predictor.predict_load(300),  # 5 minutes ahead
            'bottlenecks': self.performance_predictor.identify_bottlenecks(),
            'optimization_opportunities': self.performance_predictor.get_optimization_suggestions(),
            'resource_trends': self.performance_predictor.get_resource_trends(),
            'performance_score': self.performance_predictor.calculate_performance_score()
        }
    
    def _get_user_context(self) -> Dict[str, Any]:
        """Get user behavior and preferences context"""
        if not self.active_session_id or self.active_session_id not in self.user_sessions:
            return {'session_active': False}
        
        session = self.user_sessions[self.active_session_id]
        return {
            'session_active': True,
            'session_duration': time.time() - session.start_time,
            'command_count': session.command_count,
            'preferred_tools': session.preferred_tools,
            'security_level': session.security_level,
            'interaction_patterns': session.interaction_patterns,
            'last_activity': session.last_activity,
            'user_expertise_level': self.pattern_analyzer.assess_user_expertise(session)
        }
    
    def _get_session_context(self) -> Dict[str, Any]:
        """Get current session context"""
        return {
            'session_id': self.active_session_id,
            'session_count': len(self.user_sessions),
            'context_snapshots': len(self.context_history),
            'monitoring_active': self.monitoring_active,
            'last_context_update': self.current_context.get('metadata', {}).get('last_update', 0)
        }
    
    def _get_predictive_context(self) -> Dict[str, Any]:
        """Get predictive analysis context"""
        return {
            'predicted_next_commands': self.pattern_analyzer.predict_next_commands(),
            'resource_usage_forecast': self.performance_predictor.forecast_resource_usage(),
            'potential_issues': self.risk_assessor.predict_potential_issues(),
            'optimization_recommendations': self.performance_predictor.get_proactive_recommendations(),
            'security_recommendations': self.risk_assessor.get_proactive_security_recommendations()
        }
    
    def _get_top_processes_by_cpu(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top processes by CPU usage"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return sorted(processes, key=lambda x: x['cpu_percent'] or 0, reverse=True)[:limit]
        except Exception:
            return []
    
    def _get_top_processes_by_memory(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top processes by memory usage"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            return sorted(processes, key=lambda x: x['memory_percent'] or 0, reverse=True)[:limit]
        except Exception:
            return []
    
    def update_context(self, force: bool = False) -> Dict[str, Any]:
        """Update current context"""
        try:
            current_time = time.time()
            last_update = self.current_context.get('metadata', {}).get('last_update', 0)
            
            # Check if update is needed
            if not force and (current_time - last_update) < self.context_update_interval:
                return self.current_context
            
            # Update all context components
            self.current_context.update({
                'system': self._get_system_context(),
                'security': self._get_security_context(),
                'performance': self._get_performance_context(),
                'user': self._get_user_context(),
                'session': self._get_session_context(),
                'predictive': self._get_predictive_context(),
                'metadata': {
                    'last_update': current_time,
                    'update_count': self.current_context.get('metadata', {}).get('update_count', 0) + 1,
                    'context_version': '2.0'
                }
            })
            
            # Create context snapshot
            snapshot = ContextSnapshot(
                timestamp=current_time,
                system_metrics=self.current_context['system'],
                user_activity=self.current_context['user'],
                security_status=self.current_context['security'],
                performance_indicators=self.current_context['performance'],
                active_operations=self._get_active_operations(),
                risk_assessment=self.current_context['security'].get('threat_level', 'UNKNOWN'),
                session_id=self.active_session_id or 'no_session'
            )
            
            # Add to history
            self.context_history.append(snapshot)
            
            # Update session context history
            if self.active_session_id and self.active_session_id in self.user_sessions:
                session = self.user_sessions[self.active_session_id]
                session.context_history.append(snapshot)
                # Keep only last 100 snapshots per session
                if len(session.context_history) > 100:
                    session.context_history = session.context_history[-100:]
            
            if self.logger:
                self.logger.debug(f"Context updated - Update #{self.current_context['metadata']['update_count']}")
            
            return self.current_context
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Context update failed: {e}")
            return self.current_context
    
    def get_context(self, components: List[str] = None) -> Dict[str, Any]:
        """Get current context or specific components"""
        if components is None:
            return self.current_context.copy()
        
        return {comp: self.current_context.get(comp, {}) for comp in components}
    
    def _get_active_operations(self) -> List[str]:
        """Get list of currently active operations"""
        # This would be populated by other system components
        return []
    
    def _start_background_monitoring(self):
        """Start background context monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._background_monitor, daemon=True)
            self.monitoring_thread.start()
            if self.logger:
                self.logger.info("Background context monitoring started")
    
    def _background_monitor(self):
        """Background monitoring loop"""
        while self.monitoring_active:
            try:
                self.update_context()
                time.sleep(self.context_update_interval)
            except Exception as e:
                if self.logger:
                    self.logger.error(f"Background monitoring error: {e}")
                time.sleep(60)  # Wait longer on error
    
    def _load_persistent_context(self):
        """Load persistent context from disk"""
        try:
            if self.context_persistence_path.exists():
                with open(self.context_persistence_path, 'rb') as f:
                    data = pickle.load(f)
                    self.user_sessions = data.get('user_sessions', {})
                    # Load other persistent data as needed
                if self.logger:
                    self.logger.info("Persistent context loaded")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not load persistent context: {e}")
    
    def save_persistent_context(self):
        """Save persistent context to disk"""
        try:
            self.context_persistence_path.parent.mkdir(parents=True, exist_ok=True)
            data = {
                'user_sessions': self.user_sessions,
                'last_save': time.time()
            }
            with open(self.context_persistence_path, 'wb') as f:
                pickle.dump(data, f)
            if self.logger:
                self.logger.info("Persistent context saved")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Could not save persistent context: {e}")


class PatternAnalyzer:
    """Analyzes user patterns and predicts behavior"""
    
    def __init__(self):
        self.command_patterns = defaultdict(list)
        self.time_patterns = defaultdict(list)
        self.sequence_patterns = deque(maxlen=100)
    
    def predict_next_commands(self) -> List[str]:
        """Predict likely next commands based on patterns"""
        # Simplified implementation
        return ["get_system_info", "security_audit", "performance_analysis"]
    
    def assess_user_expertise(self, session: UserSession) -> str:
        """Assess user expertise level based on session data"""
        if session.command_count > 100:
            return "EXPERT"
        elif session.command_count > 20:
            return "INTERMEDIATE"
        else:
            return "BEGINNER"


class RiskAssessor:
    """Assesses security risks and threats"""
    
    def assess_current_threat_level(self) -> str:
        """Assess current threat level"""
        # Simplified implementation
        return "LOW"
    
    def get_recent_security_events(self) -> List[Dict[str, Any]]:
        """Get recent security events"""
        return []
    
    def get_last_scan_time(self) -> float:
        """Get last security scan time"""
        return time.time() - 3600  # 1 hour ago
    
    def get_compliance_status(self) -> Dict[str, Any]:
        """Get compliance status"""
        return {"status": "COMPLIANT", "last_check": time.time()}
    
    def get_active_measures(self) -> List[str]:
        """Get active security measures"""
        return ["firewall", "antivirus", "access_control"]
    
    def predict_potential_issues(self) -> List[Dict[str, Any]]:
        """Predict potential security issues"""
        return []
    
    def get_proactive_security_recommendations(self) -> List[str]:
        """Get proactive security recommendations"""
        return ["Update system patches", "Review access logs", "Scan for vulnerabilities"]


class PerformancePredictor:
    """Predicts performance issues and optimizations"""
    
    def get_current_load(self) -> Dict[str, float]:
        """Get current system load"""
        try:
            return {
                'cpu': psutil.cpu_percent(),
                'memory': psutil.virtual_memory().percent,
                'disk': psutil.disk_usage('/').percent
            }
        except:
            return {'cpu': 0, 'memory': 0, 'disk': 0}
    
    def predict_load(self, seconds_ahead: int) -> Dict[str, float]:
        """Predict system load N seconds ahead"""
        current = self.get_current_load()
        # Simplified prediction - in reality would use ML models
        return {k: min(v * 1.1, 100) for k, v in current.items()}
    
    def identify_bottlenecks(self) -> List[str]:
        """Identify current system bottlenecks"""
        bottlenecks = []
        load = self.get_current_load()
        
        if load['cpu'] > 80:
            bottlenecks.append("CPU")
        if load['memory'] > 85:
            bottlenecks.append("Memory")
        if load['disk'] > 90:
            bottlenecks.append("Disk")
        
        return bottlenecks
    
    def get_optimization_suggestions(self) -> List[str]:
        """Get optimization suggestions"""
        suggestions = []
        bottlenecks = self.identify_bottlenecks()
        
        if "CPU" in bottlenecks:
            suggestions.append("Consider closing CPU-intensive applications")
        if "Memory" in bottlenecks:
            suggestions.append("Free up memory by closing unused applications")
        if "Disk" in bottlenecks:
            suggestions.append("Clean up disk space or move files to external storage")
        
        return suggestions
    
    def get_resource_trends(self) -> Dict[str, str]:
        """Get resource usage trends"""
        return {
            'cpu': 'stable',
            'memory': 'increasing',
            'disk': 'stable'
        }
    
    def calculate_performance_score(self) -> int:
        """Calculate overall performance score (0-100)"""
        load = self.get_current_load()
        score = 100 - max(load['cpu'], load['memory'], load['disk'])
        return max(0, int(score))
    
    def forecast_resource_usage(self) -> Dict[str, Any]:
        """Forecast resource usage"""
        return {
            'next_hour': self.predict_load(3600),
            'trend': self.get_resource_trends(),
            'recommendations': self.get_optimization_suggestions()
        }
    
    def get_proactive_recommendations(self) -> List[str]:
        """Get proactive performance recommendations"""
        return [
            "Schedule system maintenance during low usage periods",
            "Monitor resource usage trends",
            "Consider hardware upgrades if bottlenecks persist"
        ]


def create_advanced_context_manager(log_manager=None) -> AdvancedContextManager:
    """Factory function to create advanced context manager"""
    return AdvancedContextManager(log_manager)


# Example usage
if __name__ == "__main__":
    context_manager = create_advanced_context_manager()
    
    # Update and get context
    context = context_manager.update_context(force=True)
    print(f"Context updated: {context['metadata']['update_count']} updates")
    
    # Get specific components
    system_context = context_manager.get_context(['system', 'performance'])
    print(f"System CPU: {system_context['system']['cpu_percent']:.1f}%")
    print(f"Performance Score: {system_context['performance']['performance_score']}")
