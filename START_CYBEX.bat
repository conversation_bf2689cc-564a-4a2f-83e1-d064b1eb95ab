@echo off
title CYBEX Enterprise - Quick Start
color 0B

echo.
echo    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗
echo   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝
echo   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝ 
echo   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗ 
echo   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗
echo    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝
echo.
echo           🚀 CYBEX ENTERPRISE - Quick Start
echo              Advanced AI Assistant with Monitor
echo.

REM Quick launch without extensive checks
echo 🎯 Quick launching CYBEX Enterprise...

python -c "from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI; app = CybexFuturisticGUI(); app.run()" 2>nul

if errorlevel 1 (
    echo ❌ Quick start failed, trying detailed launcher...
    call CYBEX_ENTERPRISE_MONITOR.bat
)

echo.
echo 👋 CYBEX Enterprise session ended
timeout /t 3 >nul
