agent:
  auto_confirm_safe: false
  auto_retry: true
  execution_mode: sequential
  max_failure_rate: 0.3
  max_parallel_tasks: 3
  max_steps: 10
  plan_review_required: true
  rollback_on_failure: true
  step_timeout: 60
confirm_level: strict
critical_ops:
- rm
- del
- format
- fdisk
- apt remove
- yum remove
- net stop
- systemctl stop
- reg delete
- diskpart
database:
  backup_enabled: true
  backup_interval: 24
  enabled: true
  path: cybex/data/cybex.db
  type: sqlite
disk_management:
  auto_cleanup: false
  cleanup_threshold: 85
  enabled: true
  log_file_age: 30
  smart_monitoring: true
  temp_file_age: 7
enterprise:
  auto_patching: false
  cloud_backup_enabled: false
  ghostops_mode: false
  plugin_system: false
  virustotal_api_key: ''
logging:
  backup_count: 5
  console_output: true
  file_path: logs/cybex.log
  level: INFO
  max_file_size: 10MB
mode: agent
monitoring:
  alert_enabled: true
  cpu_threshold: 80
  disk_threshold: 90
  enabled: true
  interval: 30
  memory_threshold: 85
  network_threshold: 80
  performance_history: 24
ollama:
  host: localhost
  max_tokens: 4000
  model: gemma3:4b
  port: 11434
  timeout: 180
security:
  allowed_directories:
  - /home
  - /tmp
  - C:\Users
  - C:\Temp
  blocked_commands:
  - sudo rm -rf /
  - 'format c:'
  - del /f /s /q c:\
  enable_sandbox: true
  max_command_length: 1000
snapshots:
  auto_snapshot_before_agent: true
  directory: snapshots
  enabled: true
  include_files: true
  include_registry: true
  include_services: true
  max_snapshots: 10
system: auto
ui:
  auto_save_session: true
  command_history_size: 100
  show_timestamps: true
  theme: dark
