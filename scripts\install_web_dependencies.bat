@echo off
echo ========================================
echo CYBEX ENTERPRISE - WEB DEPENDENCIES INSTALLER
echo ========================================
echo.

echo 🌐 Installing Web Browsing Dependencies...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Install web browsing requirements
echo 📦 Installing web browsing packages...
pip install -r requirements_web.txt

if errorlevel 1 (
    echo ❌ Failed to install web dependencies
    echo.
    echo 💡 Try running as administrator or check your internet connection
    pause
    exit /b 1
)

echo.
echo ✅ Web dependencies installed successfully!
echo.

REM Check Chrome/Chromium for Selenium
echo 🔍 Checking for Chrome browser...
where chrome >nul 2>&1
if errorlevel 1 (
    where chromium >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  Chrome/Chromium not found
        echo 💡 For full web browsing capabilities, install Google Chrome
        echo    Download from: https://www.google.com/chrome/
    ) else (
        echo ✅ Chromium found
    )
) else (
    echo ✅ Chrome found
)

echo.
echo 🎉 Web browsing setup complete!
echo.
echo 🚀 CYBEX Enterprise now has web browsing capabilities:
echo    • Web search using Google, Bing, DuckDuckGo
echo    • Webpage content fetching and analysis
echo    • SEO and technical analysis
echo    • Browsing history tracking
echo.
echo 💡 Test with commands like:
echo    "Cerca informazioni su intelligenza artificiale"
echo    "Apri sito google.com"
echo    "Analizza sito web per SEO"
echo.
pause
