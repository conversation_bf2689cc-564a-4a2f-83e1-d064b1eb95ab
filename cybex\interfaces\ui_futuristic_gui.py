#!/usr/bin/env python3
"""
Cybex Futuristic GUI - Enterprise Edition
Professional cyberpunk interface with Ollama integration
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import time
import os
import sys
import json
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any
import queue

# Import Ollama Monitor
try:
    from ..modules.ollama_monitor import OllamaMonitor, OllamaStatus
except ImportError:
    OllamaMonitor = None
    OllamaStatus = None

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from cybex.core.cybex_core import CybexCore
    from cybex.modules.ollama_interface import OllamaInterface
    from cybex.modules.agent_tools import AgentTools
except ImportError as e:
    print(f"Warning: Could not import Cybex modules: {e}")

class FuturisticTheme:
    """Enterprise cyberpunk theme"""
    
    # Colors
    BG_DARK = "#0a0a0a"
    BG_PANEL = "#1a1a2e"
    BG_ACCENT = "#16213e"
    
    # Neon colors
    NEON_CYAN = "#00ffff"
    NEON_PURPLE = "#bf00ff"
    NEON_GREEN = "#00ff41"
    NEON_ORANGE = "#ff6600"
    NEON_PINK = "#ff0080"
    
    # Text colors
    TEXT_PRIMARY = "#ffffff"
    TEXT_SECONDARY = "#b0b0b0"
    TEXT_MUTED = "#666666"
    
    # Status colors
    SUCCESS = "#00ff41"
    WARNING = "#ff6600"
    ERROR = "#ff0080"

class FuturisticTerminal(tk.Frame):
    """Enterprise terminal with AI integration"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=FuturisticTheme.BG_DARK, **kwargs)
        
        self.command_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.gui_parent = None
        self.command_history = []
        self.history_index = -1
        
        self.setup_ui()
        self.setup_welcome()
        self.start_output_monitor()
    
    def setup_ui(self):
        """Setup terminal UI"""
        # Output area
        self.output_text = scrolledtext.ScrolledText(
            self,
            bg=FuturisticTheme.BG_DARK,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 10),
            insertbackground=FuturisticTheme.NEON_CYAN,
            selectbackground=FuturisticTheme.BG_ACCENT,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Input frame
        input_frame = tk.Frame(self, bg=FuturisticTheme.BG_DARK)
        input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Prompt label
        prompt_label = tk.Label(
            input_frame,
            text="cybex❯",
            bg=FuturisticTheme.BG_DARK,
            fg=FuturisticTheme.NEON_CYAN,
            font=("Consolas", 10, "bold")
        )
        prompt_label.pack(side=tk.LEFT)
        
        # Input entry
        self.input_entry = tk.Entry(
            input_frame,
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 10),
            insertbackground=FuturisticTheme.NEON_CYAN,
            selectbackground=FuturisticTheme.BG_ACCENT,
            bd=1,
            relief=tk.SOLID
        )
        self.input_entry.pack(fill=tk.X, expand=True, padx=(5, 0))
        self.input_entry.bind('<Return>', self.on_enter)
        self.input_entry.bind('<Up>', self.on_history_up)
        self.input_entry.bind('<Down>', self.on_history_down)
        self.input_entry.focus()
        
        # Setup text tags for colors
        self.setup_text_tags()
    
    def setup_text_tags(self):
        """Setup text color tags"""
        tags = {
            "header": {"foreground": FuturisticTheme.NEON_CYAN, "font": ("Consolas", 10, "bold")},
            "info": {"foreground": FuturisticTheme.TEXT_PRIMARY},
            "success": {"foreground": FuturisticTheme.SUCCESS},
            "warning": {"foreground": FuturisticTheme.WARNING},
            "error": {"foreground": FuturisticTheme.ERROR},
            "ai": {"foreground": FuturisticTheme.NEON_PURPLE, "font": ("Consolas", 10, "italic")},
            "system": {"foreground": FuturisticTheme.TEXT_SECONDARY}
        }
        
        for tag, config in tags.items():
            self.output_text.tag_configure(tag, **config)
    
    def setup_welcome(self):
        """Setup welcome message"""
        welcome = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 CYBEX ENTERPRISE TERMINAL                              ║
║                      Advanced AI Command Interface                           ║
╚══════════════════════════════════════════════════════════════════════════════╝

💡 Type commands or natural language requests
🎯 Examples: 'ciao', 'models', 'system info', 'help'

"""
        self.add_output(welcome, "header")
    
    def add_output(self, text: str, tag: str = "info"):
        """Add output to terminal"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, text, tag)
        self.output_text.see(tk.END)
        self.output_text.config(state=tk.DISABLED)
    
    def on_enter(self, event):
        """Handle enter key"""
        command = self.input_entry.get().strip()
        if command:
            self.command_history.append(command)
            self.history_index = len(self.command_history)
            
            self.add_output(f"cybex❯ {command}\n", "system")
            self.input_entry.delete(0, tk.END)
            
            # Process command in thread
            threading.Thread(target=self.process_command, args=(command,), daemon=True).start()
    
    def on_history_up(self, event):
        """Handle up arrow - previous command"""
        if self.command_history and self.history_index > 0:
            self.history_index -= 1
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, self.command_history[self.history_index])
    
    def on_history_down(self, event):
        """Handle down arrow - next command"""
        if self.command_history and self.history_index < len(self.command_history) - 1:
            self.history_index += 1
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, self.command_history[self.history_index])
        elif self.history_index >= len(self.command_history) - 1:
            self.input_entry.delete(0, tk.END)
            self.history_index = len(self.command_history)
    
    def process_command(self, command: str):
        """Process command"""
        try:
            # Special commands
            if command.lower() in ['clear', 'cls']:
                self.after(0, self.clear_terminal)
                return
            
            if command.lower() in ['help', '?']:
                self.after(0, self.show_help)
                return
            
            if command.lower() in ['models', 'lista modelli']:
                self.after(0, self.show_models)
                return

            if command.lower() in ['reload prompt', 'reload-prompt', 'refresh prompt', 'ricarica prompt']:
                self.after(0, self._reload_system_prompt)
                return

            # System commands
            if self._is_system_command(command):
                self._execute_system_command(command)
            else:
                # AI processing
                self._process_with_ai(command)
                
        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ Error: {e}\n", "error"))
    
    def _is_system_command(self, command: str) -> bool:
        """Check if command is a system command"""
        system_commands = ['dir', 'ls', 'pwd', 'echo', 'systeminfo', 'tasklist', 'ping']
        return any(command.lower().startswith(cmd) for cmd in system_commands)
    
    def _execute_system_command(self, command: str):
        """Execute system command"""
        try:
            if self.gui_parent and self.gui_parent.agent_tools:
                execution_id = self.gui_parent.agent_tools.execute_tool('execute_command', {'command': command})
                
                if execution_id:
                    # Wait for result
                    import time
                    for _ in range(60):  # Wait up to 30 seconds
                        execution = self.gui_parent.agent_tools.get_execution_status(execution_id)
                        if execution and execution.status.value in ['completed', 'failed']:
                            break
                        time.sleep(0.5)
                    
                    execution = self.gui_parent.agent_tools.get_execution_status(execution_id)
                    if execution and execution.result:
                        if execution.result.success:
                            self.after(0, lambda: self.add_output(execution.result.output + "\n", "info"))
                        else:
                            self.after(0, lambda: self.add_output(f"❌ {execution.result.error}\n", "error"))
                    else:
                        self.after(0, lambda: self.add_output("❌ Command execution failed\n", "error"))
                else:
                    self.after(0, lambda: self.add_output("❌ Could not execute command\n", "error"))
            else:
                self.after(0, lambda: self.add_output("❌ Agent tools not available\n", "error"))
                
        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ System command error: {e}\n", "error"))
    
    def _process_with_ai(self, command: str):
        """Process command with AI"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.after(0, lambda: self.add_output("❌ AI interface not available. Check Ollama server.\n", "error"))
                return

            # Start monitoring if available
            if hasattr(self.gui_parent, 'ollama_monitor') and self.gui_parent.ollama_monitor:
                current_model = self.gui_parent.ollama_interface.model
                self.gui_parent.ollama_monitor.start_activity(current_model, command)

            # Generate AI response
            response = self.gui_parent.ollama_interface.generate_response(
                f"L'utente ha scritto: '{command}'. Rispondi in italiano in modo conciso e utile.",
                {'mode': 'terminal', 'user_input': command}
            )

            # End monitoring
            if hasattr(self.gui_parent, 'ollama_monitor') and self.gui_parent.ollama_monitor:
                if response.success:
                    self.gui_parent.ollama_monitor.end_activity(success=True)
                else:
                    self.gui_parent.ollama_monitor.end_activity(success=False, error=response.error)

            if response.success:
                self.after(0, lambda: self.add_output(f"🤖 {response.content}\n\n", "ai"))
            else:
                error_msg = response.error or "Unknown error"
                self.after(0, lambda: self.add_output(f"❌ AI Error: {error_msg}\n", "error"))

        except Exception as e:
            # End monitoring on error
            if hasattr(self.gui_parent, 'ollama_monitor') and self.gui_parent.ollama_monitor:
                self.gui_parent.ollama_monitor.end_activity(success=False, error=str(e))
            self.after(0, lambda: self.add_output(f"❌ AI processing error: {e}\n", "error"))
    
    def clear_terminal(self):
        """Clear terminal"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
        self.setup_welcome()
    
    def show_help(self):
        """Show help"""
        help_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              🚀 CYBEX HELP                                  ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║ 🎯 NATURAL LANGUAGE COMMANDS:                                               ║
║   • "ciao"                        - Greet the AI                            ║
║   • "analizza sistema"            - System analysis                         ║
║   • "stato ollama"                - Check Ollama status                     ║
║                                                                              ║
║ 🖥️ SYSTEM COMMANDS:                                                          ║
║   • systeminfo                    - System information                      ║
║   • tasklist                      - Running processes                       ║
║   • dir                           - List directory                          ║
║                                                                              ║
║ 🛠️ TERMINAL COMMANDS:                                                        ║
║   • clear, cls                    - Clear terminal                          ║
║   • help, ?                       - Show this help                          ║
║   • models                        - Show available Ollama models            ║
║   • reload prompt                 - Reload system prompt from file          ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""
        self.add_output(help_text, "info")
    
    def show_models(self):
        """Show available models"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return
            
            result = self.gui_parent.ollama_interface.get_available_models()
            
            if result['success']:
                models_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                           🤖 AVAILABLE OLLAMA MODELS                        ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
"""
                
                current_model = result['current_model']
                
                for i, model in enumerate(result['models'], 1):
                    name = model['name']
                    size_gb = model['size_gb']
                    current_marker = " ← CURRENT" if name == current_model else ""
                    
                    models_text += f"║ {i:2d}. {name:<25} ({size_gb:>5.1f} GB){current_marker:<10} ║\n"
                
                models_text += f"""║                                                                              ║
║ Total Models: {result['count']:<3}                                                     ║
║ Current Model: {current_model:<25}                              ║
║                                                                              ║
║ 💡 To change model: Use the dropdown in the AI panel                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""
                self.add_output(models_text, "info")
            else:
                self.add_output(f"❌ Failed to get models: {result['error']}\n", "error")
                
        except Exception as e:
            self.add_output(f"❌ Error showing models: {e}\n", "error")

    def _reload_system_prompt(self):
        """Reload system prompt from file"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            # Reload the prompt
            success = self.gui_parent.ollama_interface.reload_system_prompt()

            if success:
                self.add_output("✅ System prompt reloaded successfully\n", "success")

                # Show preview of the prompt
                prompt_preview = self.gui_parent.ollama_interface.get_system_prompt()[:200]
                self.add_output(f"📋 Prompt preview: {prompt_preview}...\n", "info")
            else:
                self.add_output("⚠️ System prompt unchanged or failed to reload\n", "warning")

        except Exception as e:
            self.add_output(f"❌ Error reloading prompt: {e}\n", "error")
    
    def start_output_monitor(self):
        """Start output monitoring"""
        def monitor():
            while True:
                try:
                    if not self.output_queue.empty():
                        text, tag = self.output_queue.get_nowait()
                        self.after(0, lambda t=text, tg=tag: self.add_output(t, tg))
                except:
                    pass
                time.sleep(0.1)
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

class CybexFuturisticGUI:
    """Main Cybex Enterprise GUI"""

    def __init__(self):
        self.root = tk.Tk()
        self.core = None
        self.ollama_interface = None
        self.agent_tools = None

        self.setup_window()
        self.setup_theme()
        self.initialize_core()
        self.setup_ui()
        self.setup_bindings()

        # Start status monitoring
        self.start_status_monitor()

    def setup_window(self):
        """Setup main window"""
        self.root.title("CYBEX ENTERPRISE - AI Assistant")
        self.root.geometry("1400x900")
        self.root.configure(bg=FuturisticTheme.BG_DARK)
        self.root.minsize(1200, 700)

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1400x900+{x}+{y}")

    def setup_theme(self):
        """Setup GUI theme"""
        style = ttk.Style()
        style.theme_use('clam')

        # Configure styles
        style.configure('Futuristic.TFrame', background=FuturisticTheme.BG_PANEL)
        style.configure('Futuristic.TLabel', background=FuturisticTheme.BG_PANEL, foreground=FuturisticTheme.TEXT_PRIMARY)
        style.configure('Futuristic.TButton', background=FuturisticTheme.BG_ACCENT, foreground=FuturisticTheme.TEXT_PRIMARY)
        style.configure('Futuristic.TCombobox', fieldbackground=FuturisticTheme.BG_ACCENT, foreground=FuturisticTheme.TEXT_PRIMARY)

    def initialize_core(self):
        """Initialize Cybex core systems"""
        try:
            # Initialize core
            self.core = CybexCore()

            # Initialize Ollama interface
            self.ollama_interface = OllamaInterface(
                self.core.config_manager,
                self.core.log_manager
            )

            # Initialize agent tools
            self.agent_tools = AgentTools(self.core.log_manager)

            print("✅ Cybex core systems initialized successfully")

        except Exception as e:
            print(f"❌ Failed to initialize core systems: {e}")
            messagebox.showerror("Initialization Error", f"Failed to initialize Cybex core: {e}")

    def setup_ui(self):
        """Setup main UI"""
        # Main container
        main_frame = tk.Frame(self.root, bg=FuturisticTheme.BG_DARK)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # Header
        self.setup_header(main_frame)

        # Content area
        content_frame = tk.Frame(main_frame, bg=FuturisticTheme.BG_DARK)
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # Left panel (Terminal)
        left_panel = tk.Frame(content_frame, bg=FuturisticTheme.BG_PANEL, relief=tk.RAISED, bd=1)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # Terminal
        self.terminal = FuturisticTerminal(left_panel)
        self.terminal.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.terminal.gui_parent = self  # Set reference

        # Right panel (Controls)
        right_panel = tk.Frame(content_frame, bg=FuturisticTheme.BG_PANEL, relief=tk.RAISED, bd=1, width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_panel.pack_propagate(False)

        self.setup_right_panel(right_panel)

        # Status bar
        self.setup_status_bar(main_frame)

    def setup_header(self, parent):
        """Setup header with logo"""
        header_frame = tk.Frame(parent, bg=FuturisticTheme.BG_DARK, height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Logo
        logo_text = """
    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗███╗   ██╗████████╗
   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝████╗  ██║╚══██╔══╝
   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██╔██╗ ██║   ██║
   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║╚██╗██║   ██║
   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ███████╗██║ ╚████║   ██║
    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝   ╚═╝
"""

        logo_label = tk.Label(
            header_frame,
            text=logo_text,
            bg=FuturisticTheme.BG_DARK,
            fg=FuturisticTheme.NEON_CYAN,
            font=("Consolas", 8, "bold"),
            justify=tk.CENTER
        )
        logo_label.pack(expand=True)

    def setup_right_panel(self, parent):
        """Setup right control panel"""
        # AI Control Panel
        ai_frame = tk.LabelFrame(
            parent,
            text="🤖 AI CONTROL",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_PURPLE,
            font=("Consolas", 10, "bold"),
            relief=tk.RAISED,
            bd=1
        )
        ai_frame.pack(fill=tk.X, padx=5, pady=5)

        # Model selection
        tk.Label(
            ai_frame,
            text="Model:",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        ).pack(anchor=tk.W, padx=5, pady=2)

        self.model_var = tk.StringVar(value="gemma3:4b")

        # Get available models dynamically
        available_models = self.get_available_models()

        self.model_combo = ttk.Combobox(
            ai_frame,
            textvariable=self.model_var,
            values=available_models,
            state="readonly",
            font=("Consolas", 9),
            style="Futuristic.TCombobox"
        )
        self.model_combo.pack(fill=tk.X, padx=5, pady=2)
        self.model_combo.bind("<<ComboboxSelected>>", self.on_model_changed)

        # AI Status
        self.ai_status_label = tk.Label(
            ai_frame,
            text="🔄 Checking...",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_SECONDARY,
            font=("Consolas", 8)
        )
        self.ai_status_label.pack(anchor=tk.W, padx=5, pady=2)

        # Ollama Monitor
        if OllamaMonitor:
            try:
                self.ollama_monitor = OllamaMonitor(parent, self.core.log_manager if self.core else None)
                self.logger.info("Ollama monitor initialized")
            except Exception as e:
                print(f"⚠️ Could not initialize Ollama monitor: {e}")
                self.ollama_monitor = None
        else:
            self.ollama_monitor = None

        # Quick Actions
        actions_frame = tk.LabelFrame(
            parent,
            text="⚡ QUICK ACTIONS",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_GREEN,
            font=("Consolas", 10, "bold"),
            relief=tk.RAISED,
            bd=1
        )
        actions_frame.pack(fill=tk.X, padx=5, pady=5)

        # Action buttons
        actions = [
            ("🖥️ System Info", self.action_system_info),
            ("🔄 Process List", self.action_process_list),
            ("🤖 Ollama Status", self.action_ollama_status),
            ("📊 Models List", self.action_models_list),
            ("🧹 Clear Terminal", self.action_clear_terminal),
            ("❓ Help", self.action_help)
        ]

        for text, command in actions:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=command,
                bg=FuturisticTheme.BG_ACCENT,
                fg=FuturisticTheme.TEXT_PRIMARY,
                font=("Consolas", 9),
                relief=tk.FLAT,
                bd=1,
                activebackground=FuturisticTheme.NEON_CYAN,
                activeforeground=FuturisticTheme.BG_DARK
            )
            btn.pack(fill=tk.X, padx=5, pady=2)

    def get_available_models(self) -> List[str]:
        """Get list of available models from Ollama"""
        try:
            if self.ollama_interface:
                result = self.ollama_interface.get_available_models()
                if result['success']:
                    models = [model['name'] for model in result['models']]
                    if models:
                        return models

            # Fallback to default models
            return ["gemma3:4b", "gemma3:27b", "deepseek-r1:8b", "deepseek-r1:latest"]

        except Exception as e:
            print(f"Failed to get available models: {e}")
            return ["gemma3:4b", "gemma3:27b", "deepseek-r1:8b"]

    def on_model_changed(self, event=None):
        """Handle model selection change"""
        try:
            selected_model = self.model_var.get()
            if self.ollama_interface and selected_model:
                # Start monitoring model switch
                if hasattr(self, 'ollama_monitor') and self.ollama_monitor:
                    self.ollama_monitor.start_activity(selected_model, f"Switching to model: {selected_model}")

                # Update the model in Ollama interface
                success = self.ollama_interface.switch_model(selected_model)

                # End monitoring
                if hasattr(self, 'ollama_monitor') and self.ollama_monitor:
                    if success:
                        self.ollama_monitor.end_activity(success=True)
                    else:
                        self.ollama_monitor.end_activity(success=False, error="Model switch failed")

                if success:
                    self.terminal.add_output(f"🤖 Model switched to: {selected_model}\n", "success")
                    self.update_ai_status()
                else:
                    self.terminal.add_output(f"❌ Failed to switch to model: {selected_model}\n", "error")

        except Exception as e:
            # End monitoring on error
            if hasattr(self, 'ollama_monitor') and self.ollama_monitor:
                self.ollama_monitor.end_activity(success=False, error=str(e))
            self.terminal.add_output(f"❌ Failed to change model: {e}\n", "error")

    def setup_status_bar(self, parent):
        """Setup status bar"""
        status_frame = tk.Frame(parent, bg=FuturisticTheme.BG_ACCENT, height=25)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="🚀 CYBEX Enterprise Ready",
            bg=FuturisticTheme.BG_ACCENT,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)

        # Version info
        version_label = tk.Label(
            status_frame,
            text="v2.0.0",
            bg=FuturisticTheme.BG_ACCENT,
            fg=FuturisticTheme.TEXT_SECONDARY,
            font=("Consolas", 8)
        )
        version_label.pack(side=tk.RIGHT, padx=10, pady=2)

    def setup_bindings(self):
        """Setup key bindings"""
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<F1>', lambda e: self.action_help())
        self.root.bind('<F5>', lambda e: self.refresh_models())

    def start_status_monitor(self):
        """Start status monitoring"""
        def monitor():
            while True:
                try:
                    self.root.after_idle(self.update_ai_status)
                    time.sleep(30)  # Update every 30 seconds
                except:
                    break

        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

        # Initial status update
        self.root.after(1000, self.update_ai_status)

    def update_ai_status(self):
        """Update AI status"""
        try:
            if self.ollama_interface and self.ollama_interface.is_server_available():
                current_model = self.ollama_interface.model
                self.ai_status_label.config(
                    text=f"✅ Online - {current_model}",
                    fg=FuturisticTheme.SUCCESS
                )
            else:
                self.ai_status_label.config(
                    text="❌ Offline",
                    fg=FuturisticTheme.ERROR
                )
        except Exception as e:
            self.ai_status_label.config(
                text="⚠️ Error",
                fg=FuturisticTheme.WARNING
            )

    def refresh_models(self):
        """Refresh available models"""
        try:
            models = self.get_available_models()
            self.model_combo['values'] = models
            self.terminal.add_output("🔄 Models list refreshed\n", "info")
        except Exception as e:
            self.terminal.add_output(f"❌ Failed to refresh models: {e}\n", "error")

    # Quick action methods
    def action_system_info(self):
        """System info action"""
        self.terminal.process_command("systeminfo")

    def action_process_list(self):
        """Process list action"""
        self.terminal.process_command("tasklist")

    def action_ollama_status(self):
        """Ollama status action"""
        self.terminal.process_command("stato ollama")

    def action_models_list(self):
        """Models list action"""
        self.terminal.process_command("models")

    def action_clear_terminal(self):
        """Clear terminal action"""
        self.terminal.clear_terminal()

    def action_help(self):
        """Help action"""
        self.terminal.show_help()

    def run(self):
        """Run the GUI"""
        try:
            print("🚀 Starting Cybex Futuristic GUI...")
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 Cybex GUI interrupted by user")
        except Exception as e:
            print(f"❌ GUI error: {e}")
        finally:
            if self.core:
                self.core.shutdown()

    def after_idle(self, func):
        """Schedule function to run after idle"""
        self.root.after_idle(func)

def main():
    """Main entry point"""
    try:
        app = CybexFuturisticGUI()
        app.run()
    except Exception as e:
        print(f"❌ Failed to start Cybex GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
