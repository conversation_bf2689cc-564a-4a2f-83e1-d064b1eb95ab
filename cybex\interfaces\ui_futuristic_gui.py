#!/usr/bin/env python3
"""
Cybex Futuristic GUI
Interfaccia grafica futuristica con integrazione CMD
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import subprocess
import threading
import time
import os
import sys
import json
import platform
from pathlib import Path
from typing import Dict, List, Optional, Any
import queue
from PIL import Image, ImageTk

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from cybex.core.cybex_core import CybexCore
    from cybex.modules.ollama_interface import OllamaInterface
    from cybex.modules.agent_tools import AgentTools
    from cybex.modules.operation_monitor import init_global_monitor
except ImportError as e:
    print(f"Warning: Could not import Cybex modules: {e}")

class FuturisticTheme:
    """Tema futuristico cyberpunk"""
    
    # Colori principali
    BG_DARK = "#0a0a0a"           # Nero profondo
    BG_PANEL = "#1a1a2e"         # Blu scuro
    BG_ACCENT = "#16213e"        # Blu medio
    
    # Colori neon
    NEON_CYAN = "#00ffff"        # Ciano brillante
    NEON_PURPLE = "#bf00ff"      # Viola neon
    NEON_GREEN = "#00ff41"       # Verde neon
    NEON_ORANGE = "#ff6600"      # Arancione neon
    NEON_PINK = "#ff0080"        # Rosa neon
    
    # Colori testo
    TEXT_PRIMARY = "#ffffff"      # Bianco
    TEXT_SECONDARY = "#b0b0b0"    # Grigio chiaro
    TEXT_MUTED = "#666666"        # Grigio scuro
    
    # Colori stato
    SUCCESS = "#00ff41"
    WARNING = "#ffaa00"
    ERROR = "#ff3366"
    INFO = "#00aaff"
    
    # Effetti
    GLOW_CYAN = "#00ffff"
    GLOW_PURPLE = "#bf00ff"
    SHADOW = "#000000"

class AnimatedLabel(tk.Label):
    """Label con effetti di animazione"""
    
    def __init__(self, parent, text="", **kwargs):
        super().__init__(parent, text=text, **kwargs)
        self.original_text = text
        self.is_typing = False
        
    def typewriter_effect(self, text: str, delay: float = 0.05):
        """Effetto macchina da scrivere"""
        if self.is_typing:
            return
            
        self.is_typing = True
        self.config(text="")
        
        def type_char(index=0):
            if index < len(text):
                current_text = text[:index + 1]
                self.config(text=current_text)
                self.after(int(delay * 1000), lambda: type_char(index + 1))
            else:
                self.is_typing = False
        
        type_char()
    
    def pulse_effect(self, color1: str, color2: str, duration: int = 1000):
        """Effetto pulsante"""
        def pulse(step=0):
            if step < 20:
                # Interpola tra i due colori
                ratio = (step % 10) / 10.0 if step < 10 else (20 - step) / 10.0
                # Semplice alternanza per ora
                current_color = color1 if step < 10 else color2
                self.config(fg=current_color)
                self.after(duration // 20, lambda: pulse(step + 1))
            else:
                self.after(duration, lambda: pulse(0))
        
        pulse()

class FuturisticTerminal(tk.Frame):
    """Terminale futuristico integrato"""
    
    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=FuturisticTheme.BG_DARK, **kwargs)

        self.command_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.current_process = None
        self.gui_parent = None  # Riferimento alla GUI principale

        self.setup_ui()
        self.start_output_monitor()

    def set_gui_parent(self, gui_parent):
        """Imposta riferimento alla GUI principale"""
        self.gui_parent = gui_parent
    
    def setup_ui(self):
        """Setup interfaccia terminale"""
        
        # Header terminale
        header_frame = tk.Frame(self, bg=FuturisticTheme.BG_PANEL, height=40)
        header_frame.pack(fill=tk.X, padx=2, pady=2)
        header_frame.pack_propagate(False)
        
        # Titolo con effetto neon
        title_label = AnimatedLabel(
            header_frame,
            text="◉ CYBEX TERMINAL ◉",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_CYAN,
            font=("Consolas", 12, "bold")
        )
        title_label.pack(side=tk.LEFT, padx=10, pady=8)
        
        # Status indicator
        self.status_label = tk.Label(
            header_frame,
            text="● READY",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_GREEN,
            font=("Consolas", 10, "bold")
        )
        self.status_label.pack(side=tk.RIGHT, padx=10, pady=8)
        
        # Area output
        output_frame = tk.Frame(self, bg=FuturisticTheme.BG_DARK)
        output_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # ScrolledText per output
        self.output_text = scrolledtext.ScrolledText(
            output_frame,
            bg=FuturisticTheme.BG_DARK,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 10),
            insertbackground=FuturisticTheme.NEON_CYAN,
            selectbackground=FuturisticTheme.BG_ACCENT,
            selectforeground=FuturisticTheme.TEXT_PRIMARY,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.output_text.pack(fill=tk.BOTH, expand=True)
        
        # Configura tag per colori
        self.setup_text_tags()
        
        # Input frame
        input_frame = tk.Frame(self, bg=FuturisticTheme.BG_PANEL, height=50)
        input_frame.pack(fill=tk.X, padx=2, pady=2)
        input_frame.pack_propagate(False)
        
        # Prompt
        prompt_label = tk.Label(
            input_frame,
            text="cybex❯",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_PURPLE,
            font=("Consolas", 12, "bold")
        )
        prompt_label.pack(side=tk.LEFT, padx=10, pady=10)
        
        # Input entry
        self.input_entry = tk.Entry(
            input_frame,
            bg=FuturisticTheme.BG_DARK,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 12),
            insertbackground=FuturisticTheme.NEON_CYAN,
            selectbackground=FuturisticTheme.BG_ACCENT,
            selectforeground=FuturisticTheme.TEXT_PRIMARY,
            relief=tk.FLAT,
            bd=1
        )
        self.input_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=5, pady=10)
        self.input_entry.bind('<Return>', self.on_command_enter)
        self.input_entry.bind('<Up>', self.on_history_up)
        self.input_entry.bind('<Down>', self.on_history_down)
        
        # Command history
        self.command_history = []
        self.history_index = -1
        
        # Welcome message
        self.add_output("╔══════════════════════════════════════════════════════════════════════════════╗\n", "header")
        self.add_output("║                    🚀 CYBEX FUTURISTIC TERMINAL                             ║\n", "header")
        self.add_output("║                      Advanced AI Command Interface                          ║\n", "header")
        self.add_output("╚══════════════════════════════════════════════════════════════════════════════╝\n", "header")
        self.add_output("\n💡 Type commands or natural language requests\n", "info")
        self.add_output("🎯 Examples: 'scansiona disco C', 'systeminfo', 'help'\n\n", "info")
    
    def setup_text_tags(self):
        """Setup tag per colorazione testo"""
        tags = {
            "header": {"foreground": FuturisticTheme.NEON_CYAN, "font": ("Consolas", 10, "bold")},
            "success": {"foreground": FuturisticTheme.SUCCESS},
            "error": {"foreground": FuturisticTheme.ERROR},
            "warning": {"foreground": FuturisticTheme.WARNING},
            "info": {"foreground": FuturisticTheme.INFO},
            "command": {"foreground": FuturisticTheme.NEON_PURPLE, "font": ("Consolas", 10, "bold")},
            "output": {"foreground": FuturisticTheme.TEXT_PRIMARY},
            "muted": {"foreground": FuturisticTheme.TEXT_MUTED}
        }
        
        for tag, config in tags.items():
            self.output_text.tag_configure(tag, **config)
    
    def add_output(self, text: str, tag: str = "output"):
        """Aggiungi testo all'output"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, text, tag)
        self.output_text.config(state=tk.DISABLED)
        self.output_text.see(tk.END)
    
    def on_command_enter(self, event):
        """Gestisci invio comando"""
        command = self.input_entry.get().strip()
        if not command:
            return
        
        # Aggiungi alla cronologia
        self.command_history.append(command)
        self.history_index = len(self.command_history)
        
        # Mostra comando
        self.add_output(f"cybex❯ {command}\n", "command")
        
        # Pulisci input
        self.input_entry.delete(0, tk.END)
        
        # Esegui comando
        self.execute_command(command)
    
    def on_history_up(self, event):
        """Naviga cronologia su"""
        if self.command_history and self.history_index > 0:
            self.history_index -= 1
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, self.command_history[self.history_index])
    
    def on_history_down(self, event):
        """Naviga cronologia giù"""
        if self.command_history and self.history_index < len(self.command_history) - 1:
            self.history_index += 1
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, self.command_history[self.history_index])
        elif self.history_index == len(self.command_history) - 1:
            self.history_index = len(self.command_history)
            self.input_entry.delete(0, tk.END)
    
    def execute_command(self, command: str):
        """Esegui comando"""
        self.status_label.config(text="● PROCESSING", fg=FuturisticTheme.WARNING)
        
        # Esegui in thread separato
        thread = threading.Thread(target=self._execute_command_thread, args=(command,))
        thread.daemon = True
        thread.start()
    
    def _execute_command_thread(self, command: str):
        """Esegui comando in thread separato"""
        try:
            # Comandi speciali
            if command.lower() in ['clear', 'cls']:
                self.after(0, self.clear_terminal)
                return

            if command.lower() in ['help', '?']:
                self.after(0, lambda: self.show_help())
                return

            if command.lower() in ['models', 'lista modelli', 'show models']:
                self.after(0, lambda: self.show_models())
                return

            # Determina se è comando sistema o AI
            if self._is_system_command(command):
                # Esegui come comando sistema
                self._execute_system_command_thread(command)
            else:
                # Processa con AI
                self._execute_ai_command_thread(command)

        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ Command execution error: {str(e)}\n", "error"))
            self.after(0, lambda: self.status_label.config(text="● ERROR", fg=FuturisticTheme.ERROR))

        # Reset status dopo 2 secondi
        self.after(2000, lambda: self.status_label.config(text="● READY", fg=FuturisticTheme.NEON_GREEN))

    def _execute_system_command_thread(self, command: str):
        """Esegui comando sistema"""
        try:
            # Esegui comando sistema con encoding corretto
            process = subprocess.Popen(
                command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',  # Sostituisce caratteri non decodificabili
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            stdout, stderr = process.communicate(timeout=30)

            # Mostra output
            if stdout:
                self.after(0, lambda: self.add_output(stdout + "\n", "output"))

            if stderr:
                self.after(0, lambda: self.add_output(stderr + "\n", "error"))

            if process.returncode == 0:
                self.after(0, lambda: self.status_label.config(text="● SUCCESS", fg=FuturisticTheme.SUCCESS))
            else:
                self.after(0, lambda: self.status_label.config(text="● ERROR", fg=FuturisticTheme.ERROR))

        except subprocess.TimeoutExpired:
            self.after(0, lambda: self.add_output("⚠️ Command timed out\n", "warning"))
            self.after(0, lambda: self.status_label.config(text="● TIMEOUT", fg=FuturisticTheme.WARNING))

        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ System command error: {str(e)}\n", "error"))
            self.after(0, lambda: self.status_label.config(text="● ERROR", fg=FuturisticTheme.ERROR))

    def _execute_ai_command_thread(self, command: str):
        """Esegui comando AI"""
        try:
            # Processa con AI
            result = self._process_with_ai(command)

            # Mostra output
            self.after(0, lambda: self.add_output(result['output'] + "\n", "success" if result['success'] else "error"))

            # Mostra suggerimento se disponibile
            if result.get('suggestion'):
                suggestion_text = f"💡 {result['suggestion']}\n"
                self.after(0, lambda: self.add_output(suggestion_text, "info"))

            # Aggiorna status
            if result['success']:
                self.after(0, lambda: self.status_label.config(text="● AI SUCCESS", fg=FuturisticTheme.SUCCESS))
            else:
                self.after(0, lambda: self.status_label.config(text="● AI ERROR", fg=FuturisticTheme.ERROR))

        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ AI command error: {str(e)}\n", "error"))
            self.after(0, lambda: self.status_label.config(text="● AI ERROR", fg=FuturisticTheme.ERROR))
        
    def _process_with_ai(self, command: str) -> Dict[str, Any]:
        """Process command with AI - REAL IMPLEMENTATION"""
        try:
            # Usa il riferimento alla GUI principale
            if not self.gui_parent or not hasattr(self.gui_parent, 'ollama_interface') or not self.gui_parent.ollama_interface:
                return {
                    'success': False,
                    'output': "❌ AI interface not available. Check Ollama server.\n💡 Try system commands instead."
                }

            # Prepara contesto per AI
            context = {
                'mode': 'futuristic_gui',
                'interface_type': 'gui_terminal',
                'current_directory': os.getcwd(),
                'system_type': platform.system(),
                'available_tools': [
                    'scan_disk', 'cleanup_temp_files', 'execute_command',
                    'list_processes', 'get_system_info', 'network_scan'
                ],
                'user_request': command
            }

            # Crea prompt specifico per GUI
            ai_prompt = f"""
Sei un assistente AI integrato in un terminale futuristico. L'utente ha scritto: "{command}"

Contesto:
- Sistema: {platform.system()}
- Directory: {os.getcwd()}
- Interfaccia: GUI Terminal futuristico
- Tool disponibili: scan_disk, cleanup_temp_files, execute_command, list_processes, get_system_info, network_scan

Se l'utente chiede di:
- "scansiona disco C" o simili → Usa tool scan_disk
- "elimina file temp" o simili → Usa tool cleanup_temp_files
- "mostra processi" o simili → Usa tool list_processes
- "stato sistema" o simili → Usa tool get_system_info
- "connessioni rete" o simili → Usa tool network_scan

Rispondi in italiano, sii conciso ma informativo. Se devi usare un tool, spiega cosa farai prima di farlo.
"""

            # Try enhanced processing first
            ollama_interface = self.gui_parent.ollama_interface
            if hasattr(ollama_interface, 'process_enhanced_command'):
                try:
                    # Use enhanced processing with context
                    response = ollama_interface.process_enhanced_command(command, context)

                    if response and response.success:
                        # Execute AI tools if available
                        ai_output = response.content
                        if self.gui_parent.agent_tools:
                            ai_output = self._execute_ai_tools(ai_output, self.gui_parent.agent_tools, command)

                        return {
                            'success': True,
                            'output': ai_output,
                            'enhanced': True,
                            'execution_time': getattr(response, 'execution_time', None),
                            'risk_level': getattr(response, 'risk_level', 'UNKNOWN'),
                            'suggestion': self._generate_smart_suggestion(command, ai_output)
                        }
                    else:
                        # Fallback to standard processing
                        return self._fallback_ai_processing(command, context, ollama_interface)

                except Exception as e:
                    # Fallback to standard processing on enhanced failure
                    return self._fallback_ai_processing(command, context, ollama_interface)
            else:
                # Standard processing if enhanced not available
                return self._fallback_ai_processing(command, context, ollama_interface)

        except Exception as e:
            return {
                'success': False,
                'output': f"❌ AI processing error: {str(e)}\n💡 Check Ollama server status with: curl http://localhost:11434/api/tags"
            }

    def _fallback_ai_processing(self, command: str, context: Dict[str, Any], ollama_interface) -> Dict[str, Any]:
        """Fallback to standard AI processing"""
        try:
            # Create enhanced prompt for standard processing
            ai_prompt = f"""
Sei un assistente AI integrato in un terminale futuristico. L'utente ha scritto: "{command}"

Contesto:
- Sistema: {context.get('system_type', 'Unknown')}
- Directory: {context.get('current_directory', 'Unknown')}
- Interfaccia: GUI Terminal futuristico
- Tool disponibili: {', '.join(context.get('available_tools', []))}

Se l'utente chiede di:
- "scansiona disco C" o simili → Usa tool scan_disk
- "elimina file temp" o simili → Usa tool cleanup_temp_files
- "mostra processi" o simili → Usa tool list_processes
- "stato sistema" o simili → Usa tool get_system_info
- "connessioni rete" o simili → Usa tool network_scan

Rispondi in italiano, sii conciso ma informativo. Se devi usare un tool, spiega cosa farai prima di farlo.
"""

            # Generate response using standard Ollama
            response = ollama_interface.generate_response(ai_prompt, context)

            if response and response.success:
                # Execute AI tools if available
                ai_output = response.content
                if hasattr(self.gui_parent, 'agent_tools') and self.gui_parent.agent_tools:
                    ai_output = self._execute_ai_tools(ai_output, self.gui_parent.agent_tools, command)

                return {
                    'success': True,
                    'output': ai_output,
                    'enhanced': False,
                    'suggestion': self._generate_smart_suggestion(command, ai_output)
                }
            else:
                error_msg = response.error if response else "Unknown AI error"
                return {
                    'success': False,
                    'output': f"❌ AI Error: {error_msg}\n💡 Try a system command like 'systeminfo' or 'dir'."
                }

        except Exception as e:
            return {
                'success': False,
                'output': f"❌ Fallback AI processing error: {str(e)}"
            }

    def _execute_ai_tools(self, ai_response: str, agent_tools, original_command: str) -> str:
        """Esegui tool AI se richiesto nella risposta"""
        try:
            # Mappa comandi a tool
            tool_mapping = {
                'scansiona disco': 'scan_disk',
                'scan disco': 'scan_disk',
                'analizza disco': 'scan_disk',
                'elimina file temp': 'cleanup_temp_files',
                'pulisci temp': 'cleanup_temp_files',
                'cleanup': 'cleanup_temp_files',
                'mostra processi': 'list_processes',
                'lista processi': 'list_processes',
                'processi': 'list_processes',
                'stato sistema': 'get_system_info',
                'info sistema': 'get_system_info',
                'system info': 'get_system_info',
                'connessioni rete': 'network_scan',
                'rete': 'network_scan',
                'network': 'network_scan'
            }

            # Trova tool da eseguire
            tool_to_execute = None
            tool_params = {}

            original_lower = original_command.lower()
            for keyword, tool_name in tool_mapping.items():
                if keyword in original_lower:
                    tool_to_execute = tool_name

                    # Parametri specifici per tool
                    if tool_name == 'scan_disk':
                        if 'disco c' in original_lower or 'drive c' in original_lower:
                            tool_params = {'drive': 'C'}
                        else:
                            tool_params = {'drive': 'C'}  # Default
                    elif tool_name == 'cleanup_temp_files':
                        tool_params = {'preview': True, 'drive': 'C'}

                    break

            if tool_to_execute:
                # Esegui tool
                self.after(0, lambda: self.add_output(f"⚡ Executing {tool_to_execute}...\n", "info"))

                execution_id = agent_tools.execute_tool(tool_to_execute, tool_params)

                if execution_id:
                    # Attendi risultato (max 30 secondi)
                    import time
                    max_wait = 30
                    wait_time = 0

                    while wait_time < max_wait:
                        execution = agent_tools.get_execution_status(execution_id)
                        if execution and execution.status.value in ['completed', 'failed']:
                            break
                        time.sleep(0.5)
                        wait_time += 0.5

                    execution = agent_tools.get_execution_status(execution_id)
                    if execution and execution.result:
                        if execution.result.success:
                            tool_output = execution.result.output or "Tool executed successfully"
                            return f"{ai_response}\n\n⚡ TOOL EXECUTION RESULT:\n{tool_output}"
                        else:
                            error_output = execution.result.error or "Tool execution failed"
                            return f"{ai_response}\n\n❌ TOOL ERROR:\n{error_output}"
                    else:
                        return f"{ai_response}\n\n⚠️ Tool execution timed out or failed"
                else:
                    return f"{ai_response}\n\n❌ Failed to start tool execution"

            return ai_response

        except Exception as e:
            return f"{ai_response}\n\n❌ Tool execution error: {str(e)}"

    def _get_available_models(self) -> List[str]:
        """Get list of available models from Ollama"""
        try:
            if self.ollama_interface:
                result = self.ollama_interface.get_available_models()
                if result['success']:
                    return [model['name'] for model in result['models']]

            # Fallback to default models if Ollama not available
            return ["gemma3:4b", "gemma3:27b", "deepseek-r1:8b", "deepseek-r1:latest"]

        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return ["gemma3:4b", "gemma3:27b", "deepseek-r1:8b"]

    def _on_model_changed(self, event=None):
        """Handle model selection change"""
        try:
            selected_model = self.model_var.get()
            if self.ollama_interface and selected_model:
                # Update the model in Ollama interface
                self.ollama_interface.model = selected_model

                # Log the change
                self.logger.info(f"Model changed to: {selected_model}")

                # Update terminal with confirmation
                self._add_to_terminal(f"🤖 Model switched to: {selected_model}")

        except Exception as e:
            self.logger.error(f"Failed to change model: {e}")
            self._add_to_terminal(f"❌ Failed to change model: {e}")

    def _generate_suggestion(self, command: str, response: str) -> str:
        """Generate AI suggestion for next command (legacy)"""
        return self._generate_smart_suggestion(command, response)

    def _generate_smart_suggestion(self, command: str, response: str) -> str:
        """Generate intelligent suggestion based on command and response"""
        try:
            command_lower = command.lower()

            # Suggerimenti basati sul comando
            if 'disco' in command_lower or 'disk' in command_lower:
                return "Try: elimina file temp dal disco C"
            elif 'temp' in command_lower or 'cleanup' in command_lower:
                return "Try: scansiona disco C per vedere lo spazio liberato"
            elif 'processi' in command_lower or 'process' in command_lower:
                return "Try: systeminfo per informazioni complete del sistema"
            elif 'sistema' in command_lower or 'system' in command_lower:
                return "Try: tasklist per vedere i processi in esecuzione"
            elif 'rete' in command_lower or 'network' in command_lower:
                return "Try: ping google.com per testare la connessione"
            else:
                # Suggerimenti generali intelligenti
                suggestions = [
                    "Try: scansiona disco C per analisi completa",
                    "Use: mostra processi CPU per monitoraggio",
                    "Type: stato del sistema per overview completo",
                    "Try: connessioni di rete per diagnostica",
                    "Use: elimina file temp per pulizia sicura"
                ]

                import random
                return random.choice(suggestions)

        except Exception:
            return "Try: help for more commands"

    def _is_system_command(self, command: str) -> bool:
        """Check if command should be executed as system command"""
        system_commands = [
            'dir', 'ls', 'pwd', 'echo', 'type', 'cat', 'ping', 'ipconfig',
            'systeminfo', 'tasklist', 'netstat', 'whoami', 'date', 'time',
            'cd', 'mkdir', 'rmdir', 'copy', 'move', 'del', 'tree'
        ]

        cmd_name = command.split()[0].lower()
        return cmd_name in system_commands
    
    def clear_terminal(self):
        """Pulisci terminale"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
        
        # Re-add welcome
        self.add_output("Terminal cleared.\n\n", "info")
    
    def show_help(self):
        """Mostra help"""
        help_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              🚀 CYBEX HELP                                  ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║ 🎯 NATURAL LANGUAGE COMMANDS:                                               ║
║   • "scansiona disco C"           - AI disk analysis                        ║
║   • "elimina file temp"           - Safe cleanup                            ║
║   • "mostra processi CPU"         - Process analysis                        ║
║                                                                              ║
║ ⚡ SYSTEM COMMANDS:                                                          ║
║   • dir, ls                       - List directory                          ║
║   • systeminfo                    - System information                      ║
║   • tasklist                      - Running processes                       ║
║   • ping <host>                   - Network ping                            ║
║                                                                              ║
║ 🛠️ TERMINAL COMMANDS:                                                        ║
║   • clear, cls                    - Clear terminal                          ║
║   • help, ?                       - Show this help                          ║
║                                                                              ║
║ 🎮 NAVIGATION:                                                               ║
║   • ↑/↓ arrows                    - Command history                         ║
║   • Enter                         - Execute command                         ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""
        self.add_output(help_text, "info")

    def show_models(self):
        """Show available Ollama models"""
        try:
            if not self.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            result = self.ollama_interface.get_available_models()

            if result['success']:
                models_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                           🤖 AVAILABLE OLLAMA MODELS                        ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
"""

                current_model = result['current_model']

                for i, model in enumerate(result['models'], 1):
                    name = model['name']
                    size_gb = model['size_gb']
                    current_marker = " ← CURRENT" if name == current_model else ""

                    models_text += f"║ {i:2d}. {name:<25} ({size_gb:>5.1f} GB){current_marker:<10} ║\n"

                models_text += f"""║                                                                              ║
║ Total Models: {result['count']:<3}                                                     ║
║ Current Model: {current_model:<25}                              ║
║                                                                              ║
║ 💡 To change model: Use the dropdown in the AI panel                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""
                self.add_output(models_text, "info")
            else:
                self.add_output(f"❌ Failed to get models: {result['error']}\n", "error")

        except Exception as e:
            self.add_output(f"❌ Error showing models: {e}\n", "error")
    
    def start_output_monitor(self):
        """Avvia monitor output"""
        def monitor():
            while True:
                try:
                    if not self.output_queue.empty():
                        text, tag = self.output_queue.get_nowait()
                        self.after(0, lambda t=text, tg=tag: self.add_output(t, tg))
                except:
                    pass
                time.sleep(0.1)
        
        thread = threading.Thread(target=monitor)
        thread.daemon = True
        thread.start()

class CybexFuturisticGUI:
    """Interfaccia grafica futuristica principale"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_ui()
        self.setup_cybex_components()
        
    def setup_window(self):
        """Setup finestra principale"""
        self.root.title("CYBEX - Futuristic AI Terminal")
        self.root.geometry("1400x900")
        self.root.minsize(1000, 600)
        
        # Tema scuro
        self.root.configure(bg=FuturisticTheme.BG_DARK)

        # Icona finestra
        self.set_window_icon()
        
        # Centra finestra
        self.center_window()

    def set_window_icon(self):
        """Imposta icona della finestra"""
        try:
            # Percorso del logo
            logo_path = Path(__file__).parent.parent.parent / "assets" / "logo.png"

            if logo_path.exists():
                # Carica immagine per icona
                image = Image.open(logo_path)

                # Ridimensiona per icona (32x32 pixel)
                image = image.resize((32, 32), Image.Resampling.LANCZOS)

                # Converti in formato compatibile con iconphoto
                icon_photo = ImageTk.PhotoImage(image)

                # Mantieni riferimento per evitare garbage collection
                self.window_icon = icon_photo

                # Imposta icona (con gestione errori)
                try:
                    self.root.iconphoto(True, icon_photo)
                    print("✅ Window icon set successfully")
                except tk.TclError as e:
                    # Fallback: prova con wm_iconbitmap se disponibile
                    print(f"⚠️ iconphoto failed: {e}")
                    try:
                        # Salva temporaneamente come ICO se possibile
                        temp_ico = Path(__file__).parent.parent.parent / "temp_icon.ico"
                        image.save(temp_ico, format='ICO')
                        self.root.iconbitmap(temp_ico)
                        print("✅ Window icon set via iconbitmap")
                        # Rimuovi file temporaneo
                        temp_ico.unlink()
                    except Exception:
                        print("⚠️ Could not set window icon")

            else:
                print("⚠️ Logo file not found for window icon")

        except ImportError:
            print("⚠️ PIL not available for window icon")
        except Exception as e:
            print(f"⚠️ Error setting window icon: {e}")
    
    def center_window(self):
        """Centra finestra sullo schermo"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """Setup interfaccia utente"""
        
        # Header principale
        self.create_header()
        
        # Pannello principale
        main_frame = tk.Frame(self.root, bg=FuturisticTheme.BG_DARK)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Pannello sinistro (terminale)
        left_frame = tk.Frame(main_frame, bg=FuturisticTheme.BG_DARK)
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
        
        # Terminale
        self.terminal = FuturisticTerminal(left_frame)
        self.terminal.set_gui_parent(self)  # Imposta riferimento alla GUI
        self.terminal.pack(fill=tk.BOTH, expand=True)
        
        # Pannello destro (controlli)
        right_frame = tk.Frame(main_frame, bg=FuturisticTheme.BG_PANEL, width=300)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_frame.pack_propagate(False)
        
        self.create_control_panel(right_frame)
        
        # Footer
        self.create_footer()
    
    def create_header(self):
        """Crea header futuristico con logo"""
        header_frame = tk.Frame(self.root, bg=FuturisticTheme.BG_PANEL, height=80)
        header_frame.pack(fill=tk.X, padx=10, pady=10)
        header_frame.pack_propagate(False)

        # Logo container
        logo_frame = tk.Frame(header_frame, bg=FuturisticTheme.BG_PANEL)
        logo_frame.pack(side=tk.LEFT, padx=10, pady=10)

        # Carica e mostra logo
        self.load_logo(logo_frame)

        # Titolo con font fallback
        title_frame = tk.Frame(header_frame, bg=FuturisticTheme.BG_PANEL)
        title_frame.pack(side=tk.LEFT, padx=10, pady=10)

        try:
            # Prova font futuristico
            title_font = ("Orbitron", 20, "bold")
        except:
            # Fallback a font disponibili
            title_font = ("Consolas", 18, "bold")

        title_label = AnimatedLabel(
            title_frame,
            text="◢◤ CYBEX FUTURISTIC TERMINAL ◥◣",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_CYAN,
            font=title_font
        )
        title_label.pack()

        # Sottotitolo
        subtitle_label = tk.Label(
            title_frame,
            text="Next-Generation AI Command Interface",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_SECONDARY,
            font=("Consolas", 10)
        )
        subtitle_label.pack()

        # Avvia effetto pulsante
        title_label.pulse_effect(FuturisticTheme.NEON_CYAN, FuturisticTheme.NEON_PURPLE, 2000)

        # Status panel
        status_frame = tk.Frame(header_frame, bg=FuturisticTheme.BG_PANEL)
        status_frame.pack(side=tk.RIGHT, padx=20, pady=10)

        # System status
        self.system_status = tk.Label(
            status_frame,
            text="SYSTEM: ONLINE",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_GREEN,
            font=("Consolas", 10, "bold")
        )
        self.system_status.pack(anchor=tk.E)

        # AI status
        self.ai_status = tk.Label(
            status_frame,
            text="AI: READY",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_PURPLE,
            font=("Consolas", 10, "bold")
        )
        self.ai_status.pack(anchor=tk.E)

        # Time
        self.time_label = tk.Label(
            status_frame,
            text="",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_SECONDARY,
            font=("Consolas", 9)
        )
        self.time_label.pack(anchor=tk.E)

        self.update_time()

    def load_logo(self, parent):
        """Carica e mostra il logo"""
        try:
            # Percorso del logo
            logo_path = Path(__file__).parent.parent.parent / "assets" / "logo.png"

            if logo_path.exists():
                # Carica immagine
                image = Image.open(logo_path)

                # Ridimensiona per header (60x60 pixel)
                image = image.resize((60, 60), Image.Resampling.LANCZOS)

                # Converti per tkinter
                self.logo_photo = ImageTk.PhotoImage(image)

                # Crea label con logo
                logo_label = tk.Label(
                    parent,
                    image=self.logo_photo,
                    bg=FuturisticTheme.BG_PANEL,
                    relief=tk.FLAT,
                    bd=0
                )
                logo_label.pack()

                # Effetto hover per logo
                logo_label.bind("<Enter>", lambda e: self.on_logo_hover(e, True))
                logo_label.bind("<Leave>", lambda e: self.on_logo_hover(e, False))

                print("✅ Logo loaded successfully")
            else:
                # Logo non trovato, usa testo alternativo
                self.create_text_logo(parent)
                print("⚠️ Logo file not found, using text logo")

        except ImportError:
            # PIL non disponibile
            self.create_text_logo(parent)
            print("⚠️ PIL not available, using text logo")
        except Exception as e:
            # Errore generico
            self.create_text_logo(parent)
            print(f"⚠️ Error loading logo: {e}")

    def create_text_logo(self, parent):
        """Crea logo testuale se immagine non disponibile"""
        text_logo = tk.Label(
            parent,
            text="🚀\nCYBEX",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_CYAN,
            font=("Consolas", 12, "bold"),
            justify=tk.CENTER
        )
        text_logo.pack()

    def on_logo_hover(self, event, entering):
        """Effetto hover per logo"""
        if entering:
            # Mouse entra - effetto glow
            event.widget.config(relief=tk.RAISED, bd=2)
        else:
            # Mouse esce - normale
            event.widget.config(relief=tk.FLAT, bd=0)

    def load_mini_logo(self, parent):
        """Carica mini logo per pannello controlli"""
        try:
            # Percorso del logo
            logo_path = Path(__file__).parent.parent.parent / "assets" / "logo.png"

            if logo_path.exists():
                # Carica immagine
                image = Image.open(logo_path)

                # Ridimensiona per mini logo (24x24 pixel)
                image = image.resize((24, 24), Image.Resampling.LANCZOS)

                # Converti per tkinter
                mini_logo_photo = ImageTk.PhotoImage(image)

                # Crea label con mini logo
                mini_logo_label = tk.Label(
                    parent,
                    image=mini_logo_photo,
                    bg=FuturisticTheme.BG_PANEL,
                    relief=tk.FLAT,
                    bd=0
                )
                mini_logo_label.pack(pady=2)

                # Mantieni riferimento
                self.mini_logo_photo = mini_logo_photo

            else:
                # Logo non trovato, usa emoji
                mini_logo_label = tk.Label(
                    parent,
                    text="🚀",
                    bg=FuturisticTheme.BG_PANEL,
                    fg=FuturisticTheme.NEON_ORANGE,
                    font=("Consolas", 16)
                )
                mini_logo_label.pack(pady=2)

        except Exception as e:
            # Errore, usa emoji fallback
            mini_logo_label = tk.Label(
                parent,
                text="🚀",
                bg=FuturisticTheme.BG_PANEL,
                fg=FuturisticTheme.NEON_ORANGE,
                font=("Consolas", 16)
            )
            mini_logo_label.pack(pady=2)
    
    def create_control_panel(self, parent):
        """Crea pannello controlli"""

        # Header pannello con mini logo
        panel_header = tk.Frame(parent, bg=FuturisticTheme.BG_PANEL)
        panel_header.pack(pady=5)

        # Mini logo per pannello
        self.load_mini_logo(panel_header)

        # Titolo pannello
        panel_title = tk.Label(
            panel_header,
            text="◉ CONTROL PANEL ◉",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_ORANGE,
            font=("Consolas", 12, "bold")
        )
        panel_title.pack(pady=5)
        
        # Quick actions
        self.create_quick_actions(parent)
        
        # System monitor
        self.create_system_monitor(parent)
        
        # AI controls
        self.create_ai_controls(parent)
    
    def create_quick_actions(self, parent):
        """Crea azioni rapide"""
        actions_frame = tk.LabelFrame(
            parent,
            text="⚡ QUICK ACTIONS",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_GREEN,
            font=("Consolas", 10, "bold")
        )
        actions_frame.pack(fill=tk.X, padx=10, pady=5)
        
        actions = [
            ("🖥️ System Info", "systeminfo"),
            ("💾 Disk Scan", "scansiona disco C"),
            ("🧹 Cleanup", "elimina file temp"),
            ("📊 Processes", "tasklist"),
            ("🌐 Network", "ipconfig"),
            ("🔄 Clear", "clear")
        ]
        
        for i, (text, command) in enumerate(actions):
            btn = tk.Button(
                actions_frame,
                text=text,
                bg=FuturisticTheme.BG_DARK,
                fg=FuturisticTheme.TEXT_PRIMARY,
                font=("Consolas", 9),
                relief=tk.FLAT,
                bd=1,
                command=lambda cmd=command: self.execute_quick_action(cmd)
            )
            btn.pack(fill=tk.X, padx=5, pady=2)
            
            # Hover effects
            btn.bind("<Enter>", lambda e, b=btn: b.config(bg=FuturisticTheme.BG_ACCENT))
            btn.bind("<Leave>", lambda e, b=btn: b.config(bg=FuturisticTheme.BG_DARK))
    
    def create_system_monitor(self, parent):
        """Crea monitor sistema"""
        monitor_frame = tk.LabelFrame(
            parent,
            text="📊 SYSTEM MONITOR",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_CYAN,
            font=("Consolas", 10, "bold")
        )
        monitor_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # CPU
        self.cpu_label = tk.Label(
            monitor_frame,
            text="CPU: ---%",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        )
        self.cpu_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # Memory
        self.memory_label = tk.Label(
            monitor_frame,
            text="RAM: ---%",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        )
        self.memory_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # Disk
        self.disk_label = tk.Label(
            monitor_frame,
            text="DISK: ---%",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        )
        self.disk_label.pack(anchor=tk.W, padx=5, pady=2)
        
        self.start_system_monitor()
    
    def create_ai_controls(self, parent):
        """Crea controlli AI"""
        ai_frame = tk.LabelFrame(
            parent,
            text="🤖 AI CONTROLS",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_PURPLE,
            font=("Consolas", 10, "bold")
        )
        ai_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Model selector
        tk.Label(
            ai_frame,
            text="Model:",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        ).pack(anchor=tk.W, padx=5, pady=2)
        
        self.model_var = tk.StringVar(value="gemma3:4b")

        # Get available models from Ollama
        available_models = self._get_available_models()

        model_combo = ttk.Combobox(
            ai_frame,
            textvariable=self.model_var,
            values=available_models,
            state="readonly",
            font=("Consolas", 9)
        )
        model_combo.pack(fill=tk.X, padx=5, pady=2)

        # Bind model change event
        model_combo.bind("<<ComboboxSelected>>", self._on_model_changed)
        
        # AI toggle
        self.ai_enabled = tk.BooleanVar(value=True)
        ai_check = tk.Checkbutton(
            ai_frame,
            text="AI Processing Enabled",
            variable=self.ai_enabled,
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            selectcolor=FuturisticTheme.BG_DARK,
            font=("Consolas", 9)
        )
        ai_check.pack(anchor=tk.W, padx=5, pady=5)
    
    def create_footer(self):
        """Crea footer"""
        footer_frame = tk.Frame(self.root, bg=FuturisticTheme.BG_PANEL, height=30)
        footer_frame.pack(fill=tk.X, padx=10, pady=5)
        footer_frame.pack_propagate(False)
        
        footer_text = tk.Label(
            footer_frame,
            text="◢◤ CYBEX FUTURISTIC TERMINAL v2.0 - Advanced AI Command Interface ◥◣",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_MUTED,
            font=("Consolas", 9)
        )
        footer_text.pack(expand=True)
    
    def setup_cybex_components(self):
        """Setup componenti Cybex"""
        try:
            print("Initializing Cybex components...")
            self.core = CybexCore()
            print("✅ Cybex Core initialized")

            self.ollama_interface = OllamaInterface(
                self.core.config_manager,
                self.core.log_manager
            )
            print("✅ Ollama Interface initialized")

            self.agent_tools = AgentTools(self.core.log_manager)
            print("✅ Agent Tools initialized")

            self.operation_monitor = init_global_monitor(self.core.log_manager)
            print("✅ Operation Monitor initialized")

            self.ai_status.config(text="AI: ONLINE", fg=FuturisticTheme.NEON_GREEN)
            print("✅ All Cybex components ready")

        except Exception as e:
            print(f"❌ Failed to initialize Cybex components: {e}")
            self.ai_status.config(text="AI: OFFLINE", fg=FuturisticTheme.ERROR)

            # Set fallback values
            self.core = None
            self.ollama_interface = None
            self.agent_tools = None
            self.operation_monitor = None
    
    def execute_quick_action(self, command: str):
        """Esegui azione rapida"""
        self.terminal.input_entry.delete(0, tk.END)
        self.terminal.input_entry.insert(0, command)
        self.terminal.on_command_enter(None)
    
    def update_time(self):
        """Aggiorna ora"""
        current_time = time.strftime("%H:%M:%S")
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)
    
    def start_system_monitor(self):
        """Avvia monitor sistema"""
        def update_stats():
            try:
                import psutil

                # CPU (non-blocking)
                cpu_percent = psutil.cpu_percent(interval=None)
                if cpu_percent == 0.0:
                    cpu_percent = psutil.cpu_percent(interval=0.1)
                self.cpu_label.config(text=f"CPU: {cpu_percent:.1f}%")

                # Memory
                memory = psutil.virtual_memory()
                self.memory_label.config(text=f"RAM: {memory.percent:.1f}%")

                # Disk (Windows compatible)
                try:
                    if os.name == 'nt':
                        disk = psutil.disk_usage('C:\\')
                    else:
                        disk = psutil.disk_usage('/')
                    disk_percent = (disk.used / disk.total) * 100
                    self.disk_label.config(text=f"DISK: {disk_percent:.1f}%")
                except:
                    self.disk_label.config(text="DISK: N/A")

            except Exception as e:
                print(f"System monitor error: {e}")
                # Set default values on error
                self.cpu_label.config(text="CPU: ---%")
                self.memory_label.config(text="RAM: ---%")
                self.disk_label.config(text="DISK: ---%")

            # Schedule next update
            self.root.after(5000, update_stats)  # Update every 5 seconds

        # Start monitoring
        update_stats()
    
    def run(self):
        """Avvia interfaccia"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            pass
        finally:
            if hasattr(self, 'core') and self.core:
                try:
                    self.core.shutdown()
                except:
                    pass


def main():
    """Main entry point"""
    try:
        app = CybexFuturisticGUI()
        app.run()
    except Exception as e:
        print(f"Error starting Cybex Futuristic GUI: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
