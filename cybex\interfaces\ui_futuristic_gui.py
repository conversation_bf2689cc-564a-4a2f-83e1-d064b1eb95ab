#!/usr/bin/env python3
"""
Cybex Futuristic GUI - Enterprise Edition
Professional cyberpunk interface with Ollama integration
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox, font
import threading
import time
import os
import sys
import json
import requests
from pathlib import Path
from typing import Dict, List, Optional, Any
import queue
import math
from datetime import datetime

# Import Ollama Monitor
try:
    from ..modules.ollama_monitor import OllamaMonitor, OllamaStatus
except ImportError:
    OllamaMonitor = None
    OllamaStatus = None

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from cybex.core.cybex_core import CybexCore
    from cybex.modules.ollama_interface import OllamaInterface
    from cybex.modules.agent_tools import AgentTools
except ImportError as e:
    print(f"Warning: Could not import Cybex modules: {e}")

class SunriseProfessionalTheme:
    """Professional Sunrise Theme - Apple-level Design System"""

    # === PRIMARY SUNRISE PALETTE ===
    SUNRISE_ORANGE = "#FF6B35"      # Vibrant sunrise orange
    SUNRISE_PINK = "#FF8E9B"        # Soft sunrise pink
    SUNRISE_GOLD = "#FFD23F"        # Golden sunrise
    SUNRISE_CORAL = "#FF7F7F"       # Coral sunrise
    SUNRISE_PEACH = "#FFAB91"       # Peach sunrise
    SUNRISE_AMBER = "#FFC107"       # Amber sunrise

    # === BACKGROUND SYSTEM ===
    BG_PRIMARY = "#0F0F0F"          # Deep space black
    BG_SECONDARY = "#1A1A1A"        # Card background
    BG_TERTIARY = "#252525"         # Elevated surfaces
    BG_ACCENT = "#2A2A2A"           # Subtle accent
    BG_GLASS = "#1A1A1A80"          # Glass morphism

    # === TEXT HIERARCHY ===
    TEXT_PRIMARY = "#FFFFFF"        # Primary text
    TEXT_SECONDARY = "#E5E5E5"      # Secondary text
    TEXT_TERTIARY = "#B3B3B3"       # Tertiary text
    TEXT_MUTED = "#808080"          # Muted text
    TEXT_ACCENT = "#FFD23F"         # Accent text

    # === STATUS SYSTEM ===
    SUCCESS = "#4CAF50"             # Success green
    WARNING = "#FF9800"             # Warning orange
    ERROR = "#F44336"               # Error red
    INFO = "#2196F3"                # Info blue
    PROCESSING = "#FF6B35"          # Processing orange

    # === BORDERS & SHADOWS ===
    BORDER_SUBTLE = "#333333"       # Subtle borders
    BORDER_ACCENT = "#FF6B35"       # Accent borders
    SHADOW_LIGHT = "#00000020"      # Light shadow
    SHADOW_MEDIUM = "#00000040"     # Medium shadow
    SHADOW_HEAVY = "#00000080"      # Heavy shadow

class SunriseProfessionalTerminal(tk.Frame):
    """Professional Sunrise Terminal with AI integration"""

    def __init__(self, parent, **kwargs):
        super().__init__(parent, bg=SunriseProfessionalTheme.BG_PRIMARY, **kwargs)
        
        self.command_queue = queue.Queue()
        self.output_queue = queue.Queue()
        self.gui_parent = None
        self.command_history = []
        self.history_index = -1
        self.max_history = 100
        
        self.setup_ui()
        self.setup_welcome()
        self.start_output_monitor()
    
    def setup_ui(self):
        """Setup professional terminal UI with Sunrise theme"""
        # Output area with professional styling
        self.output_text = scrolledtext.ScrolledText(
            self,
            bg=SunriseProfessionalTheme.BG_SECONDARY,
            fg=SunriseProfessionalTheme.TEXT_PRIMARY,
            font=("SF Pro Display", 11),  # Apple-like font
            insertbackground=SunriseProfessionalTheme.SUNRISE_ORANGE,
            selectbackground=SunriseProfessionalTheme.BG_ACCENT,
            selectforeground=SunriseProfessionalTheme.TEXT_PRIMARY,
            wrap=tk.WORD,
            state=tk.DISABLED,
            relief=tk.FLAT,
            borderwidth=0,
            highlightthickness=1,
            highlightcolor=SunriseProfessionalTheme.SUNRISE_ORANGE,
            highlightbackground=SunriseProfessionalTheme.BORDER_SUBTLE
        )
        self.output_text.pack(fill=tk.BOTH, expand=True, padx=8, pady=8)

        # Professional input frame
        input_frame = tk.Frame(
            self,
            bg=SunriseProfessionalTheme.BG_TERTIARY,
            relief=tk.FLAT,
            bd=1
        )
        input_frame.pack(fill=tk.X, padx=8, pady=(0, 8))

        # Modern prompt label with sunrise accent
        prompt_label = tk.Label(
            input_frame,
            text="CYBEX ❯",
            bg=SunriseProfessionalTheme.BG_TERTIARY,
            fg=SunriseProfessionalTheme.SUNRISE_GOLD,
            font=("SF Pro Display", 11, "bold")
        )
        prompt_label.pack(side=tk.LEFT, padx=(12, 8), pady=8)
        
        # Professional input entry with modern styling
        self.input_entry = tk.Entry(
            input_frame,
            bg=SunriseProfessionalTheme.BG_SECONDARY,
            fg=SunriseProfessionalTheme.TEXT_PRIMARY,
            font=("SF Pro Display", 11),
            insertbackground=SunriseProfessionalTheme.SUNRISE_ORANGE,
            selectbackground=SunriseProfessionalTheme.SUNRISE_ORANGE,
            selectforeground=SunriseProfessionalTheme.TEXT_PRIMARY,
            bd=0,
            relief=tk.FLAT,
            highlightthickness=2,
            highlightcolor=SunriseProfessionalTheme.SUNRISE_ORANGE,
            highlightbackground=SunriseProfessionalTheme.BORDER_SUBTLE
        )
        self.input_entry.pack(fill=tk.X, expand=True, padx=(0, 12), pady=8)
        self.input_entry.bind('<Return>', self.on_enter)
        self.input_entry.bind('<Up>', self.on_history_up)
        self.input_entry.bind('<Down>', self.on_history_down)
        self.input_entry.bind('<FocusIn>', self.on_input_focus)
        self.input_entry.bind('<FocusOut>', self.on_input_blur)
        self.input_entry.focus()

        # Setup professional text tags
        self.setup_professional_text_tags()

    def setup_professional_text_tags(self):
        """Setup professional text styling tags"""
        tags = {
            "header": {
                "foreground": SunriseProfessionalTheme.SUNRISE_GOLD,
                "font": ("SF Pro Display", 12, "bold")
            },
            "info": {
                "foreground": SunriseProfessionalTheme.TEXT_SECONDARY,
                "font": ("SF Pro Display", 11)
            },
            "success": {
                "foreground": SunriseProfessionalTheme.SUCCESS,
                "font": ("SF Pro Display", 11, "bold")
            },
            "warning": {
                "foreground": SunriseProfessionalTheme.WARNING,
                "font": ("SF Pro Display", 11, "bold")
            },
            "error": {
                "foreground": SunriseProfessionalTheme.ERROR,
                "font": ("SF Pro Display", 11, "bold")
            },
            "ai": {
                "foreground": SunriseProfessionalTheme.SUNRISE_PINK,
                "font": ("SF Pro Display", 11, "italic")
            },
            "system": {
                "foreground": SunriseProfessionalTheme.TEXT_TERTIARY,
                "font": ("SF Pro Display", 10)
            },
            "accent": {
                "foreground": SunriseProfessionalTheme.SUNRISE_ORANGE,
                "font": ("SF Pro Display", 11, "bold")
            },
            "timestamp": {
                "foreground": SunriseProfessionalTheme.TEXT_MUTED,
                "font": ("SF Pro Display", 9)
            }
        }

        for tag, config in tags.items():
            self.output_text.tag_configure(tag, **config)

    def on_input_focus(self, event):
        """Handle input focus with modern styling"""
        self.input_entry.configure(
            highlightcolor=SunriseProfessionalTheme.SUNRISE_ORANGE,
            highlightthickness=2
        )

    def on_input_blur(self, event):
        """Handle input blur"""
        self.input_entry.configure(
            highlightcolor=SunriseProfessionalTheme.BORDER_SUBTLE,
            highlightthickness=1
        )

    def setup_welcome(self):
        """Setup professional welcome message"""
        current_time = datetime.now().strftime("%H:%M:%S")
        welcome = f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🌅 CYBEX ENTERPRISE - SUNRISE EDITION                    ║
║                      Professional AI Command Interface                       ║
║                        Session started at {current_time}                           ║
╚══════════════════════════════════════════════════════════════════════════════╝

🎯 Professional AI Assistant Ready
💡 Type commands or natural language requests
🚀 Examples: 'ciao', 'models', 'system info', 'help', 'model stats'

"""
        self.add_output(welcome, "header")
    
    def add_output(self, text: str, tag: str = "info"):
        """Add output to terminal"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.insert(tk.END, text, tag)
        self.output_text.see(tk.END)
        self.output_text.config(state=tk.DISABLED)
    
    def on_enter(self, event):
        """Handle enter key with enhanced features"""
        command = self.input_entry.get().strip()
        if command:
            # Enhanced history management - avoid duplicates
            if not self.command_history or self.command_history[-1] != command:
                self.command_history.append(command)

                # Limit history size
                if len(self.command_history) > self.max_history:
                    self.command_history.pop(0)

            self.history_index = len(self.command_history)

            # Show command with timestamp
            current_time = datetime.now().strftime("%H:%M:%S")
            self.add_output(f"[{current_time}] CYBEX ❯ {command}\n", "header")
            self.input_entry.delete(0, tk.END)

            # Process command in thread
            threading.Thread(target=self.process_command, args=(command,), daemon=True).start()
    
    def on_history_up(self, event):
        """Handle up arrow - previous command"""
        if self.command_history and self.history_index > 0:
            self.history_index -= 1
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, self.command_history[self.history_index])
    
    def on_history_down(self, event):
        """Handle down arrow - next command"""
        if self.command_history and self.history_index < len(self.command_history) - 1:
            self.history_index += 1
            self.input_entry.delete(0, tk.END)
            self.input_entry.insert(0, self.command_history[self.history_index])
        elif self.history_index >= len(self.command_history) - 1:
            self.input_entry.delete(0, tk.END)
            self.history_index = len(self.command_history)
    
    def process_command(self, command: str):
        """Process command"""
        try:
            # Special commands
            if command.lower() in ['clear', 'cls']:
                self.after(0, self.clear_terminal)
                return
            
            if command.lower() in ['help', '?']:
                self.after(0, self.show_help)
                return
            
            if command.lower() in ['models', 'lista modelli']:
                self.after(0, self.show_models)
                return

            if command.lower() in ['reload prompt', 'reload-prompt', 'refresh prompt', 'ricarica prompt']:
                self.after(0, self._reload_system_prompt)
                return

            if command.lower() in ['model stats', 'model-stats', 'performance', 'statistiche modello']:
                self.after(0, self._show_model_performance)
                return

            if command.lower() in ['analyze models', 'analyze-models', 'analizza modelli']:
                self.after(0, self._analyze_model_performance)
                return

            if command.lower() in ['reset timeouts', 'reset-timeouts', 'reset timeout']:
                self.after(0, self._reset_timeouts)
                return

            if command.lower() in ['conservative timeouts', 'conservative-timeouts', 'timeout conservativi']:
                self.after(0, self._set_conservative_timeouts)
                return

            # Advanced tool execution commands
            if command.lower().startswith('execute '):
                tool_name = command[8:].strip()
                self.after(0, lambda: self._execute_tool_command(tool_name))
                return

            if command.lower() in ['tools', 'list tools', 'show tools']:
                self.after(0, self._show_all_tools)
                return

            if command.lower() in ['performance', 'perf', 'metrics']:
                self.after(0, lambda: self._execute_tool_command('get_performance_metrics'))
                return

            if command.lower() in ['memory', 'mem', 'ram']:
                self.after(0, lambda: self._execute_tool_command('get_memory_info'))
                return

            if command.lower() in ['cpu', 'processor']:
                self.after(0, lambda: self._execute_tool_command('get_cpu_info'))
                return

            if command.lower() in ['network', 'net', 'network test']:
                self.after(0, lambda: self._execute_tool_command('network_diagnostics'))
                return

            if command.lower() in ['disk', 'storage', 'disk usage']:
                self.after(0, lambda: self._execute_tool_command('get_disk_usage'))
                return

            if command.lower() in ['processes', 'proc', 'ps']:
                self.after(0, lambda: self._execute_tool_command('list_processes'))
                return

            # Dashboard commands
            if command.lower() in ['dashboard', 'dash', 'overview']:
                self.after(0, self._show_system_dashboard)
                return

            if command.lower() in ['status', 'health', 'system status']:
                self.after(0, self._show_system_status)
                return

            # System commands
            if self._is_system_command(command):
                self._execute_system_command(command)
            else:
                # Check for command suggestions first
                suggestions = self._get_command_suggestions(command)
                if suggestions:
                    self.add_output(f"❓ Unknown command: {command}\n", "warning")
                    self.add_output("💡 Did you mean:\n", "info")
                    for suggestion in suggestions[:3]:  # Show top 3 suggestions
                        self.add_output(f"  • {suggestion}\n", "accent")
                else:
                    # AI processing
                    self._process_with_ai(command)
                
        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ Error: {e}\n", "error"))
    
    def _is_system_command(self, command: str) -> bool:
        """Check if command is a system command"""
        system_commands = ['dir', 'ls', 'pwd', 'echo', 'systeminfo', 'tasklist', 'ping']
        return any(command.lower().startswith(cmd) for cmd in system_commands)
    
    def _execute_system_command(self, command: str):
        """Execute system command"""
        try:
            if self.gui_parent and self.gui_parent.agent_tools:
                execution_id = self.gui_parent.agent_tools.execute_tool('execute_command', {'command': command})
                
                if execution_id:
                    # Wait for result
                    import time
                    for _ in range(60):  # Wait up to 30 seconds
                        execution = self.gui_parent.agent_tools.get_execution_status(execution_id)
                        if execution and execution.status.value in ['completed', 'failed']:
                            break
                        time.sleep(0.5)
                    
                    execution = self.gui_parent.agent_tools.get_execution_status(execution_id)
                    if execution and execution.result:
                        if execution.result.success:
                            self.after(0, lambda: self.add_output(execution.result.output + "\n", "info"))
                        else:
                            self.after(0, lambda: self.add_output(f"❌ {execution.result.error}\n", "error"))
                    else:
                        self.after(0, lambda: self.add_output("❌ Command execution failed\n", "error"))
                else:
                    self.after(0, lambda: self.add_output("❌ Could not execute command\n", "error"))
            else:
                self.after(0, lambda: self.add_output("❌ Agent tools not available\n", "error"))
                
        except Exception as e:
            self.after(0, lambda: self.add_output(f"❌ System command error: {e}\n", "error"))
    
    def _process_with_ai(self, command: str):
        """Process command with AI"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.after(0, lambda: self.add_output("❌ AI interface not available. Check Ollama server.\n", "error"))
                return

            # Start monitoring if available
            if hasattr(self.gui_parent, 'ollama_monitor') and self.gui_parent.ollama_monitor:
                current_model = self.gui_parent.ollama_interface.model
                self.gui_parent.ollama_monitor.start_activity(current_model, command)

            # Generate AI response
            response = self.gui_parent.ollama_interface.generate_response(
                f"L'utente ha scritto: '{command}'. Rispondi in italiano in modo conciso e utile.",
                {'mode': 'terminal', 'user_input': command}
            )

            # End monitoring
            if hasattr(self.gui_parent, 'ollama_monitor') and self.gui_parent.ollama_monitor:
                if response.success:
                    self.gui_parent.ollama_monitor.end_activity(success=True)
                else:
                    self.gui_parent.ollama_monitor.end_activity(success=False, error=response.error)

            if response.success:
                self.after(0, lambda: self.add_output(f"🤖 {response.content}\n\n", "ai"))
            else:
                error_msg = response.error or "Unknown error"
                self.after(0, lambda: self.add_output(f"❌ AI Error: {error_msg}\n", "error"))

        except Exception as e:
            # End monitoring on error
            if hasattr(self.gui_parent, 'ollama_monitor') and self.gui_parent.ollama_monitor:
                self.gui_parent.ollama_monitor.end_activity(success=False, error=str(e))
            self.after(0, lambda: self.add_output(f"❌ AI processing error: {e}\n", "error"))
    
    def clear_terminal(self):
        """Clear terminal"""
        self.output_text.config(state=tk.NORMAL)
        self.output_text.delete(1.0, tk.END)
        self.output_text.config(state=tk.DISABLED)
        self.setup_welcome()
    
    def show_help(self):
        """Show help"""
        help_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                              🚀 CYBEX HELP                                  ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
║ 🎯 NATURAL LANGUAGE COMMANDS:                                               ║
║   • "ciao"                        - Greet the AI                            ║
║   • "analizza sistema"            - System analysis                         ║
║   • "stato ollama"                - Check Ollama status                     ║
║                                                                              ║
║ 🖥️ SYSTEM COMMANDS:                                                          ║
║   • systeminfo                    - System information                      ║
║   • tasklist                      - Running processes                       ║
║   • dir                           - List directory                          ║
║                                                                              ║
║ 🛠️ TERMINAL COMMANDS:                                                        ║
║   • clear, cls                    - Clear terminal                          ║
║   • help, ?                       - Show this help                          ║
║   • models                        - Show available Ollama models            ║
║   • reload prompt                 - Reload system prompt from file          ║
║   • model stats                   - Show current model performance          ║
║   • analyze models                - Analyze and optimize model performance  ║
║   • reset timeouts                - Reset all timeouts to 320s base        ║
║   • conservative timeouts         - Set conservative timeouts by model size ║
║                                                                              ║
║ 🚀 ENTERPRISE TOOLS:                                                        ║
║   • execute <tool_name>           - Execute any available enterprise tool   ║
║   • tools                         - Show all available tools catalog        ║
║   • dashboard                     - Show comprehensive system dashboard     ║
║   • status                        - Quick system status overview            ║
║                                                                              ║
║ ⚡ QUICK COMMANDS:                                                           ║
║   • performance, perf, metrics    - System performance metrics             ║
║   • memory, mem, ram              - Detailed memory information             ║
║   • cpu, processor                - CPU information and usage               ║
║   • network, net                  - Network diagnostics and connectivity   ║
║   • disk, storage                 - Disk usage and storage information     ║
║   • processes, proc, ps           - Running processes list                  ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""
        self.add_output(help_text, "info")
    
    def show_models(self):
        """Show available models"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return
            
            result = self.gui_parent.ollama_interface.get_available_models()
            
            if result['success']:
                models_text = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                           🤖 AVAILABLE OLLAMA MODELS                        ║
╠══════════════════════════════════════════════════════════════════════════════╣
║                                                                              ║
"""
                
                current_model = result['current_model']
                
                for i, model in enumerate(result['models'], 1):
                    name = model['name']
                    size_gb = model['size_gb']
                    current_marker = " ← CURRENT" if name == current_model else ""
                    
                    models_text += f"║ {i:2d}. {name:<25} ({size_gb:>5.1f} GB){current_marker:<10} ║\n"
                
                models_text += f"""║                                                                              ║
║ Total Models: {result['count']:<3}                                                     ║
║ Current Model: {current_model:<25}                              ║
║                                                                              ║
║ 💡 To change model: Use the dropdown in the AI panel                       ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝

"""
                self.add_output(models_text, "info")
            else:
                self.add_output(f"❌ Failed to get models: {result['error']}\n", "error")
                
        except Exception as e:
            self.add_output(f"❌ Error showing models: {e}\n", "error")

    def _reload_system_prompt(self):
        """Reload system prompt from file"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            # Reload the prompt
            success = self.gui_parent.ollama_interface.reload_system_prompt()

            if success:
                self.add_output("✅ System prompt reloaded successfully\n", "success")

                # Show preview of the prompt
                prompt_preview = self.gui_parent.ollama_interface.get_system_prompt()[:200]
                self.add_output(f"📋 Prompt preview: {prompt_preview}...\n", "info")
            else:
                self.add_output("⚠️ System prompt unchanged or failed to reload\n", "warning")

        except Exception as e:
            self.add_output(f"❌ Error reloading prompt: {e}\n", "error")

    def _show_model_performance(self):
        """Show model performance statistics"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            # Get current model stats
            current_stats = self.gui_parent.ollama_interface.get_model_performance_stats()

            if "error" in current_stats:
                self.add_output(f"⚠️ {current_stats['error']}\n", "warning")
                return

            # Display current model performance
            self.add_output("📊 MODEL PERFORMANCE STATISTICS\n", "info")
            self.add_output("=" * 50 + "\n", "info")

            model_name = current_stats.get('model_name', 'Unknown')
            self.add_output(f"🤖 Model: {model_name}\n", "info")
            self.add_output(f"📈 Total Requests: {current_stats.get('total_requests', 0)}\n", "info")
            self.add_output(f"✅ Success Rate: {current_stats.get('success_rate', 0)}%\n", "info")
            self.add_output(f"⏱️ Average Response Time: {current_stats.get('average_response_time', 0)}s\n", "info")
            self.add_output(f"⚡ Min Response Time: {current_stats.get('min_response_time', 0)}s\n", "info")
            self.add_output(f"🐌 Max Response Time: {current_stats.get('max_response_time', 0)}s\n", "info")
            self.add_output(f"⏰ Current Timeout: {current_stats.get('current_timeout', 0)}s\n", "info")
            self.add_output(f"💾 Model Size: {current_stats.get('model_size_gb', 0)}GB\n", "info")
            self.add_output(f"🔧 Complexity Factor: {current_stats.get('complexity_factor', 1.0)}\n", "info")

            last_updated = current_stats.get('last_updated', '')
            if last_updated:
                self.add_output(f"🕒 Last Updated: {last_updated[:19]}\n", "info")

            self.add_output("\n💡 Use 'analyze models' to refresh performance data\n", "info")

        except Exception as e:
            self.add_output(f"❌ Error showing performance: {e}\n", "error")

    def _analyze_model_performance(self):
        """Analyze model performance"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            self.add_output("🔍 Analyzing model performance...\n", "info")

            # Perform analysis
            result = self.gui_parent.ollama_interface.analyze_model_performance()

            if result.get('success'):
                self.add_output("✅ Model performance analysis completed\n", "success")

                # Show updated stats
                current_stats = result.get('current_stats', {})
                if current_stats and "error" not in current_stats:
                    self.add_output(f"\n📊 Updated stats for {result.get('current_model', 'current model')}:\n", "info")
                    self.add_output(f"   • Timeout: {current_stats.get('current_timeout', 0)}s\n", "info")
                    self.add_output(f"   • Avg Response: {current_stats.get('average_response_time', 0)}s\n", "info")
                    self.add_output(f"   • Success Rate: {current_stats.get('success_rate', 0)}%\n", "info")

                self.add_output("\n💡 Use 'model stats' to see detailed statistics\n", "info")
            else:
                error_msg = result.get('error', 'Unknown error')
                self.add_output(f"❌ Analysis failed: {error_msg}\n", "error")

        except Exception as e:
            self.add_output(f"❌ Error analyzing performance: {e}\n", "error")

    def _reset_timeouts(self):
        """Reset all model timeouts to base (320s)"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            self.add_output("🔄 Resetting all model timeouts to 320s...\n", "info")

            # Reset timeouts
            result = self.gui_parent.ollama_interface.reset_all_timeouts()

            if result.get('success'):
                reset_count = result.get('reset_count', 0)
                base_timeout = result.get('base_timeout', 320)

                self.add_output(f"✅ Reset {reset_count} model timeouts to {base_timeout}s\n", "success")
                self.add_output("💡 Timeouts will adapt dynamically based on actual performance\n", "info")
            else:
                error_msg = result.get('error', 'Unknown error')
                self.add_output(f"❌ Reset failed: {error_msg}\n", "error")

        except Exception as e:
            self.add_output(f"❌ Error resetting timeouts: {e}\n", "error")

    def _set_conservative_timeouts(self):
        """Set conservative timeouts based on model size"""
        try:
            if not self.gui_parent or not self.gui_parent.ollama_interface:
                self.add_output("❌ Ollama not connected\n", "error")
                return

            self.add_output("🛡️ Setting conservative timeouts based on model characteristics...\n", "info")

            # Set conservative timeouts
            result = self.gui_parent.ollama_interface.set_conservative_timeouts()

            if result.get('success'):
                updated_count = result.get('updated_count', 0)

                self.add_output(f"✅ Set conservative timeouts for {updated_count} models\n", "success")
                self.add_output("📊 Timeouts adjusted based on:\n", "info")
                self.add_output("   • Model size (GB)\n", "info")
                self.add_output("   • Model complexity\n", "info")
                self.add_output("   • Conservative buffers\n", "info")
                self.add_output("\n💡 Use 'model stats' to see current timeouts\n", "info")
            else:
                error_msg = result.get('error', 'Unknown error')
                self.add_output(f"❌ Failed to set conservative timeouts: {error_msg}\n", "error")

        except Exception as e:
            self.add_output(f"❌ Error setting conservative timeouts: {e}\n", "error")

    def _execute_tool_command(self, tool_name):
        """Execute a tool via command"""
        if not self.gui_parent or not self.gui_parent.agent_tools:
            self.add_output("❌ Agent tools not available\n", "error")
            return

        try:
            self.add_output(f"⚡ Executing tool: {tool_name}\n", "info")
            result = self.gui_parent.agent_tools.execute_tool(tool_name)

            if result.success:
                self.add_output("✅ Tool executed successfully:\n", "success")
                self.add_output(f"{result.output}\n", "info")

                if result.metadata:
                    self.add_output("📋 Additional Information:\n", "system")
                    for key, value in result.metadata.items():
                        self.add_output(f"  {key}: {value}\n", "system")
            else:
                self.add_output(f"❌ Tool execution failed: {result.error}\n", "error")

        except Exception as e:
            self.add_output(f"💥 Error executing tool: {e}\n", "error")

    def _show_all_tools(self):
        """Show all available tools categorized"""
        if not self.gui_parent or not self.gui_parent.agent_tools:
            self.add_output("❌ Agent tools not available\n", "error")
            return

        try:
            tools = self.gui_parent.agent_tools.get_available_tools()

            self.add_output("\n🛠️ CYBEX ENTERPRISE TOOLS CATALOG\n", "header")
            self.add_output("=" * 50 + "\n", "info")

            # Categorize tools
            categories = {
                "🔧 Core System": [
                    'get_system_info', 'list_processes', 'get_performance_metrics',
                    'get_disk_usage', 'get_memory_info', 'get_cpu_info', 'network_diagnostics'
                ],
                "🌐 Web & Network": [
                    'web_search', 'fetch_webpage', 'analyze_webpage', 'network_scan'
                ],
                "💾 Database": [
                    'connect_database', 'execute_query', 'backup_database', 'analyze_database'
                ],
                "🔒 Security": [
                    'security_audit', 'vulnerability_scan', 'firewall_analysis', 'malware_scan'
                ],
                "📊 Enterprise": [
                    'performance_analysis', 'enterprise_health_check', 'system_hardening'
                ],
                "📁 File Management": [
                    'analyze_file', 'compare_files', 'batch_rename', 'file_search', 'backup_files'
                ],
                "🚀 Development": [
                    'create_project', 'analyze_code', 'run_tests', 'git_operations', 'docker_management'
                ],
                "🤖 AI Integration": [
                    'ollama_chat', 'ollama_status', 'ollama_manage_model', 'ollama_generate'
                ]
            }

            for category, category_tools in categories.items():
                available_tools = [t for t in category_tools if t in tools]
                if available_tools:
                    self.add_output(f"\n{category} ({len(available_tools)} tools):\n", "accent")
                    for tool in available_tools:
                        self.add_output(f"  • {tool}\n", "info")

            # Show uncategorized tools
            categorized = set()
            for cat_tools in categories.values():
                categorized.update(cat_tools)

            uncategorized = [t for t in tools if t not in categorized]
            if uncategorized:
                self.add_output(f"\n🔧 Other Tools ({len(uncategorized)}):\n", "accent")
                for tool in uncategorized:
                    self.add_output(f"  • {tool}\n", "info")

            self.add_output(f"\n📈 Total Available Tools: {len(tools)}\n", "success")
            self.add_output("💡 Usage: execute <tool_name>\n", "system")
            self.add_output("💡 Quick commands: performance, memory, cpu, network, disk, processes\n", "system")

        except Exception as e:
            self.add_output(f"❌ Error showing tools: {e}\n", "error")

    def _show_system_dashboard(self):
        """Show comprehensive system dashboard"""
        self.add_output("\n🚀 CYBEX ENTERPRISE SYSTEM DASHBOARD\n", "header")
        self.add_output("=" * 60 + "\n", "info")

        # Execute multiple tools for comprehensive overview
        dashboard_tools = [
            ('get_system_info', '🖥️ System Information'),
            ('get_performance_metrics', '📊 Performance Metrics'),
            ('get_disk_usage', '💾 Storage Status'),
            ('network_diagnostics', '🌐 Network Status')
        ]

        for tool_name, description in dashboard_tools:
            self.add_output(f"\n{description}:\n", "accent")
            self._execute_tool_command(tool_name)

    def _show_system_status(self):
        """Show quick system status"""
        self.add_output("\n⚡ QUICK SYSTEM STATUS\n", "header")
        self.add_output("=" * 30 + "\n", "info")

        # Show key metrics quickly
        if self.gui_parent and self.gui_parent.agent_tools:
            try:
                # Get basic system info
                result = self.gui_parent.agent_tools.execute_tool('get_system_info')
                if result.success:
                    lines = result.output.split('\n')[:10]  # First 10 lines
                    for line in lines:
                        if line.strip():
                            self.add_output(f"{line}\n", "info")

                self.add_output("\n💡 For detailed info use: dashboard, performance, memory, cpu\n", "system")

            except Exception as e:
                self.add_output(f"❌ Error getting status: {e}\n", "error")
        else:
            self.add_output("❌ System tools not available\n", "error")

    def _get_command_suggestions(self, command):
        """Get intelligent command suggestions for typos/partial matches"""
        # All available commands
        all_commands = [
            # Basic commands
            'help', 'clear', 'cls', 'models', 'reload prompt',

            # Model commands
            'model stats', 'analyze models', 'reset timeouts', 'conservative timeouts',

            # Tool execution
            'execute', 'tools', 'dashboard', 'status',

            # Quick commands
            'performance', 'perf', 'metrics', 'memory', 'mem', 'ram',
            'cpu', 'processor', 'network', 'net', 'disk', 'storage',
            'processes', 'proc', 'ps',

            # System commands
            'system info', 'system status', 'health check'
        ]

        # Add available tools if agent_tools is available
        if self.gui_parent and self.gui_parent.agent_tools:
            try:
                tools = self.gui_parent.agent_tools.get_available_tools()
                all_commands.extend([f"execute {tool}" for tool in tools])
            except:
                pass

        command_lower = command.lower()
        suggestions = []

        # Exact partial matches
        for cmd in all_commands:
            if cmd.lower().startswith(command_lower) and cmd.lower() != command_lower:
                suggestions.append(cmd)

        # Fuzzy matching for typos
        if not suggestions:
            import difflib
            close_matches = difflib.get_close_matches(
                command_lower,
                [cmd.lower() for cmd in all_commands],
                n=3,
                cutoff=0.6
            )

            # Map back to original case
            cmd_map = {cmd.lower(): cmd for cmd in all_commands}
            suggestions = [cmd_map[match] for match in close_matches]

        return suggestions[:5]  # Return top 5 suggestions
    
    def start_output_monitor(self):
        """Start output monitoring"""
        def monitor():
            while True:
                try:
                    if not self.output_queue.empty():
                        text, tag = self.output_queue.get_nowait()
                        self.after(0, lambda t=text, tg=tag: self.add_output(t, tg))
                except:
                    pass
                time.sleep(0.1)
        
        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

class CybexSunriseProfessionalGUI:
    """CYBEX Enterprise - Sunrise Professional Edition

    Apple-level GUI with professional design system,
    advanced animations, and modern user experience.
    """

    def __init__(self):
        self.root = tk.Tk()
        self.core = None
        self.theme = SunriseProfessionalTheme()

        # Professional fonts system
        self.fonts = self.setup_professional_fonts()

        # Animation system
        self.animations = {}
        self.animation_queue = queue.Queue()

        # GUI variables
        self.model_var = tk.StringVar()
        self.available_models = []

        # Performance tracking variables
        self.tools_executed_count = 0
        self.last_executed_tool = "None"
        self.performance_history = []

        self.ollama_interface = None
        self.agent_tools = None

        # Initialize professional interface
        self.setup_professional_window()
        self.setup_professional_theme()
        self.initialize_core()
        self.setup_professional_ui()
        self.setup_professional_bindings()
        self.start_animation_system()

        # Start advanced monitoring
        self.start_professional_monitoring()

        # Load AI models after UI is ready
        self.root.after(3000, self.load_models_async)

    def setup_professional_fonts(self):
        """Setup professional font system"""
        try:
            fonts = {
                'title': font.Font(family="SF Pro Display", size=24, weight="bold"),
                'heading': font.Font(family="SF Pro Display", size=16, weight="bold"),
                'body': font.Font(family="SF Pro Display", size=12),
                'caption': font.Font(family="SF Pro Display", size=10),
                'mono': font.Font(family="SF Mono", size=11),
                'mono_small': font.Font(family="SF Mono", size=9)
            }
        except:
            # Fallback fonts
            fonts = {
                'title': font.Font(family="Segoe UI", size=24, weight="bold"),
                'heading': font.Font(family="Segoe UI", size=16, weight="bold"),
                'body': font.Font(family="Segoe UI", size=12),
                'caption': font.Font(family="Segoe UI", size=10),
                'mono': font.Font(family="Consolas", size=11),
                'mono_small': font.Font(family="Consolas", size=9)
            }
        return fonts

    def setup_professional_window(self):
        """Setup professional main window with modern styling"""
        self.root.title("CYBEX Enterprise - Sunrise Professional Edition")
        self.root.geometry("1600x1000")
        self.root.configure(bg=self.theme.BG_PRIMARY)
        self.root.minsize(1400, 800)

        # Modern window styling
        try:
            # Windows-specific styling
            self.root.wm_attributes('-alpha', 0.98)  # Slight transparency
        except:
            pass

        # Center window professionally
        self.root.update_idletasks()
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width // 2) - (1600 // 2)
        y = (screen_height // 2) - (1000 // 2)
        self.root.geometry(f"1600x1000+{x}+{y}")

        # Professional icon (if available)
        try:
            # Set window icon
            pass
        except:
            pass

    def setup_professional_theme(self):
        """Setup professional theme system"""
        style = ttk.Style()
        style.theme_use('clam')

        # Professional button styles
        style.configure(
            'Sunrise.TButton',
            background=self.theme.SUNRISE_ORANGE,
            foreground=self.theme.TEXT_PRIMARY,
            borderwidth=0,
            focuscolor='none',
            font=self.fonts['body']
        )

        style.map(
            'Sunrise.TButton',
            background=[
                ('active', self.theme.SUNRISE_GOLD),
                ('pressed', self.theme.SUNRISE_CORAL)
            ]
        )

        # Professional frame styles
        style.configure(
            'Sunrise.TFrame',
            background=self.theme.BG_SECONDARY,
            borderwidth=1,
            relief='flat'
        )

        # Professional label styles
        style.configure(
            'Sunrise.TLabel',
            background=self.theme.BG_SECONDARY,
            foreground=self.theme.TEXT_PRIMARY,
            font=self.fonts['body']
        )

        style.configure(
            'SunriseTitle.TLabel',
            background=self.theme.BG_PRIMARY,
            foreground=self.theme.SUNRISE_GOLD,
            font=self.fonts['title']
        )

        style.configure(
            'SunriseHeading.TLabel',
            background=self.theme.BG_SECONDARY,
            foreground=self.theme.SUNRISE_ORANGE,
            font=self.fonts['heading']
        )
    def start_animation_system(self):
        """Start professional animation system"""
        def animation_loop():
            while True:
                try:
                    if not self.animation_queue.empty():
                        animation = self.animation_queue.get_nowait()
                        self.execute_animation(animation)
                except:
                    pass
                time.sleep(0.016)  # 60 FPS

        animation_thread = threading.Thread(target=animation_loop, daemon=True)
        animation_thread.start()

    def execute_animation(self, animation):
        """Execute smooth animations"""
        # Professional animation system would go here
        pass

    def initialize_core(self):
        """Initialize Cybex core systems"""
        try:
            # Initialize core
            self.core = CybexCore()

            # Initialize Ollama interface
            self.ollama_interface = OllamaInterface(
                self.core.config_manager,
                self.core.log_manager
            )

            # Initialize agent tools
            self.agent_tools = AgentTools(self.core.log_manager)

            print("✅ Cybex core systems initialized successfully")

        except Exception as e:
            print(f"❌ Failed to initialize core systems: {e}")
            messagebox.showerror("Initialization Error", f"Failed to initialize Cybex core: {e}")

    def setup_professional_ui(self):
        """Setup professional UI with modern layout system"""
        # Professional main container with padding
        self.main_container = tk.Frame(
            self.root,
            bg=self.theme.BG_PRIMARY,
            relief=tk.FLAT,
            bd=0
        )
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Professional header with gradient-like effect
        self.setup_professional_header()

        # Modern content area with card-based layout
        self.content_container = tk.Frame(
            self.main_container,
            bg=self.theme.BG_PRIMARY,
            relief=tk.FLAT
        )
        self.content_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Setup professional layout
        self.setup_professional_layout()

    def setup_professional_header(self):
        """Setup professional header with Sunrise branding"""
        # Header container with gradient-like background
        header_frame = tk.Frame(
            self.main_container,
            bg=self.theme.BG_SECONDARY,
            height=80,
            relief=tk.FLAT
        )
        header_frame.pack(fill=tk.X, padx=0, pady=0)
        header_frame.pack_propagate(False)

        # Left side - Logo and title
        left_frame = tk.Frame(header_frame, bg=self.theme.BG_SECONDARY)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=30, pady=15)

        # Modern logo/icon
        logo_frame = tk.Frame(left_frame, bg=self.theme.SUNRISE_ORANGE, width=50, height=50)
        logo_frame.pack(side=tk.LEFT, padx=(0, 15))
        logo_frame.pack_propagate(False)

        logo_label = tk.Label(
            logo_frame,
            text="🌅",
            bg=self.theme.SUNRISE_ORANGE,
            fg=self.theme.TEXT_PRIMARY,
            font=("SF Pro Display", 24)
        )
        logo_label.pack(expand=True)

        # Title and subtitle
        title_frame = tk.Frame(left_frame, bg=self.theme.BG_SECONDARY)
        title_frame.pack(side=tk.LEFT, fill=tk.Y)

        title_label = tk.Label(
            title_frame,
            text="CYBEX Enterprise",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['title']
        )
        title_label.pack(anchor=tk.W)

        subtitle_label = tk.Label(
            title_frame,
            text="Sunrise Professional Edition",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['caption']
        )
        subtitle_label.pack(anchor=tk.W)

        # Right side - Status and controls
        right_frame = tk.Frame(header_frame, bg=self.theme.BG_SECONDARY)
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=30, pady=15)

        # Status indicator
        self.setup_status_indicator(right_frame)

        # Professional controls
        self.setup_header_controls(right_frame)

    def setup_status_indicator(self, parent):
        """Setup professional status indicator"""
        status_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        status_frame.pack(side=tk.RIGHT, padx=(0, 20))

        # Status dot
        self.status_dot = tk.Label(
            status_frame,
            text="●",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUCCESS,
            font=("SF Pro Display", 16)
        )
        self.status_dot.pack(side=tk.LEFT, padx=(0, 8))

        # Status text
        self.status_label = tk.Label(
            status_frame,
            text="System Ready",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.status_label.pack(side=tk.LEFT)

    def setup_header_controls(self, parent):
        """Setup professional header controls"""
        controls_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        controls_frame.pack(side=tk.RIGHT)

        # Model selector with modern styling
        model_frame = tk.Frame(controls_frame, bg=self.theme.BG_SECONDARY)
        model_frame.pack(side=tk.LEFT, padx=(0, 15))

        model_label = tk.Label(
            model_frame,
            text="AI Model:",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_TERTIARY,
            font=self.fonts['caption']
        )
        model_label.pack(anchor=tk.W)

        self.model_selector = ttk.Combobox(
            model_frame,
            textvariable=self.model_var,
            state="readonly",
            width=20,
            font=self.fonts['body']
        )
        self.model_selector.pack()
        self.model_selector.bind('<<ComboboxSelected>>', self.on_model_change)

        # Professional action buttons
        self.setup_action_buttons(controls_frame)

    def setup_action_buttons(self, parent):
        """Setup professional action buttons"""
        buttons_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        buttons_frame.pack(side=tk.LEFT)

        # Refresh button
        refresh_btn = tk.Button(
            buttons_frame,
            text="🔄",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['body'],
            relief=tk.FLAT,
            bd=0,
            padx=12,
            pady=6,
            cursor="hand2",
            command=self.refresh_models
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 8))

        # Settings button
        settings_btn = tk.Button(
            buttons_frame,
            text="⚙️",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['body'],
            relief=tk.FLAT,
            bd=0,
            padx=12,
            pady=6,
            cursor="hand2",
            command=self.open_settings
        )
        settings_btn.pack(side=tk.LEFT)

    def setup_professional_layout(self):
        """Setup advanced modular layout with organized panels"""
        # Create main layout container
        self.main_layout = tk.Frame(self.content_container, bg=self.theme.BG_PRIMARY)
        self.main_layout.pack(fill=tk.BOTH, expand=True)

        # Create organized panel system
        self.setup_advanced_panel_system()

        # Setup dynamic content switching
        self.setup_content_switching_system()

    def setup_advanced_panel_system(self):
        """Setup advanced modular panel system"""
        # Left Control Panel (300px)
        self.left_panel = tk.Frame(
            self.main_layout,
            bg=self.theme.BG_SECONDARY,
            width=300,
            relief=tk.FLAT,
            bd=1
        )
        self.left_panel.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 2))
        self.left_panel.pack_propagate(False)

        # Center Content Area (flexible)
        self.center_panel = tk.Frame(
            self.main_layout,
            bg=self.theme.BG_PRIMARY,
            relief=tk.FLAT
        )
        self.center_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2)

        # Right Info Panel (250px)
        self.right_panel = tk.Frame(
            self.main_layout,
            bg=self.theme.BG_SECONDARY,
            width=250,
            relief=tk.FLAT,
            bd=1
        )
        self.right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(2, 0))
        self.right_panel.pack_propagate(False)

        # Setup each panel
        self.setup_left_control_panel()
        self.setup_center_content_panel()
        self.setup_right_info_panel()

    def setup_left_control_panel(self):
        """Setup organized left control panel"""
        # Panel header
        header_frame = tk.Frame(self.left_panel, bg=self.theme.BG_SECONDARY, height=50)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 0))
        header_frame.pack_propagate(False)

        header_label = tk.Label(
            header_frame,
            text="🎛️ CONTROL CENTER",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['heading']
        )
        header_label.pack(anchor=tk.W, pady=(10, 0))

        # Create scrollable content
        canvas = tk.Canvas(
            self.left_panel,
            bg=self.theme.BG_SECONDARY,
            highlightthickness=0
        )
        scrollbar = ttk.Scrollbar(self.left_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme.BG_SECONDARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=(15, 0), pady=15)
        scrollbar.pack(side="right", fill="y", padx=(0, 15), pady=15)

        # Setup organized sections
        self.setup_navigation_section(scrollable_frame)
        self.setup_tools_section(scrollable_frame)
        self.setup_ai_section(scrollable_frame)
        self.setup_system_section(scrollable_frame)

    def setup_navigation_section(self, parent):
        """Setup improved navigation section with better organization"""
        # Section header with modern styling
        nav_header = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=40)
        nav_header.pack(fill=tk.X, pady=(0, 15))
        nav_header.pack_propagate(False)

        header_label = tk.Label(
            nav_header,
            text="🧭 MAIN NAVIGATION",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['body']
        )
        header_label.pack(anchor=tk.W, pady=(8, 0))

        # Primary navigation (most used)
        primary_nav = [
            ("💬", "AI Assistant", "primary", self.show_chat_view),
            ("🛠️", "Enterprise Tools", "tools", self.show_tools_dashboard),
            ("📊", "System Dashboard", "analytics", self.show_analytics_dashboard),
            ("🔧", "System Monitor", "system", self.show_system_monitor)
        ]

        # Create primary navigation buttons
        for icon, text, category, command in primary_nav:
            self.create_improved_nav_button(parent, icon, text, category, command, is_primary=True)

        # Separator
        separator = tk.Frame(parent, bg=self.theme.BORDER_SUBTLE, height=1)
        separator.pack(fill=tk.X, pady=10)

        # Secondary navigation header
        secondary_header = tk.Label(
            parent,
            text="🔧 SPECIALIZED TOOLS",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_TERTIARY,
            font=self.fonts['caption']
        )
        secondary_header.pack(anchor=tk.W, pady=(5, 10))

        # Secondary navigation (specialized)
        secondary_nav = [
            ("🌐", "Web Development", "web", self.show_web_tools),
            ("💾", "Database Tools", "database", self.show_database_tools),
            ("🔒", "Security Center", "security", self.show_security_center),
            ("⚙️", "Advanced Settings", "settings", self.show_advanced_settings)
        ]

        # Create secondary navigation buttons (more compact)
        for icon, text, category, command in secondary_nav:
            self.create_improved_nav_button(parent, icon, text, category, command, is_primary=False)

    def create_organized_nav_button(self, parent, icon, text, category, command):
        """Create organized navigation button with category styling"""
        btn_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        btn_frame.pack(fill=tk.X, pady=2)

        # Category color mapping
        category_colors = {
            "primary": self.theme.SUNRISE_ORANGE,
            "tools": self.theme.SUNRISE_GOLD,
            "analytics": self.theme.SUNRISE_PINK,
            "system": self.theme.SUNRISE_CORAL,
            "web": self.theme.SUNRISE_PEACH,
            "database": self.theme.SUNRISE_AMBER,
            "security": "#FF4444",
            "settings": "#888888"
        }

        btn = tk.Button(
            btn_frame,
            text=f"{icon}  {text}",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['body'],
            relief=tk.FLAT,
            bd=0,
            padx=15,
            pady=10,
            cursor="hand2",
            anchor=tk.W,
            command=command
        )
        btn.pack(fill=tk.X)

        # Category indicator
        indicator = tk.Frame(btn_frame, bg=category_colors.get(category, self.theme.SUNRISE_ORANGE), height=2)
        indicator.pack(fill=tk.X)

        # Advanced hover effects
        def on_enter(e):
            btn.configure(bg=category_colors.get(category, self.theme.SUNRISE_ORANGE))
            indicator.configure(height=4)

        def on_leave(e):
            btn.configure(bg=self.theme.BG_TERTIARY)
            indicator.configure(height=2)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

    def create_improved_nav_button(self, parent, icon, text, category, command, is_primary=True):
        """Create improved navigation button with better styling"""
        # Button container
        btn_container = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        btn_container.pack(fill=tk.X, pady=3 if is_primary else 2)

        # Category color mapping with better colors
        category_colors = {
            "primary": self.theme.SUNRISE_ORANGE,
            "tools": self.theme.SUNRISE_GOLD,
            "analytics": self.theme.SUNRISE_PINK,
            "system": self.theme.SUNRISE_CORAL,
            "web": self.theme.SUNRISE_PEACH,
            "database": self.theme.SUNRISE_AMBER,
            "security": "#FF6B6B",
            "settings": "#95A5A6"
        }

        # Main button with improved styling
        btn = tk.Button(
            btn_container,
            text=f"{icon}  {text}",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['body'] if is_primary else self.fonts['caption'],
            relief=tk.FLAT,
            bd=0,
            padx=20 if is_primary else 15,
            pady=12 if is_primary else 8,
            cursor="hand2",
            anchor=tk.W,
            command=command
        )
        btn.pack(fill=tk.X)

        # Active indicator (left border)
        indicator = tk.Frame(
            btn_container,
            bg=category_colors.get(category, self.theme.SUNRISE_ORANGE),
            width=4
        )
        indicator.pack(side=tk.LEFT, fill=tk.Y)

        # Store button reference for active state management
        if not hasattr(self, 'nav_buttons'):
            self.nav_buttons = {}
        self.nav_buttons[category] = {'button': btn, 'indicator': indicator, 'color': category_colors.get(category)}

        # Enhanced hover effects
        def on_enter(e):
            btn.configure(
                bg=category_colors.get(category, self.theme.SUNRISE_ORANGE),
                fg=self.theme.TEXT_PRIMARY
            )
            indicator.configure(width=6)
            # Add subtle glow effect
            btn_container.configure(relief=tk.RAISED, bd=1)

        def on_leave(e):
            btn.configure(
                bg=self.theme.BG_TERTIARY,
                fg=self.theme.TEXT_PRIMARY
            )
            indicator.configure(width=4)
            btn_container.configure(relief=tk.FLAT, bd=0)

        def on_click(e):
            # Set as active
            self.set_active_nav_button(category)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        btn.bind("<Button-1>", on_click)

    def set_active_nav_button(self, active_category):
        """Set active navigation button"""
        if not hasattr(self, 'nav_buttons'):
            return

        for category, elements in self.nav_buttons.items():
            if category == active_category:
                # Set as active
                elements['button'].configure(
                    bg=elements['color'],
                    fg=self.theme.TEXT_PRIMARY
                )
                elements['indicator'].configure(width=6)
            else:
                # Set as inactive
                elements['button'].configure(
                    bg=self.theme.BG_TERTIARY,
                    fg=self.theme.TEXT_PRIMARY
                )
                elements['indicator'].configure(width=4)

    def setup_tools_section(self, parent):
        """Setup improved quick tools section with better visibility"""
        # Section separator with gradient effect
        separator_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=20)
        separator_frame.pack(fill=tk.X, pady=15)
        separator_frame.pack_propagate(False)

        separator = tk.Frame(separator_frame, bg=self.theme.SUNRISE_ORANGE, height=2)
        separator.pack(fill=tk.X, pady=9)

        # Section header with improved styling
        tools_header = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=35)
        tools_header.pack(fill=tk.X, pady=(0, 15))
        tools_header.pack_propagate(False)

        header_label = tk.Label(
            tools_header,
            text="⚡ QUICK ACCESS TOOLS",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['body']
        )
        header_label.pack(anchor=tk.W, pady=(5, 0))

        # Tools container with background
        tools_container = tk.Frame(
            parent,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            bd=1
        )
        tools_container.pack(fill=tk.X, pady=(0, 15))

        # Quick access tools - better organized
        quick_tools = [
            ("🔍", "System", "get_system_info", self.theme.SUNRISE_ORANGE),
            ("📈", "Performance", "get_performance_metrics", self.theme.SUNRISE_GOLD),
            ("💾", "Memory", "get_memory_info", self.theme.SUNRISE_PINK),
            ("🖥️", "CPU", "get_cpu_info", self.theme.SUNRISE_CORAL),
            ("🌐", "Network", "network_diagnostics", self.theme.SUNRISE_PEACH),
            ("💿", "Storage", "get_disk_usage", self.theme.SUNRISE_AMBER)
        ]

        # Create tools in a 2x3 grid with better styling
        tools_grid = tk.Frame(tools_container, bg=self.theme.BG_TERTIARY)
        tools_grid.pack(fill=tk.X, padx=8, pady=8)

        for i, (icon, name, tool, color) in enumerate(quick_tools):
            row = i // 2
            col = i % 2

            # Tool button with improved design
            tool_btn = tk.Button(
                tools_grid,
                text=f"{icon}\n{name}",
                bg=self.theme.BG_SECONDARY,
                fg=self.theme.TEXT_PRIMARY,
                font=self.fonts['caption'],
                relief=tk.FLAT,
                bd=1,
                padx=12,
                pady=10,
                cursor="hand2",
                command=lambda t=tool: self.execute_quick_tool(t),
                width=12,
                height=3
            )
            tool_btn.grid(row=row, column=col, padx=3, pady=3, sticky="ew")

            # Configure grid weights for equal distribution
            tools_grid.grid_columnconfigure(col, weight=1)

            # Enhanced hover effects with color coding
            def make_enhanced_hover_handler(btn, tool_name, accent_color):
                def on_enter(e):
                    btn.configure(
                        bg=accent_color,
                        fg=self.theme.TEXT_PRIMARY,
                        relief=tk.RAISED,
                        bd=2
                    )
                def on_leave(e):
                    btn.configure(
                        bg=self.theme.BG_SECONDARY,
                        fg=self.theme.TEXT_PRIMARY,
                        relief=tk.FLAT,
                        bd=1
                    )
                def on_click(e):
                    btn.configure(bg=self.theme.SUNRISE_GOLD)
                    parent.after(100, lambda: btn.configure(bg=accent_color))

                return on_enter, on_leave, on_click

            enter_handler, leave_handler, click_handler = make_enhanced_hover_handler(tool_btn, tool, color)
            tool_btn.bind("<Enter>", enter_handler)
            tool_btn.bind("<Leave>", leave_handler)
            tool_btn.bind("<Button-1>", click_handler)

        # Tools info footer
        tools_info = tk.Label(
            tools_container,
            text="💡 Click any tool for instant execution",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_MUTED,
            font=("Segoe UI", 8)
        )
        tools_info.pack(pady=(0, 8))

    def setup_ai_section(self, parent):
        """Setup improved AI management section"""
        # Section separator with AI theme
        separator_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=20)
        separator_frame.pack(fill=tk.X, pady=15)
        separator_frame.pack_propagate(False)

        separator = tk.Frame(separator_frame, bg=self.theme.SUNRISE_PINK, height=2)
        separator.pack(fill=tk.X, pady=9)

        # Section header with AI styling
        ai_header = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=35)
        ai_header.pack(fill=tk.X, pady=(0, 15))
        ai_header.pack_propagate(False)

        header_label = tk.Label(
            ai_header,
            text="🤖 AI MANAGEMENT CENTER",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_PINK,
            font=self.fonts['body']
        )
        header_label.pack(anchor=tk.W, pady=(5, 0))

        # AI container with background
        ai_container = tk.Frame(
            parent,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            bd=1
        )
        ai_container.pack(fill=tk.X, pady=(0, 15))

        # Model selector section
        model_section = tk.Frame(ai_container, bg=self.theme.BG_TERTIARY)
        model_section.pack(fill=tk.X, padx=12, pady=(12, 8))

        model_label = tk.Label(
            model_section,
            text="🎯 Active AI Model:",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['caption']
        )
        model_label.pack(anchor=tk.W, pady=(0, 5))

        # Model selector with improved styling
        selector_frame = tk.Frame(model_section, bg=self.theme.BG_TERTIARY)
        selector_frame.pack(fill=tk.X)

        self.ai_model_selector = ttk.Combobox(
            selector_frame,
            textvariable=self.model_var,
            state="readonly",
            width=28,
            font=self.fonts['caption']
        )
        self.ai_model_selector.pack(fill=tk.X)
        self.ai_model_selector.bind('<<ComboboxSelected>>', self.on_model_change)

        # AI control buttons with better organization
        controls_frame = tk.Frame(ai_container, bg=self.theme.BG_TERTIARY)
        controls_frame.pack(fill=tk.X, padx=12, pady=(8, 12))

        ai_controls = [
            ("🔄", "Refresh", self.refresh_models, self.theme.SUNRISE_ORANGE),
            ("📊", "Statistics", self.show_model_stats, self.theme.SUNRISE_GOLD),
            ("⚡", "Reset Timeouts", self.reset_all_timeouts, self.theme.SUNRISE_CORAL),
            ("🛡️", "Conservative", self.analyze_all_models, self.theme.SUNRISE_PEACH)
        ]

        # Create AI control buttons in 2x2 grid
        for i, (icon, text, command, color) in enumerate(ai_controls):
            row = i // 2
            col = i % 2

            btn = tk.Button(
                controls_frame,
                text=f"{icon}\n{text}",
                bg=self.theme.BG_SECONDARY,
                fg=self.theme.TEXT_PRIMARY,
                font=self.fonts['caption'],
                relief=tk.FLAT,
                bd=1,
                padx=8,
                pady=8,
                cursor="hand2",
                command=command,
                width=10,
                height=2
            )
            btn.grid(row=row, column=col, padx=2, pady=2, sticky="ew")

            # Configure grid weights
            controls_frame.grid_columnconfigure(col, weight=1)

            # Enhanced hover effects with AI colors
            def make_ai_hover_handler(btn, accent_color):
                def on_enter(e):
                    btn.configure(
                        bg=accent_color,
                        fg=self.theme.TEXT_PRIMARY,
                        relief=tk.RAISED,
                        bd=2
                    )
                def on_leave(e):
                    btn.configure(
                        bg=self.theme.BG_SECONDARY,
                        fg=self.theme.TEXT_PRIMARY,
                        relief=tk.FLAT,
                        bd=1
                    )
                return on_enter, on_leave

            enter_handler, leave_handler = make_ai_hover_handler(btn, color)
            btn.bind("<Enter>", enter_handler)
            btn.bind("<Leave>", leave_handler)

        # AI status info
        ai_info = tk.Label(
            ai_container,
            text="🧠 AI models loaded and ready",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_MUTED,
            font=("Segoe UI", 8)
        )
        ai_info.pack(pady=(0, 8))

    def setup_system_section(self, parent):
        """Setup improved system monitoring section"""
        # Section separator with system theme
        separator_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=20)
        separator_frame.pack(fill=tk.X, pady=15)
        separator_frame.pack_propagate(False)

        separator = tk.Frame(separator_frame, bg=self.theme.SUNRISE_CORAL, height=2)
        separator.pack(fill=tk.X, pady=9)

        # Section header with system styling
        system_header = tk.Frame(parent, bg=self.theme.BG_SECONDARY, height=35)
        system_header.pack(fill=tk.X, pady=(0, 15))
        system_header.pack_propagate(False)

        header_label = tk.Label(
            system_header,
            text="⚡ SYSTEM MONITORING",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_CORAL,
            font=self.fonts['body']
        )
        header_label.pack(anchor=tk.W, pady=(5, 0))

        # System container with background
        system_container = tk.Frame(
            parent,
            bg=self.theme.BG_TERTIARY,
            relief=tk.FLAT,
            bd=1
        )
        system_container.pack(fill=tk.X, pady=(0, 15))

        # System status indicators with improved design
        self.setup_improved_system_indicators(system_container)

    def setup_improved_system_indicators(self, parent):
        """Setup improved system status indicators"""
        indicators_frame = tk.Frame(parent, bg=self.theme.BG_TERTIARY)
        indicators_frame.pack(fill=tk.X, padx=12, pady=12)

        # Status indicators with modern design
        status_items = [
            ("🟢", "System Core", "Online", self.theme.SUCCESS),
            ("🤖", "AI Engine", "Ready", self.theme.SUNRISE_PINK),
            ("🛠️", "Enterprise Tools", "Active", self.theme.SUNRISE_GOLD),
            ("🌐", "Network", "Connected", self.theme.INFO)
        ]

        for i, (icon, system, status, color) in enumerate(status_items):
            # Indicator container
            indicator_container = tk.Frame(indicators_frame, bg=self.theme.BG_SECONDARY, relief=tk.FLAT, bd=1)
            indicator_container.pack(fill=tk.X, pady=2)

            # Left side - icon and system
            left_frame = tk.Frame(indicator_container, bg=self.theme.BG_SECONDARY)
            left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=8, pady=6)

            system_label = tk.Label(
                left_frame,
                text=f"{icon} {system}",
                bg=self.theme.BG_SECONDARY,
                fg=self.theme.TEXT_PRIMARY,
                font=self.fonts['caption']
            )
            system_label.pack(side=tk.LEFT)

            # Right side - status
            right_frame = tk.Frame(indicator_container, bg=self.theme.BG_SECONDARY)
            right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=8, pady=6)

            status_label = tk.Label(
                right_frame,
                text=status,
                bg=self.theme.BG_SECONDARY,
                fg=color,
                font=self.fonts['caption']
            )
            status_label.pack(side=tk.RIGHT)

            # Status indicator dot
            dot_frame = tk.Frame(indicator_container, bg=color, width=4)
            dot_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # System info footer
        system_info = tk.Label(
            parent,
            text="📊 Real-time system monitoring active",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_MUTED,
            font=("Segoe UI", 8)
        )
        system_info.pack(pady=(0, 8))

    def execute_quick_tool(self, tool_name):
        """Execute quick tool and show in terminal"""
        if hasattr(self, 'terminal') and self.terminal:
            self.terminal._execute_tool_command(tool_name)

    def setup_center_content_panel(self):
        """Setup advanced center content panel"""
        # Content header with tabs
        self.content_header = tk.Frame(
            self.center_panel,
            bg=self.theme.BG_SECONDARY,
            height=50,
            relief=tk.FLAT
        )
        self.content_header.pack(fill=tk.X, padx=0, pady=0)
        self.content_header.pack_propagate(False)

        # Tab system for content switching
        self.setup_content_tabs()

        # Main content area with terminal
        self.content_area = tk.Frame(
            self.center_panel,
            bg=self.theme.BG_PRIMARY,
            relief=tk.FLAT
        )
        self.content_area.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)

        # Setup terminal in content area
        self.terminal = SunriseProfessionalTerminal(self.content_area)
        self.terminal.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        self.terminal.gui_parent = self

    def setup_content_tabs(self):
        """Setup content switching tabs"""
        tabs_frame = tk.Frame(self.content_header, bg=self.theme.BG_SECONDARY)
        tabs_frame.pack(side=tk.LEFT, fill=tk.Y, padx=15, pady=10)

        self.active_tab = "terminal"

        tabs = [
            ("💻", "Terminal", "terminal"),
            ("📊", "Dashboard", "dashboard"),
            ("🛠️", "Tools", "tools"),
            ("📈", "Analytics", "analytics")
        ]

        self.tab_buttons = {}

        for icon, text, tab_id in tabs:
            btn = tk.Button(
                tabs_frame,
                text=f"{icon} {text}",
                bg=self.theme.BG_TERTIARY if tab_id != self.active_tab else self.theme.SUNRISE_ORANGE,
                fg=self.theme.TEXT_PRIMARY,
                font=self.fonts['caption'],
                relief=tk.FLAT,
                bd=0,
                padx=12,
                pady=6,
                cursor="hand2",
                command=lambda t=tab_id: self.switch_content_tab(t)
            )
            btn.pack(side=tk.LEFT, padx=2)

            self.tab_buttons[tab_id] = btn

        # Content info
        info_frame = tk.Frame(self.content_header, bg=self.theme.BG_SECONDARY)
        info_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=15, pady=10)

        self.content_info_label = tk.Label(
            info_frame,
            text="🌅 CYBEX Enterprise Terminal",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['caption']
        )
        self.content_info_label.pack(side=tk.RIGHT, pady=8)

    def switch_content_tab(self, tab_id):
        """Switch content tab"""
        # Update active tab
        self.active_tab = tab_id

        # Update button styles
        for tid, btn in self.tab_buttons.items():
            if tid == tab_id:
                btn.configure(bg=self.theme.SUNRISE_ORANGE)
            else:
                btn.configure(bg=self.theme.BG_TERTIARY)

        # Update content info
        tab_info = {
            "terminal": "🌅 CYBEX Enterprise Terminal",
            "dashboard": "📊 System Dashboard",
            "tools": "🛠️ Enterprise Tools Hub",
            "analytics": "📈 Performance Analytics"
        }

        self.content_info_label.configure(text=tab_info.get(tab_id, "🌅 CYBEX Enterprise"))

        # Switch content (for now just update terminal)
        if tab_id == "dashboard":
            if hasattr(self, 'terminal') and self.terminal:
                self.terminal._show_system_dashboard()
        elif tab_id == "tools":
            if hasattr(self, 'terminal') and self.terminal:
                self.terminal._show_all_tools()
        elif tab_id == "analytics":
            if hasattr(self, 'terminal') and self.terminal:
                self.terminal.add_output("\n📈 ANALYTICS DASHBOARD\n", "header")
                self.terminal._execute_tool_command("get_performance_metrics")

    def setup_right_info_panel(self):
        """Setup organized right information panel"""
        # Panel header
        header_frame = tk.Frame(self.right_panel, bg=self.theme.BG_SECONDARY, height=50)
        header_frame.pack(fill=tk.X, padx=15, pady=(15, 0))
        header_frame.pack_propagate(False)

        header_label = tk.Label(
            header_frame,
            text="📊 SYSTEM INFO",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['heading']
        )
        header_label.pack(anchor=tk.W, pady=(10, 0))

        # Create scrollable content
        canvas = tk.Canvas(
            self.right_panel,
            bg=self.theme.BG_SECONDARY,
            highlightthickness=0
        )
        scrollbar = ttk.Scrollbar(self.right_panel, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.theme.BG_SECONDARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True, padx=(15, 0), pady=15)
        scrollbar.pack(side="right", fill="y", padx=(0, 15), pady=15)

        # Setup info sections
        self.setup_performance_info(scrollable_frame)
        self.setup_tools_info(scrollable_frame)
        self.setup_ai_info(scrollable_frame)

    def setup_performance_info(self, parent):
        """Setup real-time performance information"""
        # Performance section
        perf_frame = tk.Frame(parent, bg=self.theme.BG_TERTIARY, relief=tk.FLAT)
        perf_frame.pack(fill=tk.X, pady=(0, 15))

        # Section header
        tk.Label(
            perf_frame,
            text="⚡ REAL-TIME PERFORMANCE",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.SUNRISE_ORANGE,
            font=self.fonts['caption']
        ).pack(anchor=tk.W, padx=12, pady=(10, 5))

        # Performance metrics
        metrics_frame = tk.Frame(perf_frame, bg=self.theme.BG_TERTIARY)
        metrics_frame.pack(fill=tk.X, padx=12, pady=(0, 10))

        # CPU Usage
        self.cpu_info_label = tk.Label(
            metrics_frame,
            text="🖥️ CPU: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.cpu_info_label.pack(anchor=tk.W, pady=1)

        # Memory Usage
        self.memory_info_label = tk.Label(
            metrics_frame,
            text="💾 Memory: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.memory_info_label.pack(anchor=tk.W, pady=1)

        # Network Activity
        self.network_info_label = tk.Label(
            metrics_frame,
            text="🌐 Network: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.network_info_label.pack(anchor=tk.W, pady=1)

        # Performance chart
        self.perf_chart_label = tk.Label(
            metrics_frame,
            text="📊 ▁▂▃▄▅▆▇█",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.SUNRISE_ORANGE,
            font=("Consolas", 8)
        )
        self.perf_chart_label.pack(anchor=tk.W, pady=(5, 0))

    def setup_tools_info(self, parent):
        """Setup tools information section"""
        # Tools section
        tools_frame = tk.Frame(parent, bg=self.theme.BG_TERTIARY, relief=tk.FLAT)
        tools_frame.pack(fill=tk.X, pady=(0, 15))

        # Section header
        tk.Label(
            tools_frame,
            text="🛠️ TOOLS STATUS",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.SUNRISE_GOLD,
            font=self.fonts['caption']
        ).pack(anchor=tk.W, padx=12, pady=(10, 5))

        # Tools metrics
        tools_metrics_frame = tk.Frame(tools_frame, bg=self.theme.BG_TERTIARY)
        tools_metrics_frame.pack(fill=tk.X, padx=12, pady=(0, 10))

        # Total tools
        self.total_tools_label = tk.Label(
            tools_metrics_frame,
            text="📦 Total Tools: 60",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.total_tools_label.pack(anchor=tk.W, pady=1)

        # Tools executed
        self.tools_executed_info_label = tk.Label(
            tools_metrics_frame,
            text="⚡ Executed: 0",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.tools_executed_info_label.pack(anchor=tk.W, pady=1)

        # Last tool
        self.last_tool_info_label = tk.Label(
            tools_metrics_frame,
            text="🔧 Last: None",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.last_tool_info_label.pack(anchor=tk.W, pady=1)

        # Success rate
        self.tools_success_label = tk.Label(
            tools_metrics_frame,
            text="✅ Success: 100%",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.SUCCESS,
            font=self.fonts['caption']
        )
        self.tools_success_label.pack(anchor=tk.W, pady=1)

    def setup_ai_info(self, parent):
        """Setup AI information section"""
        # AI section
        ai_frame = tk.Frame(parent, bg=self.theme.BG_TERTIARY, relief=tk.FLAT)
        ai_frame.pack(fill=tk.X, pady=(0, 15))

        # Section header
        tk.Label(
            ai_frame,
            text="🤖 AI STATUS",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.SUNRISE_PINK,
            font=self.fonts['caption']
        ).pack(anchor=tk.W, padx=12, pady=(10, 5))

        # AI metrics
        ai_metrics_frame = tk.Frame(ai_frame, bg=self.theme.BG_TERTIARY)
        ai_metrics_frame.pack(fill=tk.X, padx=12, pady=(0, 10))

        # Current model
        self.current_model_info_label = tk.Label(
            ai_metrics_frame,
            text="🎯 Model: Loading...",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.current_model_info_label.pack(anchor=tk.W, pady=1)

        # Response time
        self.ai_response_time_label = tk.Label(
            ai_metrics_frame,
            text="⚡ Response: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.ai_response_time_label.pack(anchor=tk.W, pady=1)

        # Success rate
        self.ai_success_rate_label = tk.Label(
            ai_metrics_frame,
            text="✅ Success: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.ai_success_rate_label.pack(anchor=tk.W, pady=1)

        # Timeout
        self.ai_timeout_label = tk.Label(
            ai_metrics_frame,
            text="⏰ Timeout: 320s",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.ai_timeout_label.pack(anchor=tk.W, pady=1)

    def setup_system_indicators(self, parent):
        """Setup system status indicators"""
        # System indicators frame
        indicators_frame = tk.Frame(parent, bg=self.theme.BG_TERTIARY, relief=tk.FLAT)
        indicators_frame.pack(fill=tk.X, pady=(0, 10))

        # Status indicators
        status_items = [
            ("🟢", "System", "Online"),
            ("🟡", "AI", "Ready"),
            ("🔵", "Tools", "Active"),
            ("🟠", "Network", "Connected")
        ]

        for color, system, status in status_items:
            indicator_frame = tk.Frame(indicators_frame, bg=self.theme.BG_TERTIARY)
            indicator_frame.pack(fill=tk.X, padx=12, pady=2)

            tk.Label(
                indicator_frame,
                text=f"{color} {system}: {status}",
                bg=self.theme.BG_TERTIARY,
                fg=self.theme.TEXT_SECONDARY,
                font=self.fonts['caption']
            ).pack(anchor=tk.W)

    def setup_content_switching_system(self):
        """Setup dynamic content switching system"""
        # Initialize content views
        self.content_views = {
            "terminal": self.terminal if hasattr(self, 'terminal') else None,
            "dashboard": None,
            "tools": None,
            "analytics": None
        }

        # Set default view
        self.current_view = "terminal"

    def setup_professional_sidebar(self, parent):
        """Setup professional sidebar with navigation"""
        # Sidebar container
        sidebar_frame = tk.Frame(
            parent,
            bg=self.theme.BG_SECONDARY,
            width=300,
            relief=tk.FLAT
        )
        sidebar_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 20))
        sidebar_frame.pack_propagate(False)

        # Sidebar header
        sidebar_header = tk.Frame(sidebar_frame, bg=self.theme.BG_SECONDARY, height=60)
        sidebar_header.pack(fill=tk.X, padx=20, pady=(20, 0))
        sidebar_header.pack_propagate(False)

        sidebar_title = tk.Label(
            sidebar_header,
            text="AI Assistant",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.SUNRISE_ORANGE,
            font=self.fonts['heading']
        )
        sidebar_title.pack(anchor=tk.W, pady=(15, 0))

        # Navigation menu
        self.setup_navigation_menu(sidebar_frame)

        # Performance monitor
        self.setup_performance_monitor(sidebar_frame)

    def setup_navigation_menu(self, parent):
        """Setup professional navigation menu with enterprise tools integration"""
        nav_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        nav_frame.pack(fill=tk.X, padx=20, pady=20)

        nav_title = tk.Label(
            nav_frame,
            text="Enterprise Navigation",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_TERTIARY,
            font=self.fonts['caption']
        )
        nav_title.pack(anchor=tk.W, pady=(0, 10))

        # Advanced navigation with tool categories
        nav_items = [
            ("💬", "AI Chat", self.show_chat_view),
            ("🛠️", "Enterprise Tools", self.show_tools_dashboard),
            ("📊", "Performance Analytics", self.show_analytics_dashboard),
            ("🔧", "System Monitor", self.show_system_monitor),
            ("🌐", "Web Tools", self.show_web_tools),
            ("💾", "Database Tools", self.show_database_tools),
            ("🔒", "Security Center", self.show_security_center),
            ("⚙️", "Advanced Settings", self.show_advanced_settings)
        ]

        for icon, text, command in nav_items:
            self.create_advanced_nav_button(nav_frame, icon, text, command)

        # Tools quick access
        self.setup_tools_quick_access(nav_frame)

    def create_advanced_nav_button(self, parent, icon, text, command):
        """Create advanced navigation button with enterprise styling"""
        btn_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        btn_frame.pack(fill=tk.X, pady=3)

        # Main button with gradient-like effect
        btn = tk.Button(
            btn_frame,
            text=f"{icon}  {text}",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['body'],
            relief=tk.FLAT,
            bd=0,
            padx=20,
            pady=12,
            cursor="hand2",
            anchor=tk.W,
            command=command
        )
        btn.pack(fill=tk.X)

        # Advanced hover effects with animations
        def on_enter(e):
            btn.configure(
                bg=self.theme.SUNRISE_ORANGE,
                fg=self.theme.TEXT_PRIMARY
            )
            # Add subtle animation effect
            self.animate_button_glow(btn)

        def on_leave(e):
            btn.configure(
                bg=self.theme.BG_TERTIARY,
                fg=self.theme.TEXT_PRIMARY
            )

        def on_click(e):
            btn.configure(bg=self.theme.SUNRISE_GOLD)
            self.root.after(100, lambda: btn.configure(bg=self.theme.SUNRISE_ORANGE))

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)
        btn.bind("<Button-1>", on_click)

    def animate_button_glow(self, button):
        """Add subtle glow animation to buttons"""
        # Professional animation effect
        try:
            button.configure(relief=tk.RAISED, bd=1)
            self.root.after(200, lambda: button.configure(relief=tk.FLAT, bd=0))
        except:
            pass

    def setup_tools_quick_access(self, parent):
        """Setup quick access to most used enterprise tools"""
        tools_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        tools_frame.pack(fill=tk.X, pady=(20, 0))

        tools_title = tk.Label(
            tools_frame,
            text="Quick Tools",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_TERTIARY,
            font=self.fonts['caption']
        )
        tools_title.pack(anchor=tk.W, pady=(0, 10))

        # Quick access tools grid
        quick_tools = [
            ("🔍", "System Info", lambda: self.execute_tool("get_system_info")),
            ("📈", "Performance", lambda: self.execute_tool("get_performance_metrics")),
            ("🌐", "Network Test", lambda: self.execute_tool("network_diagnostics")),
            ("💾", "Disk Usage", lambda: self.execute_tool("get_disk_usage"))
        ]

        tools_grid = tk.Frame(tools_frame, bg=self.theme.BG_SECONDARY)
        tools_grid.pack(fill=tk.X)

        for i, (icon, name, command) in enumerate(quick_tools):
            row = i // 2
            col = i % 2

            tool_btn = tk.Button(
                tools_grid,
                text=f"{icon}\n{name}",
                bg=self.theme.BG_TERTIARY,
                fg=self.theme.TEXT_SECONDARY,
                font=self.fonts['caption'],
                relief=tk.FLAT,
                bd=0,
                padx=8,
                pady=8,
                cursor="hand2",
                command=command
            )
            tool_btn.grid(row=row, column=col, padx=2, pady=2, sticky="ew")

            # Configure grid weights
            tools_grid.grid_columnconfigure(col, weight=1)

            # Hover effects for tool buttons
            def make_hover_handler(btn):
                def on_enter(e):
                    btn.configure(bg=self.theme.SUNRISE_PEACH, fg=self.theme.TEXT_PRIMARY)
                def on_leave(e):
                    btn.configure(bg=self.theme.BG_TERTIARY, fg=self.theme.TEXT_SECONDARY)
                return on_enter, on_leave

            enter_handler, leave_handler = make_hover_handler(tool_btn)
            tool_btn.bind("<Enter>", enter_handler)
            tool_btn.bind("<Leave>", leave_handler)

    def setup_performance_monitor(self, parent):
        """Setup advanced real-time performance monitor"""
        perf_frame = tk.Frame(parent, bg=self.theme.BG_SECONDARY)
        perf_frame.pack(fill=tk.X, padx=20, pady=20)

        perf_title = tk.Label(
            perf_frame,
            text="Real-Time Performance",
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_TERTIARY,
            font=self.fonts['caption']
        )
        perf_title.pack(anchor=tk.W, pady=(0, 10))

        # Advanced performance metrics with visual indicators
        metrics_frame = tk.Frame(perf_frame, bg=self.theme.BG_TERTIARY, relief=tk.FLAT)
        metrics_frame.pack(fill=tk.X, pady=5)

        # AI Model Performance
        ai_frame = tk.Frame(metrics_frame, bg=self.theme.BG_TERTIARY)
        ai_frame.pack(fill=tk.X, padx=15, pady=8)

        self.response_time_label = tk.Label(
            ai_frame,
            text="⚡ Response Time: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.response_time_label.pack(anchor=tk.W)

        self.success_rate_label = tk.Label(
            ai_frame,
            text="✅ Success Rate: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.success_rate_label.pack(anchor=tk.W)

        # System Performance
        sys_frame = tk.Frame(metrics_frame, bg=self.theme.BG_TERTIARY)
        sys_frame.pack(fill=tk.X, padx=15, pady=(0, 8))

        self.cpu_usage_label = tk.Label(
            sys_frame,
            text="🖥️ CPU Usage: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.cpu_usage_label.pack(anchor=tk.W)

        self.memory_usage_label = tk.Label(
            sys_frame,
            text="💾 Memory: --",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.memory_usage_label.pack(anchor=tk.W)

        # Tools Activity
        tools_frame = tk.Frame(metrics_frame, bg=self.theme.BG_TERTIARY)
        tools_frame.pack(fill=tk.X, padx=15, pady=(0, 8))

        self.tools_executed_label = tk.Label(
            tools_frame,
            text="🛠️ Tools Executed: 0",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.tools_executed_label.pack(anchor=tk.W)

        self.last_tool_label = tk.Label(
            tools_frame,
            text="🔧 Last Tool: None",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_SECONDARY,
            font=self.fonts['caption']
        )
        self.last_tool_label.pack(anchor=tk.W)

        # Performance history mini-chart (text-based)
        self.setup_performance_history(metrics_frame)

    def setup_performance_history(self, parent):
        """Setup performance history visualization"""
        history_frame = tk.Frame(parent, bg=self.theme.BG_TERTIARY)
        history_frame.pack(fill=tk.X, padx=15, pady=(0, 8))

        history_title = tk.Label(
            history_frame,
            text="📊 Performance Trend:",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.TEXT_TERTIARY,
            font=self.fonts['caption']
        )
        history_title.pack(anchor=tk.W)

        # Text-based performance chart
        self.performance_chart = tk.Label(
            history_frame,
            text="▁▂▃▄▅▆▇█ Real-time monitoring...",
            bg=self.theme.BG_TERTIARY,
            fg=self.theme.SUNRISE_ORANGE,
            font=("Consolas", 9)
        )
        self.performance_chart.pack(anchor=tk.W)

        # Initialize performance tracking
        self.performance_history = []
        self.tools_executed_count = 0
        self.last_executed_tool = "None"

        # Enhanced command history
        self.command_history = []
        self.history_index = -1
        self.max_history = 100

    def setup_main_content_area(self, parent):
        """Setup main content area with terminal"""
        # Content container
        content_frame = tk.Frame(parent, bg=self.theme.BG_PRIMARY)
        content_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Professional terminal
        self.terminal = SunriseProfessionalTerminal(content_frame)
        self.terminal.pack(fill=tk.BOTH, expand=True)
        self.terminal.gui_parent = self

    # === ADVANCED NAVIGATION METHODS ===

    def show_chat_view(self):
        """Show advanced AI chat interface"""
        self.update_status("AI Chat Interface Active", self.theme.SUCCESS)
        self.terminal.add_output("🤖 AI Chat Interface activated\n", "info")
        self.terminal.add_output("💡 Enhanced conversational AI ready\n", "info")

    def show_tools_dashboard(self):
        """Show enterprise tools dashboard"""
        self.update_status("Enterprise Tools Dashboard", self.theme.PROCESSING)
        self.display_tools_dashboard()

    def show_analytics_dashboard(self):
        """Show performance analytics dashboard"""
        self.update_status("Performance Analytics Active", self.theme.INFO)
        self.display_analytics_dashboard()

    def show_system_monitor(self):
        """Show advanced system monitoring"""
        self.update_status("System Monitor Active", self.theme.WARNING)
        self.display_system_monitor()

    def show_web_tools(self):
        """Show web browsing and scraping tools"""
        self.update_status("Web Tools Active", self.theme.INFO)
        self.display_web_tools()

    def show_database_tools(self):
        """Show database management tools"""
        self.update_status("Database Tools Active", self.theme.PROCESSING)
        self.display_database_tools()

    def show_security_center(self):
        """Show security and encryption tools"""
        self.update_status("Security Center Active", self.theme.ERROR)
        self.display_security_center()

    def show_advanced_settings(self):
        """Show advanced system settings"""
        self.update_status("Advanced Settings Active", self.theme.WARNING)
        self.display_advanced_settings()

    # === ENTERPRISE DASHBOARD METHODS ===

    def display_tools_dashboard(self):
        """Display comprehensive tools dashboard"""
        if not self.agent_tools:
            self.terminal.add_output("❌ Agent tools not available\n", "error")
            return

        self.terminal.add_output("\n🛠️ ENTERPRISE TOOLS DASHBOARD\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        # Get available tools by category
        tools = self.agent_tools.get_available_tools()

        # Categorize tools
        categories = {
            "🔧 Core System": [t for t in tools if any(x in t for x in ['system', 'process', 'disk', 'memory'])],
            "🌐 Web & Network": [t for t in tools if any(x in t for x in ['web', 'fetch', 'browse', 'network'])],
            "💾 Database": [t for t in tools if any(x in t for x in ['database', 'query', 'backup', 'sql'])],
            "🔒 Security": [t for t in tools if any(x in t for x in ['security', 'encrypt', 'hash', 'auth'])],
            "📊 Analytics": [t for t in tools if any(x in t for x in ['performance', 'monitor', 'analyze'])],
            "🚀 Development": [t for t in tools if any(x in t for x in ['git', 'docker', 'test', 'deploy'])]
        }

        for category, category_tools in categories.items():
            if category_tools:
                self.terminal.add_output(f"\n{category}:\n", "accent")
                for tool in category_tools[:5]:  # Show top 5 per category
                    self.terminal.add_output(f"  • {tool.replace('_', ' ').title()}\n", "info")
                if len(category_tools) > 5:
                    self.terminal.add_output(f"  ... and {len(category_tools) - 5} more\n", "system")

        self.terminal.add_output(f"\n📈 Total Tools Available: {len(tools)}\n", "success")
        self.terminal.add_output("💡 Use 'execute <tool_name>' to run tools\n", "info")

    def display_analytics_dashboard(self):
        """Display performance analytics dashboard"""
        self.terminal.add_output("\n📊 PERFORMANCE ANALYTICS DASHBOARD\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        # System performance
        self.execute_tool("get_system_info")

        # Model performance if available
        if self.ollama_interface and hasattr(self.ollama_interface, 'performance_analyzer'):
            self.terminal.add_output("\n🤖 AI MODEL PERFORMANCE:\n", "accent")
            try:
                current_model = self.model_var.get() or "gemma3:4b"
                stats = self.ollama_interface.get_model_performance_stats(current_model)

                if "error" not in stats:
                    self.terminal.add_output(f"Model: {stats.get('model_name', 'Unknown')}\n", "info")
                    self.terminal.add_output(f"Requests: {stats.get('total_requests', 0)}\n", "info")
                    self.terminal.add_output(f"Success Rate: {stats.get('success_rate', 0)}%\n", "success")
                    self.terminal.add_output(f"Avg Response: {stats.get('average_response_time', 0)}s\n", "info")
                    self.terminal.add_output(f"Current Timeout: {stats.get('current_timeout', 0)}s\n", "warning")
            except Exception as e:
                self.terminal.add_output(f"⚠️ Model stats unavailable: {e}\n", "warning")

    def display_system_monitor(self):
        """Display advanced system monitoring"""
        self.terminal.add_output("\n🔧 ADVANCED SYSTEM MONITOR\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        # Execute multiple monitoring tools
        monitoring_tools = [
            "get_system_info",
            "list_processes",
            "get_disk_usage",
            "get_performance_metrics"
        ]

        for tool in monitoring_tools:
            if tool in self.agent_tools.get_available_tools():
                self.terminal.add_output(f"\n🔍 Executing {tool.replace('_', ' ').title()}...\n", "accent")
                self.execute_tool(tool)

    def execute_tool(self, tool_name, params=None):
        """Execute enterprise tool with advanced tracking and error handling"""
        if not self.agent_tools:
            self.terminal.add_output("❌ Agent tools not initialized\n", "error")
            return

        if params is None:
            params = {}

        # Update activity tracking
        self.tools_executed_count += 1
        self.last_executed_tool = tool_name.replace('_', ' ').title()

        try:
            # Show execution with timestamp
            current_time = datetime.now().strftime("%H:%M:%S")
            self.terminal.add_output(f"⚡ [{current_time}] Executing: {tool_name}\n", "accent")

            # Execute with timing
            start_time = time.time()
            result = self.agent_tools.execute_tool(tool_name, params)
            execution_time = time.time() - start_time

            if result.success:
                self.terminal.add_output(f"✅ Success ({execution_time:.2f}s):\n", "success")
                self.terminal.add_output(f"{result.output}\n", "info")

                if result.metadata:
                    self.terminal.add_output("📋 Metadata:\n", "system")
                    for key, value in result.metadata.items():
                        self.terminal.add_output(f"  {key}: {value}\n", "system")

                # Update status
                self.update_status(f"Tool '{tool_name}' executed successfully", self.theme.SUCCESS)

            else:
                self.terminal.add_output(f"❌ Error ({execution_time:.2f}s):\n", "error")
                self.terminal.add_output(f"{result.error}\n", "error")

                # Update status
                self.update_status(f"Tool '{tool_name}' failed", self.theme.ERROR)

        except Exception as e:
            self.terminal.add_output(f"💥 Execution failed: {e}\n", "error")
            self.update_status(f"Tool execution error", self.theme.ERROR)

    def display_web_tools(self):
        """Display web browsing and scraping tools dashboard"""
        self.terminal.add_output("\n🌐 WEB TOOLS DASHBOARD\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        web_tools = [
            ("🔍", "web_search", "Search the web for information"),
            ("📄", "web_fetch", "Fetch and parse web pages"),
            ("🌐", "browse_website", "Interactive web browsing"),
            ("📊", "analyze_website", "Analyze website structure")
        ]

        self.terminal.add_output("Available Web Tools:\n", "accent")
        for icon, tool, description in web_tools:
            if tool in self.agent_tools.get_available_tools():
                self.terminal.add_output(f"{icon} {tool}: {description}\n", "info")

        self.terminal.add_output("\n💡 Example: execute web_search query='AI news'\n", "system")

    def display_database_tools(self):
        """Display database management tools dashboard"""
        self.terminal.add_output("\n💾 DATABASE TOOLS DASHBOARD\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        db_tools = [
            ("🗄️", "database_query", "Execute SQL queries"),
            ("💾", "database_backup", "Backup database"),
            ("🔄", "database_restore", "Restore from backup"),
            ("📊", "database_analyze", "Analyze database performance")
        ]

        self.terminal.add_output("Available Database Tools:\n", "accent")
        for icon, tool, description in db_tools:
            if tool in self.agent_tools.get_available_tools():
                self.terminal.add_output(f"{icon} {tool}: {description}\n", "info")

        self.terminal.add_output("\n💡 Example: execute database_query sql='SELECT * FROM users LIMIT 10'\n", "system")

    def display_security_center(self):
        """Display security and encryption tools dashboard"""
        self.terminal.add_output("\n🔒 SECURITY CENTER DASHBOARD\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        security_tools = [
            ("🔐", "encrypt_data", "Encrypt sensitive data"),
            ("🔓", "decrypt_data", "Decrypt encrypted data"),
            ("🔑", "generate_keys", "Generate encryption keys"),
            ("🛡️", "security_scan", "Perform security scan"),
            ("🔍", "vulnerability_check", "Check for vulnerabilities")
        ]

        self.terminal.add_output("Available Security Tools:\n", "accent")
        for icon, tool, description in security_tools:
            if tool in self.agent_tools.get_available_tools():
                self.terminal.add_output(f"{icon} {tool}: {description}\n", "info")

        self.terminal.add_output("\n⚠️ Security tools require elevated permissions\n", "warning")
        self.terminal.add_output("💡 Example: execute security_scan target='localhost'\n", "system")

    def display_advanced_settings(self):
        """Display advanced system settings"""
        self.terminal.add_output("\n⚙️ ADVANCED SETTINGS DASHBOARD\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        # System configuration
        self.terminal.add_output("🔧 System Configuration:\n", "accent")
        self.terminal.add_output(f"  • Theme: Sunrise Professional Edition\n", "info")
        self.terminal.add_output(f"  • AI Model: {self.model_var.get() or 'Not selected'}\n", "info")
        self.terminal.add_output(f"  • Tools Available: {len(self.agent_tools.get_available_tools()) if self.agent_tools else 0}\n", "info")

        # Performance settings
        self.terminal.add_output("\n📈 Performance Settings:\n", "accent")
        if self.ollama_interface and hasattr(self.ollama_interface, 'performance_analyzer'):
            analyzer = self.ollama_interface.performance_analyzer
            self.terminal.add_output(f"  • Base Timeout: {analyzer.base_timeout}s\n", "info")
            self.terminal.add_output(f"  • Max Timeout: {analyzer.max_timeout}s\n", "info")
            self.terminal.add_output(f"  • Min Timeout: {analyzer.min_timeout}s\n", "info")

        # Available commands
        self.terminal.add_output("\n🎛️ Advanced Commands:\n", "accent")
        advanced_commands = [
            "reset timeouts - Reset all model timeouts to base",
            "conservative timeouts - Set conservative timeouts",
            "analyze models - Analyze model performance",
            "model stats - Show current model statistics"
        ]

        for cmd in advanced_commands:
            self.terminal.add_output(f"  • {cmd}\n", "info")

    # === CONTROL METHODS ===

    def refresh_models(self):
        """Refresh AI models"""
        self.update_status("Refreshing Models...", self.theme.PROCESSING)
        # Refresh logic here

    def open_settings(self):
        """Open settings dialog"""
        self.update_status("Opening Settings...", self.theme.INFO)
        # Settings dialog here

    def update_status(self, message, color=None):
        """Update status indicator"""
        if color is None:
            color = self.theme.SUCCESS

        self.status_label.configure(text=message)
        self.status_dot.configure(fg=color)

    def setup_professional_bindings(self):
        """Setup advanced professional keyboard bindings"""
        # Global shortcuts
        self.root.bind('<Control-r>', lambda e: self.refresh_models())
        self.root.bind('<Control-comma>', lambda e: self.show_advanced_settings())
        self.root.bind('<F11>', self.toggle_fullscreen)
        self.root.bind('<Escape>', self.exit_fullscreen)

        # Advanced navigation shortcuts
        self.root.bind('<Control-1>', lambda e: self.show_chat_view())
        self.root.bind('<Control-2>', lambda e: self.show_tools_dashboard())
        self.root.bind('<Control-3>', lambda e: self.show_analytics_dashboard())
        self.root.bind('<Control-4>', lambda e: self.show_system_monitor())
        self.root.bind('<Control-5>', lambda e: self.show_web_tools())
        self.root.bind('<Control-6>', lambda e: self.show_database_tools())
        self.root.bind('<Control-7>', lambda e: self.show_security_center())
        self.root.bind('<Control-8>', lambda e: self.show_advanced_settings())

        # Quick tool execution shortcuts
        self.root.bind('<Control-Shift-s>', lambda e: self.execute_tool("get_system_info"))
        self.root.bind('<Control-Shift-p>', lambda e: self.execute_tool("get_performance_metrics"))
        self.root.bind('<Control-Shift-d>', lambda e: self.execute_tool("get_disk_usage"))
        self.root.bind('<Control-Shift-n>', lambda e: self.execute_tool("network_diagnostics"))

        # Advanced features
        self.root.bind('<Control-Shift-r>', lambda e: self.reset_all_timeouts())
        self.root.bind('<Control-Shift-a>', lambda e: self.analyze_all_models())
        self.root.bind('<Control-Shift-m>', lambda e: self.show_model_stats())

        # Help and documentation
        self.root.bind('<F1>', lambda e: self.show_advanced_help())
        self.root.bind('<Control-question>', lambda e: self.show_shortcuts_help())

    def toggle_fullscreen(self, event=None):
        """Toggle fullscreen mode"""
        self.root.attributes('-fullscreen', not self.root.attributes('-fullscreen'))

    def exit_fullscreen(self, event=None):
        """Exit fullscreen mode"""
        self.root.attributes('-fullscreen', False)

    def start_professional_monitoring(self):
        """Start advanced real-time system monitoring"""
        def advanced_monitor_loop():
            while True:
                try:
                    # Update all performance metrics
                    self.update_advanced_performance_metrics()

                    # Update performance history
                    self.update_performance_history()

                    # Update visual indicators
                    self.update_visual_indicators()

                    time.sleep(1)  # More frequent updates for real-time feel
                except Exception as e:
                    print(f"Monitor error: {e}")
                    time.sleep(5)  # Wait longer on error

        monitor_thread = threading.Thread(target=advanced_monitor_loop, daemon=True)
        monitor_thread.start()

    def update_advanced_performance_metrics(self):
        """Update comprehensive performance metrics"""
        try:
            import psutil

            # System metrics
            cpu_percent = psutil.cpu_percent(interval=0.1)
            memory = psutil.virtual_memory()
            memory_percent = memory.percent

            # Update system performance labels (sidebar)
            if hasattr(self, 'cpu_usage_label'):
                self.cpu_usage_label.configure(
                    text=f"🖥️ CPU Usage: {cpu_percent:.1f}%",
                    fg=self.get_performance_color(cpu_percent, 80, 90)
                )

            if hasattr(self, 'memory_usage_label'):
                self.memory_usage_label.configure(
                    text=f"💾 Memory: {memory_percent:.1f}%",
                    fg=self.get_performance_color(memory_percent, 75, 85)
                )

            # Update right panel info labels
            if hasattr(self, 'cpu_info_label'):
                self.cpu_info_label.configure(
                    text=f"🖥️ CPU: {cpu_percent:.1f}%",
                    fg=self.get_performance_color(cpu_percent, 70, 85)
                )

            if hasattr(self, 'memory_info_label'):
                self.memory_info_label.configure(
                    text=f"💾 Memory: {memory_percent:.1f}%",
                    fg=self.get_performance_color(memory_percent, 70, 85)
                )

            if hasattr(self, 'network_info_label'):
                self.network_info_label.configure(
                    text=f"🌐 Network: Active",
                    fg=self.theme.SUCCESS
                )

            # AI Model metrics (if available)
            if self.ollama_interface and hasattr(self.ollama_interface, 'performance_analyzer'):
                try:
                    current_model = self.model_var.get() or "gemma3:4b"
                    stats = self.ollama_interface.get_model_performance_stats(current_model)

                    if "error" not in stats:
                        avg_time = stats.get('average_response_time', 0)
                        success_rate = stats.get('success_rate', 0)

                        self.response_time_label.configure(
                            text=f"⚡ Response Time: {avg_time:.1f}s",
                            fg=self.get_performance_color(avg_time, 5, 10, reverse=True)
                        )

                        self.success_rate_label.configure(
                            text=f"✅ Success Rate: {success_rate:.1f}%",
                            fg=self.get_performance_color(success_rate, 95, 90, reverse=True)
                        )
                except:
                    pass

            # Tools activity (sidebar)
            if hasattr(self, 'tools_executed_label'):
                self.tools_executed_label.configure(
                    text=f"🛠️ Tools Executed: {self.tools_executed_count}"
                )

            if hasattr(self, 'last_tool_label'):
                self.last_tool_label.configure(
                    text=f"🔧 Last Tool: {self.last_executed_tool}"
                )

            # Tools activity (right panel)
            if hasattr(self, 'tools_executed_info_label'):
                self.tools_executed_info_label.configure(
                    text=f"⚡ Executed: {self.tools_executed_count}"
                )

            if hasattr(self, 'last_tool_info_label'):
                self.last_tool_info_label.configure(
                    text=f"🔧 Last: {self.last_executed_tool}"
                )

            # Update AI info in right panel
            if hasattr(self, 'current_model_info_label'):
                current_model = self.model_var.get() or "Loading..."
                self.current_model_info_label.configure(
                    text=f"🎯 Model: {current_model}"
                )

        except ImportError:
            # Fallback without psutil
            self.cpu_usage_label.configure(text="🖥️ CPU Usage: N/A")
            self.memory_usage_label.configure(text="💾 Memory: N/A")
        except Exception as e:
            print(f"Performance update error: {e}")

    def get_performance_color(self, value, warning_threshold, critical_threshold, reverse=False):
        """Get color based on performance value"""
        if reverse:
            # For metrics where higher is better (like success rate)
            if value >= warning_threshold:
                return self.theme.SUCCESS
            elif value >= critical_threshold:
                return self.theme.WARNING
            else:
                return self.theme.ERROR
        else:
            # For metrics where lower is better (like CPU usage)
            if value <= warning_threshold:
                return self.theme.SUCCESS
            elif value <= critical_threshold:
                return self.theme.WARNING
            else:
                return self.theme.ERROR

    def update_performance_history(self):
        """Update performance history visualization"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # Add to history (keep last 20 values)
            self.performance_history.append(cpu_percent)
            if len(self.performance_history) > 20:
                self.performance_history.pop(0)

            # Create text-based chart
            chart_chars = "▁▂▃▄▅▆▇█"
            chart = ""

            for value in self.performance_history:
                # Map 0-100% to chart characters
                char_index = min(int(value / 12.5), 7)  # 8 characters, so divide by 12.5
                chart += chart_chars[char_index]

            # Pad with spaces if needed
            while len(chart) < 20:
                chart = "▁" + chart

            # Update sidebar performance chart
            if hasattr(self, 'performance_chart'):
                self.performance_chart.configure(
                    text=f"{chart} CPU: {cpu_percent:.1f}%",
                    fg=self.get_performance_color(cpu_percent, 70, 85)
                )

            # Update right panel performance chart
            if hasattr(self, 'perf_chart_label'):
                self.perf_chart_label.configure(
                    text=f"📊 {chart}",
                    fg=self.get_performance_color(cpu_percent, 70, 85)
                )

        except ImportError:
            self.performance_chart.configure(text="▁▂▃▄▅▆▇█ Monitoring unavailable")
        except Exception as e:
            print(f"History update error: {e}")

    def update_visual_indicators(self):
        """Update visual status indicators"""
        try:
            # Update status dot based on overall system health
            if hasattr(self, 'performance_history') and self.performance_history:
                avg_cpu = sum(self.performance_history) / len(self.performance_history)

                if avg_cpu < 50:
                    self.status_dot.configure(fg=self.theme.SUCCESS)
                elif avg_cpu < 80:
                    self.status_dot.configure(fg=self.theme.WARNING)
                else:
                    self.status_dot.configure(fg=self.theme.ERROR)
        except:
            pass

    # === ADVANCED SHORTCUT METHODS ===

    def reset_all_timeouts(self):
        """Reset all model timeouts via shortcut"""
        if self.ollama_interface and hasattr(self.ollama_interface, 'reset_all_timeouts'):
            result = self.ollama_interface.reset_all_timeouts()
            if result.get('success'):
                self.terminal.add_output(f"✅ Reset {result.get('reset_count', 0)} model timeouts\n", "success")
                self.update_status("Timeouts reset successfully", self.theme.SUCCESS)
            else:
                self.terminal.add_output(f"❌ Failed to reset timeouts: {result.get('error', 'Unknown error')}\n", "error")

    def analyze_all_models(self):
        """Analyze all models via shortcut"""
        if self.ollama_interface and hasattr(self.ollama_interface, 'analyze_model_performance'):
            self.terminal.add_output("🔍 Analyzing all model performance...\n", "info")
            result = self.ollama_interface.analyze_model_performance()
            if result.get('success'):
                self.terminal.add_output("✅ Model analysis completed\n", "success")
                self.update_status("Model analysis completed", self.theme.SUCCESS)
            else:
                self.terminal.add_output(f"❌ Analysis failed: {result.get('error', 'Unknown error')}\n", "error")

    def show_model_stats(self):
        """Show current model statistics"""
        if self.ollama_interface and hasattr(self.ollama_interface, 'get_model_performance_stats'):
            current_model = self.model_var.get() or "gemma3:4b"
            stats = self.ollama_interface.get_model_performance_stats(current_model)

            if "error" not in stats:
                self.terminal.add_output(f"\n📊 MODEL STATISTICS - {stats.get('model_name', 'Unknown')}\n", "header")
                self.terminal.add_output("=" * 50 + "\n", "info")
                self.terminal.add_output(f"📈 Total Requests: {stats.get('total_requests', 0)}\n", "info")
                self.terminal.add_output(f"✅ Success Rate: {stats.get('success_rate', 0)}%\n", "success")
                self.terminal.add_output(f"⏱️ Average Response: {stats.get('average_response_time', 0)}s\n", "info")
                self.terminal.add_output(f"⚡ Min Response: {stats.get('min_response_time', 0)}s\n", "info")
                self.terminal.add_output(f"🐌 Max Response: {stats.get('max_response_time', 0)}s\n", "info")
                self.terminal.add_output(f"⏰ Current Timeout: {stats.get('current_timeout', 0)}s\n", "warning")
                self.terminal.add_output(f"💾 Model Size: {stats.get('model_size_gb', 0)}GB\n", "info")
            else:
                self.terminal.add_output(f"❌ Could not get model stats: {stats.get('error', 'Unknown error')}\n", "error")

    def show_advanced_help(self):
        """Show advanced help and documentation"""
        self.terminal.add_output("\n🆘 CYBEX ENTERPRISE - ADVANCED HELP\n", "header")
        self.terminal.add_output("=" * 60 + "\n", "info")

        help_sections = [
            ("🎯 Navigation Shortcuts", [
                "Ctrl+1-8: Switch between different views",
                "F11: Toggle fullscreen mode",
                "Esc: Exit fullscreen",
                "F1: Show this help"
            ]),
            ("⚡ Quick Tool Execution", [
                "Ctrl+Shift+S: System information",
                "Ctrl+Shift+P: Performance metrics",
                "Ctrl+Shift+D: Disk usage",
                "Ctrl+Shift+N: Network diagnostics"
            ]),
            ("🤖 AI Model Management", [
                "Ctrl+Shift+R: Reset all timeouts",
                "Ctrl+Shift+A: Analyze all models",
                "Ctrl+Shift+M: Show model statistics",
                "Ctrl+R: Refresh model list"
            ]),
            ("🛠️ Enterprise Tools", [
                "execute <tool_name>: Run any available tool",
                "tools: List all available tools",
                "models: Show AI model information",
                "system info: Comprehensive system status"
            ])
        ]

        for section_title, items in help_sections:
            self.terminal.add_output(f"\n{section_title}:\n", "accent")
            for item in items:
                self.terminal.add_output(f"  • {item}\n", "info")

        self.terminal.add_output(f"\n💡 Total Tools Available: {len(self.agent_tools.get_available_tools()) if self.agent_tools else 0}\n", "success")

    def show_shortcuts_help(self):
        """Show keyboard shortcuts reference"""
        self.terminal.add_output("\n⌨️ KEYBOARD SHORTCUTS REFERENCE\n", "header")
        self.terminal.add_output("=" * 50 + "\n", "info")

        shortcuts = [
            ("Navigation", "Ctrl+1-8", "Switch views"),
            ("System Info", "Ctrl+Shift+S", "Quick system info"),
            ("Performance", "Ctrl+Shift+P", "Performance metrics"),
            ("Disk Usage", "Ctrl+Shift+D", "Disk information"),
            ("Network Test", "Ctrl+Shift+N", "Network diagnostics"),
            ("Reset Timeouts", "Ctrl+Shift+R", "Reset AI timeouts"),
            ("Analyze Models", "Ctrl+Shift+A", "Analyze AI models"),
            ("Model Stats", "Ctrl+Shift+M", "Show model statistics"),
            ("Refresh Models", "Ctrl+R", "Refresh model list"),
            ("Fullscreen", "F11", "Toggle fullscreen"),
            ("Help", "F1", "Show advanced help")
        ]

        for category, shortcut, description in shortcuts:
            self.terminal.add_output(f"{category:15} {shortcut:15} {description}\n", "info")

    def show_notification(self, message, type="info"):
        """Show temporary notification (could be enhanced with toast notifications)"""
        colors = {
            "info": self.theme.INFO,
            "success": self.theme.SUCCESS,
            "warning": self.theme.WARNING,
            "error": self.theme.ERROR
        }

        self.update_status(message, colors.get(type, self.theme.INFO))

        # Auto-clear after 5 seconds
        self.root.after(5000, lambda: self.update_status("System Ready", self.theme.SUCCESS))

    def show_futuristic_notification(self, message, type="info", duration=3000):
        """Show futuristic notification overlay"""
        # Create notification overlay
        notification = tk.Toplevel(self.root)
        notification.overrideredirect(True)
        notification.configure(bg=self.theme.BG_SECONDARY)

        # Position at top-right
        notification.geometry("350x80+{}+50".format(self.root.winfo_x() + self.root.winfo_width() - 370))

        # Notification content
        content_frame = tk.Frame(
            notification,
            bg=self.theme.BG_SECONDARY,
            relief=tk.FLAT,
            bd=2
        )
        content_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        # Icon and message
        icon_map = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌"
        }

        color_map = {
            "info": self.theme.INFO,
            "success": self.theme.SUCCESS,
            "warning": self.theme.WARNING,
            "error": self.theme.ERROR
        }

        icon_label = tk.Label(
            content_frame,
            text=icon_map.get(type, "ℹ️"),
            bg=self.theme.BG_SECONDARY,
            fg=color_map.get(type, self.theme.INFO),
            font=("Segoe UI", 16)
        )
        icon_label.pack(side=tk.LEFT, padx=(10, 5), pady=10)

        message_label = tk.Label(
            content_frame,
            text=message,
            bg=self.theme.BG_SECONDARY,
            fg=self.theme.TEXT_PRIMARY,
            font=self.fonts['caption'],
            wraplength=250,
            justify=tk.LEFT
        )
        message_label.pack(side=tk.LEFT, padx=(5, 10), pady=10, fill=tk.BOTH, expand=True)

        # Border effect
        border_color = color_map.get(type, self.theme.INFO)
        content_frame.configure(highlightbackground=border_color, highlightthickness=1)

        # Auto-close
        self.root.after(duration, notification.destroy)

        # Fade-in animation (simple)
        notification.attributes('-alpha', 0.0)
        self.animate_notification_fade_in(notification)

    def animate_notification_fade_in(self, notification, alpha=0.0):
        """Animate notification fade-in"""
        if alpha < 0.95:
            alpha += 0.05
            try:
                notification.attributes('-alpha', alpha)
                self.root.after(20, lambda: self.animate_notification_fade_in(notification, alpha))
            except:
                pass

    def load_models_async(self):
        """Load AI models asynchronously"""
        def load_models():
            try:
                if self.ollama_interface:
                    self.update_status("Loading AI models...", self.theme.PROCESSING)

                    # Get available models
                    models_info = self.ollama_interface.get_available_models()

                    if models_info.get('success') and models_info.get('models'):
                        models = models_info['models']
                        self.available_models = models

                        # Update model selector on main thread
                        self.root.after(0, lambda: self.populate_model_selector(models))
                        self.root.after(0, lambda: self.update_status(f"Loaded {len(models)} AI models", self.theme.SUCCESS))

                        print(f"✅ Loaded {len(models)} AI models successfully")
                        for model in models:
                            print(f"   • {model.get('name', 'Unknown')} ({model.get('size', 0) // (1024**3)}GB)")
                    else:
                        error_msg = models_info.get('error', 'Unknown error')
                        self.root.after(0, lambda: self.update_status(f"Failed to load models: {error_msg}", self.theme.ERROR))
                        print(f"❌ Failed to load models: {error_msg}")
                else:
                    self.root.after(0, lambda: self.update_status("Ollama not available", self.theme.WARNING))
                    print("⚠️ Ollama interface not available")

            except Exception as e:
                self.root.after(0, lambda: self.update_status(f"Model loading error: {e}", self.theme.ERROR))
                print(f"❌ Error loading models: {e}")

        # Run in separate thread
        threading.Thread(target=load_models, daemon=True).start()

    def populate_model_selector(self, models):
        """Populate the model selector dropdown"""
        try:
            if hasattr(self, 'model_selector'):
                # Clear existing values
                self.model_selector['values'] = []

                # Add models to selector
                model_names = []
                for model in models:
                    name = model.get('name', 'Unknown')
                    size_gb = model.get('size', 0) // (1024**3)
                    display_name = f"{name} ({size_gb}GB)"
                    model_names.append(display_name)

                self.model_selector['values'] = model_names

                # Set default selection
                if model_names:
                    self.model_selector.set(model_names[0])
                    # Extract just the model name for the variable
                    first_model_name = models[0].get('name', '')
                    self.model_var.set(first_model_name)

                    # Switch to the first model
                    if self.ollama_interface:
                        self.ollama_interface.switch_model(first_model_name)

                print(f"✅ Model selector populated with {len(model_names)} models")

        except Exception as e:
            print(f"❌ Error populating model selector: {e}")

    def refresh_models(self):
        """Refresh the models list"""
        self.update_status("Refreshing models...", self.theme.PROCESSING)
        self.load_models_async()

    def on_model_change(self, event=None):
        """Handle model selection change"""
        try:
            selected = self.model_selector.get()
            if selected and self.ollama_interface:
                # Extract model name from display name (remove size info)
                model_name = selected.split(' (')[0]

                self.update_status(f"Switching to {model_name}...", self.theme.PROCESSING)

                # Switch model in background
                def switch_model():
                    try:
                        success = self.ollama_interface.switch_model(model_name)
                        if success:
                            self.model_var.set(model_name)
                            self.root.after(0, lambda: self.update_status(f"Switched to {model_name}", self.theme.SUCCESS))
                            print(f"✅ Switched to model: {model_name}")
                        else:
                            self.root.after(0, lambda: self.update_status(f"Failed to switch to {model_name}", self.theme.ERROR))
                            print(f"❌ Failed to switch to model: {model_name}")
                    except Exception as e:
                        self.root.after(0, lambda: self.update_status(f"Model switch error: {e}", self.theme.ERROR))
                        print(f"❌ Model switch error: {e}")

                threading.Thread(target=switch_model, daemon=True).start()

        except Exception as e:
            self.update_status(f"Model change error: {e}", self.theme.ERROR)
            print(f"❌ Model change error: {e}")
        content_frame.pack(fill=tk.BOTH, expand=True, pady=(10, 0))

        # Left panel (Terminal)
        left_panel = tk.Frame(content_frame, bg=FuturisticTheme.BG_PANEL, relief=tk.RAISED, bd=1)
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))

        # Terminal
        self.terminal = FuturisticTerminal(left_panel)
        self.terminal.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        self.terminal.gui_parent = self  # Set reference

        # Right panel (Controls)
        right_panel = tk.Frame(content_frame, bg=FuturisticTheme.BG_PANEL, relief=tk.RAISED, bd=1, width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))
        right_panel.pack_propagate(False)

        self.setup_right_panel(right_panel)

        # Status bar
        self.setup_status_bar(main_frame)

    def setup_header(self, parent):
        """Setup header with logo"""
        header_frame = tk.Frame(parent, bg=FuturisticTheme.BG_DARK, height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # Logo
        logo_text = """
    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗███╗   ██╗████████╗
   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝████╗  ██║╚══██╔══╝
   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██╔██╗ ██║   ██║
   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║╚██╗██║   ██║
   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ███████╗██║ ╚████║   ██║
    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝   ╚═╝
"""

        logo_label = tk.Label(
            header_frame,
            text=logo_text,
            bg=FuturisticTheme.BG_DARK,
            fg=FuturisticTheme.NEON_CYAN,
            font=("Consolas", 8, "bold"),
            justify=tk.CENTER
        )
        logo_label.pack(expand=True)

    def setup_right_panel(self, parent):
        """Setup right control panel"""
        # AI Control Panel
        ai_frame = tk.LabelFrame(
            parent,
            text="🤖 AI CONTROL",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_PURPLE,
            font=("Consolas", 10, "bold"),
            relief=tk.RAISED,
            bd=1
        )
        ai_frame.pack(fill=tk.X, padx=5, pady=5)

        # Model selection
        tk.Label(
            ai_frame,
            text="Model:",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        ).pack(anchor=tk.W, padx=5, pady=2)

        self.model_var = tk.StringVar(value="gemma3:4b")

        # Get available models dynamically
        available_models = self.get_available_models()

        self.model_combo = ttk.Combobox(
            ai_frame,
            textvariable=self.model_var,
            values=available_models,
            state="readonly",
            font=("Consolas", 9),
            style="Futuristic.TCombobox"
        )
        self.model_combo.pack(fill=tk.X, padx=5, pady=2)
        self.model_combo.bind("<<ComboboxSelected>>", self.on_model_changed)

        # AI Status
        self.ai_status_label = tk.Label(
            ai_frame,
            text="🔄 Checking...",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.TEXT_SECONDARY,
            font=("Consolas", 8)
        )
        self.ai_status_label.pack(anchor=tk.W, padx=5, pady=2)

        # Ollama Monitor
        if OllamaMonitor:
            try:
                self.ollama_monitor = OllamaMonitor(parent, self.core.log_manager if self.core else None)
                self.logger.info("Ollama monitor initialized")
            except Exception as e:
                print(f"⚠️ Could not initialize Ollama monitor: {e}")
                self.ollama_monitor = None
        else:
            self.ollama_monitor = None

        # Quick Actions
        actions_frame = tk.LabelFrame(
            parent,
            text="⚡ QUICK ACTIONS",
            bg=FuturisticTheme.BG_PANEL,
            fg=FuturisticTheme.NEON_GREEN,
            font=("Consolas", 10, "bold"),
            relief=tk.RAISED,
            bd=1
        )
        actions_frame.pack(fill=tk.X, padx=5, pady=5)

        # Action buttons
        actions = [
            ("🖥️ System Info", self.action_system_info),
            ("🔄 Process List", self.action_process_list),
            ("🤖 Ollama Status", self.action_ollama_status),
            ("📊 Models List", self.action_models_list),
            ("🧹 Clear Terminal", self.action_clear_terminal),
            ("❓ Help", self.action_help)
        ]

        for text, command in actions:
            btn = tk.Button(
                actions_frame,
                text=text,
                command=command,
                bg=FuturisticTheme.BG_ACCENT,
                fg=FuturisticTheme.TEXT_PRIMARY,
                font=("Consolas", 9),
                relief=tk.FLAT,
                bd=1,
                activebackground=FuturisticTheme.NEON_CYAN,
                activeforeground=FuturisticTheme.BG_DARK
            )
            btn.pack(fill=tk.X, padx=5, pady=2)

    def get_available_models(self) -> List[str]:
        """Get list of available models from Ollama"""
        try:
            if self.ollama_interface:
                result = self.ollama_interface.get_available_models()
                if result['success']:
                    models = [model['name'] for model in result['models']]
                    if models:
                        return models

            # Fallback to default models
            return ["gemma3:4b", "gemma3:27b", "deepseek-r1:8b", "deepseek-r1:latest"]

        except Exception as e:
            print(f"Failed to get available models: {e}")
            return ["gemma3:4b", "gemma3:27b", "deepseek-r1:8b"]

    def on_model_changed(self, event=None):
        """Handle model selection change"""
        try:
            selected_model = self.model_var.get()
            if self.ollama_interface and selected_model:
                # Start monitoring model switch
                if hasattr(self, 'ollama_monitor') and self.ollama_monitor:
                    self.ollama_monitor.start_activity(selected_model, f"Switching to model: {selected_model}")

                # Update the model in Ollama interface
                success = self.ollama_interface.switch_model(selected_model)

                # End monitoring
                if hasattr(self, 'ollama_monitor') and self.ollama_monitor:
                    if success:
                        self.ollama_monitor.end_activity(success=True)
                    else:
                        self.ollama_monitor.end_activity(success=False, error="Model switch failed")

                if success:
                    self.terminal.add_output(f"🤖 Model switched to: {selected_model}\n", "success")
                    self.update_ai_status()
                else:
                    self.terminal.add_output(f"❌ Failed to switch to model: {selected_model}\n", "error")

        except Exception as e:
            # End monitoring on error
            if hasattr(self, 'ollama_monitor') and self.ollama_monitor:
                self.ollama_monitor.end_activity(success=False, error=str(e))
            self.terminal.add_output(f"❌ Failed to change model: {e}\n", "error")

    def setup_status_bar(self, parent):
        """Setup status bar"""
        status_frame = tk.Frame(parent, bg=FuturisticTheme.BG_ACCENT, height=25)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="🚀 CYBEX Enterprise Ready",
            bg=FuturisticTheme.BG_ACCENT,
            fg=FuturisticTheme.TEXT_PRIMARY,
            font=("Consolas", 9)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=2)

        # Version info
        version_label = tk.Label(
            status_frame,
            text="v2.0.0",
            bg=FuturisticTheme.BG_ACCENT,
            fg=FuturisticTheme.TEXT_SECONDARY,
            font=("Consolas", 8)
        )
        version_label.pack(side=tk.RIGHT, padx=10, pady=2)

    def setup_bindings(self):
        """Setup key bindings"""
        self.root.bind('<Control-q>', lambda e: self.root.quit())
        self.root.bind('<F1>', lambda e: self.action_help())
        self.root.bind('<F5>', lambda e: self.refresh_models())

    def start_status_monitor(self):
        """Start status monitoring"""
        def monitor():
            while True:
                try:
                    self.root.after_idle(self.update_ai_status)
                    time.sleep(30)  # Update every 30 seconds
                except:
                    break

        thread = threading.Thread(target=monitor, daemon=True)
        thread.start()

        # Initial status update
        self.root.after(1000, self.update_ai_status)

    def update_ai_status(self):
        """Update AI status"""
        try:
            if self.ollama_interface and self.ollama_interface.is_server_available():
                current_model = self.ollama_interface.model
                self.ai_status_label.config(
                    text=f"✅ Online - {current_model}",
                    fg=FuturisticTheme.SUCCESS
                )
            else:
                self.ai_status_label.config(
                    text="❌ Offline",
                    fg=FuturisticTheme.ERROR
                )
        except Exception as e:
            self.ai_status_label.config(
                text="⚠️ Error",
                fg=FuturisticTheme.WARNING
            )

    def refresh_models(self):
        """Refresh available models"""
        try:
            models = self.get_available_models()
            self.model_combo['values'] = models
            self.terminal.add_output("🔄 Models list refreshed\n", "info")
        except Exception as e:
            self.terminal.add_output(f"❌ Failed to refresh models: {e}\n", "error")

    # Quick action methods
    def action_system_info(self):
        """System info action"""
        self.terminal.process_command("systeminfo")

    def action_process_list(self):
        """Process list action"""
        self.terminal.process_command("tasklist")

    def action_ollama_status(self):
        """Ollama status action"""
        self.terminal.process_command("stato ollama")

    def action_models_list(self):
        """Models list action"""
        self.terminal.process_command("models")

    def action_clear_terminal(self):
        """Clear terminal action"""
        self.terminal.clear_terminal()

    def action_help(self):
        """Help action"""
        self.terminal.show_help()

    def run(self):
        """Run the professional Sunrise GUI"""
        try:
            print("🌅 Starting CYBEX Enterprise - Sunrise Professional Edition...")
            print("✨ Professional UI with Apple-level design")
            print("🎨 Sunrise color palette activated")
            print("🚀 Advanced features enabled")
            self.root.mainloop()
        except KeyboardInterrupt:
            print("\n👋 Cybex GUI interrupted by user")
        except Exception as e:
            print(f"❌ GUI error: {e}")
        finally:
            if self.core:
                self.core.shutdown()

    def after_idle(self, func):
        """Schedule function to run after idle"""
        self.root.after_idle(func)

# === COMPATIBILITY ALIAS ===
# Maintain compatibility with existing code
CybexFuturisticGUI = CybexSunriseProfessionalGUI

def main():
    """Main entry point for CYBEX Sunrise Professional Edition"""
    try:
        print("🌅 Launching CYBEX Enterprise - Sunrise Professional Edition")
        print("✨ Apple-level design system activated")
        print("🎨 Professional Sunrise color palette")
        app = CybexSunriseProfessionalGUI()
        app.run()
    except Exception as e:
        print(f"❌ Failed to start CYBEX Professional GUI: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
