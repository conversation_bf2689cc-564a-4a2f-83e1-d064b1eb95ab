#!/usr/bin/env python3
"""
Ollama Integration for CYBEX Enterprise
Local AI model integration with Ollama
"""

import requests
import json
import time
from typing import Dict, List, Any, Optional, Generator
from dataclasses import dataclass
from datetime import datetime
import threading
import queue


@dataclass
class OllamaModel:
    """Ollama model information"""
    name: str
    size: int
    modified: str
    digest: str
    details: Dict[str, Any]


@dataclass
class ChatMessage:
    """Chat message structure"""
    role: str  # 'system', 'user', 'assistant'
    content: str
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()


class OllamaIntegration:
    """Ollama local AI integration"""
    
    def __init__(self, base_url: str = "http://localhost:11434", log_manager=None):
        self.base_url = base_url.rstrip('/')
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Connection status
        self.is_connected = False
        self.available_models = []
        self.current_model = None
        
        # Chat history
        self.chat_history = []
        self.max_history = 50
        
        # System prompt for CYBEX
        self.system_prompt = self._load_cybex_system_prompt()
        
        # Initialize connection
        self._check_connection()
    
    def _load_cybex_system_prompt(self) -> str:
        """Load CYBEX system prompt"""
        try:
            from pathlib import Path
            prompt_file = Path(__file__).parent.parent / 'config' / 'enhanced_ai_prompt.txt'
            if prompt_file.exists():
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not load system prompt: {e}")
        
        # Fallback system prompt
        return """You are CYBEX Enterprise, an advanced AI assistant with comprehensive system administration, 
web browsing, database management, file operations, and Excel creation capabilities. 
You can execute commands, analyze systems, create professional documents, and help with enterprise tasks.
Always be helpful, professional, and security-conscious."""
    
    def _check_connection(self) -> bool:
        """Check if Ollama is running and accessible"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.is_connected = True
                self._load_available_models()
                if self.logger:
                    self.logger.info("Connected to Ollama successfully")
                return True
            else:
                self.is_connected = False
                if self.logger:
                    self.logger.warning(f"Ollama connection failed: {response.status_code}")
                return False
                
        except requests.exceptions.RequestException as e:
            self.is_connected = False
            if self.logger:
                self.logger.warning(f"Could not connect to Ollama: {e}")
            return False
    
    def _load_available_models(self):
        """Load list of available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                self.available_models = []
                
                for model_data in data.get('models', []):
                    model = OllamaModel(
                        name=model_data['name'],
                        size=model_data.get('size', 0),
                        modified=model_data.get('modified_at', ''),
                        digest=model_data.get('digest', ''),
                        details=model_data.get('details', {})
                    )
                    self.available_models.append(model)
                
                # Set default model if none selected
                if self.available_models and not self.current_model:
                    self.current_model = self.available_models[0].name
                    
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to load models: {e}")
    
    def get_connection_status(self) -> Dict[str, Any]:
        """Get connection status and available models"""
        self._check_connection()
        
        return {
            'connected': self.is_connected,
            'base_url': self.base_url,
            'models_count': len(self.available_models),
            'current_model': self.current_model,
            'available_models': [model.name for model in self.available_models]
        }
    
    def set_model(self, model_name: str) -> bool:
        """Set the current model to use"""
        if not self.is_connected:
            return False
        
        # Check if model exists
        model_names = [model.name for model in self.available_models]
        if model_name in model_names:
            self.current_model = model_name
            if self.logger:
                self.logger.info(f"Switched to model: {model_name}")
            return True
        else:
            if self.logger:
                self.logger.warning(f"Model not found: {model_name}")
            return False
    
    def pull_model(self, model_name: str) -> Dict[str, Any]:
        """Pull/download a model from Ollama"""
        try:
            if not self.is_connected:
                return {'success': False, 'error': 'Not connected to Ollama'}
            
            response = requests.post(
                f"{self.base_url}/api/pull",
                json={'name': model_name},
                stream=True,
                timeout=300  # 5 minutes timeout
            )
            
            if response.status_code == 200:
                # Process streaming response
                progress_info = []
                for line in response.iter_lines():
                    if line:
                        try:
                            data = json.loads(line.decode('utf-8'))
                            progress_info.append(data)
                            
                            # Log progress
                            if 'status' in data and self.logger:
                                self.logger.info(f"Pull progress: {data['status']}")
                                
                        except json.JSONDecodeError:
                            continue
                
                # Refresh available models
                self._load_available_models()
                
                return {
                    'success': True,
                    'model': model_name,
                    'message': f'Model {model_name} pulled successfully',
                    'progress': progress_info
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to pull model: {response.status_code}'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def chat(self, message: str, stream: bool = False) -> Dict[str, Any]:
        """Send chat message to Ollama"""
        try:
            if not self.is_connected:
                return {'success': False, 'error': 'Not connected to Ollama'}
            
            if not self.current_model:
                return {'success': False, 'error': 'No model selected'}
            
            # Add user message to history
            user_msg = ChatMessage(role='user', content=message)
            self.chat_history.append(user_msg)
            
            # Prepare messages for API
            messages = []
            
            # Add system prompt
            messages.append({
                'role': 'system',
                'content': self.system_prompt
            })
            
            # Add recent chat history (last 10 messages)
            recent_history = self.chat_history[-10:]
            for msg in recent_history:
                messages.append({
                    'role': msg.role,
                    'content': msg.content
                })
            
            # Make API request
            payload = {
                'model': self.current_model,
                'messages': messages,
                'stream': stream
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                stream=stream,
                timeout=60
            )
            
            if response.status_code == 200:
                if stream:
                    return self._handle_streaming_response(response)
                else:
                    return self._handle_regular_response(response)
            else:
                return {
                    'success': False,
                    'error': f'Chat request failed: {response.status_code}'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _handle_regular_response(self, response) -> Dict[str, Any]:
        """Handle regular (non-streaming) response"""
        try:
            data = response.json()
            assistant_message = data.get('message', {}).get('content', '')
            
            # Add assistant response to history
            assistant_msg = ChatMessage(role='assistant', content=assistant_message)
            self.chat_history.append(assistant_msg)
            
            # Trim history if too long
            if len(self.chat_history) > self.max_history:
                self.chat_history = self.chat_history[-self.max_history:]
            
            return {
                'success': True,
                'response': assistant_message,
                'model': self.current_model,
                'done': data.get('done', True),
                'total_duration': data.get('total_duration', 0),
                'load_duration': data.get('load_duration', 0),
                'prompt_eval_count': data.get('prompt_eval_count', 0),
                'eval_count': data.get('eval_count', 0)
            }
            
        except Exception as e:
            return {'success': False, 'error': f'Response parsing error: {e}'}
    
    def _handle_streaming_response(self, response) -> Generator[Dict[str, Any], None, None]:
        """Handle streaming response"""
        try:
            full_response = ""
            
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        
                        if 'message' in data:
                            content = data['message'].get('content', '')
                            full_response += content
                            
                            yield {
                                'success': True,
                                'content': content,
                                'done': data.get('done', False),
                                'model': self.current_model
                            }
                        
                        if data.get('done', False):
                            # Add complete response to history
                            assistant_msg = ChatMessage(role='assistant', content=full_response)
                            self.chat_history.append(assistant_msg)
                            
                            # Trim history
                            if len(self.chat_history) > self.max_history:
                                self.chat_history = self.chat_history[-self.max_history:]
                            
                            break
                            
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            yield {'success': False, 'error': str(e)}
    
    def generate(self, prompt: str, stream: bool = False) -> Dict[str, Any]:
        """Generate text using Ollama (simpler interface)"""
        try:
            if not self.is_connected:
                return {'success': False, 'error': 'Not connected to Ollama'}
            
            if not self.current_model:
                return {'success': False, 'error': 'No model selected'}
            
            payload = {
                'model': self.current_model,
                'prompt': prompt,
                'stream': stream
            }
            
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=payload,
                stream=stream,
                timeout=60
            )
            
            if response.status_code == 200:
                if stream:
                    return self._handle_generate_streaming(response)
                else:
                    data = response.json()
                    return {
                        'success': True,
                        'response': data.get('response', ''),
                        'model': self.current_model,
                        'done': data.get('done', True)
                    }
            else:
                return {
                    'success': False,
                    'error': f'Generate request failed: {response.status_code}'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _handle_generate_streaming(self, response) -> Generator[Dict[str, Any], None, None]:
        """Handle streaming generate response"""
        try:
            for line in response.iter_lines():
                if line:
                    try:
                        data = json.loads(line.decode('utf-8'))
                        yield {
                            'success': True,
                            'content': data.get('response', ''),
                            'done': data.get('done', False),
                            'model': self.current_model
                        }
                        
                        if data.get('done', False):
                            break
                            
                    except json.JSONDecodeError:
                        continue
                        
        except Exception as e:
            yield {'success': False, 'error': str(e)}
    
    def clear_chat_history(self):
        """Clear chat history"""
        self.chat_history = []
        if self.logger:
            self.logger.info("Chat history cleared")
    
    def get_chat_history(self) -> List[Dict[str, Any]]:
        """Get chat history"""
        return [
            {
                'role': msg.role,
                'content': msg.content,
                'timestamp': msg.timestamp.isoformat()
            }
            for msg in self.chat_history
        ]
    
    def get_model_info(self, model_name: str = None) -> Dict[str, Any]:
        """Get detailed model information"""
        try:
            target_model = model_name or self.current_model
            if not target_model:
                return {'success': False, 'error': 'No model specified'}
            
            response = requests.post(
                f"{self.base_url}/api/show",
                json={'name': target_model}
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'model_info': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to get model info: {response.status_code}'
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_ollama_integration(base_url: str = "http://localhost:11434", log_manager=None) -> OllamaIntegration:
    """Factory function to create Ollama integration"""
    return OllamaIntegration(base_url, log_manager)
