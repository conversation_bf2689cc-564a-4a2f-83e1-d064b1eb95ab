#!/usr/bin/env python3
"""
Operation Monitor
Sistema di monitoraggio operazioni in tempo reale per l'agente
"""

import os
import sys
import time
import threading
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
from collections import deque

class OperationType(Enum):
    """Tipi di operazioni"""
    TOOL_EXECUTION = "tool_execution"
    COMMAND_EXECUTION = "command_execution"
    FILE_OPERATION = "file_operation"
    NETWORK_OPERATION = "network_operation"
    SYSTEM_ANALYSIS = "system_analysis"
    AI_PROCESSING = "ai_processing"

class OperationStatus(Enum):
    """Stati operazione"""
    STARTING = "starting"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class Operation:
    """Operazione in corso"""
    id: str
    name: str
    type: OperationType
    status: OperationStatus
    start_time: float
    description: str = ""
    progress: float = 0.0  # 0-100
    details: str = ""
    end_time: Optional[float] = None
    result: Optional[str] = None
    error: Optional[str] = None

class OperationMonitor:
    """Monitor delle operazioni in tempo reale"""
    
    def __init__(self, max_history: int = 100):
        self.max_history = max_history
        
        # Active operations
        self.active_operations: Dict[str, Operation] = {}
        
        # Operation history
        self.operation_history: deque = deque(maxlen=max_history)
        
        # Observers (for UI updates)
        self.observers: List[callable] = []
        
        # Lock for thread safety
        self._lock = threading.Lock()
        
        # Stats
        self.stats = {
            'total_operations': 0,
            'successful_operations': 0,
            'failed_operations': 0,
            'average_duration': 0.0
        }
    
    def add_observer(self, callback: callable):
        """Aggiungi observer per aggiornamenti UI"""
        with self._lock:
            self.observers.append(callback)
    
    def remove_observer(self, callback: callable):
        """Rimuovi observer"""
        with self._lock:
            if callback in self.observers:
                self.observers.remove(callback)
    
    def _notify_observers(self, operation: Operation, event_type: str):
        """Notifica observers di cambiamenti"""
        for observer in self.observers:
            try:
                observer(operation, event_type)
            except Exception as e:
                # Ignore observer errors
                pass
    
    def start_operation(self, name: str, operation_type: OperationType, 
                       description: str = "") -> str:
        """Inizia una nuova operazione"""
        operation_id = f"{operation_type.value}_{int(time.time() * 1000)}"
        
        operation = Operation(
            id=operation_id,
            name=name,
            type=operation_type,
            status=OperationStatus.STARTING,
            start_time=time.time(),
            description=description
        )
        
        with self._lock:
            self.active_operations[operation_id] = operation
            self.stats['total_operations'] += 1
        
        self._notify_observers(operation, "started")
        
        return operation_id
    
    def update_operation(self, operation_id: str, status: Optional[OperationStatus] = None,
                        progress: Optional[float] = None, details: Optional[str] = None):
        """Aggiorna operazione"""
        with self._lock:
            if operation_id not in self.active_operations:
                return False
            
            operation = self.active_operations[operation_id]
            
            if status:
                operation.status = status
            if progress is not None:
                operation.progress = max(0, min(100, progress))
            if details is not None:
                operation.details = details
        
        self._notify_observers(operation, "updated")
        return True
    
    def complete_operation(self, operation_id: str, result: str = "", error: str = ""):
        """Completa operazione"""
        with self._lock:
            if operation_id not in self.active_operations:
                return False
            
            operation = self.active_operations[operation_id]
            operation.end_time = time.time()
            operation.result = result
            operation.error = error
            
            if error:
                operation.status = OperationStatus.FAILED
                self.stats['failed_operations'] += 1
            else:
                operation.status = OperationStatus.COMPLETED
                self.stats['successful_operations'] += 1
            
            # Move to history
            self.operation_history.append(operation)
            del self.active_operations[operation_id]
            
            # Update average duration
            self._update_average_duration()
        
        self._notify_observers(operation, "completed")
        return True
    
    def cancel_operation(self, operation_id: str):
        """Cancella operazione"""
        with self._lock:
            if operation_id not in self.active_operations:
                return False
            
            operation = self.active_operations[operation_id]
            operation.status = OperationStatus.CANCELLED
            operation.end_time = time.time()
            
            # Move to history
            self.operation_history.append(operation)
            del self.active_operations[operation_id]
        
        self._notify_observers(operation, "cancelled")
        return True
    
    def get_active_operations(self) -> List[Operation]:
        """Ottieni operazioni attive"""
        with self._lock:
            return list(self.active_operations.values())
    
    def get_operation_history(self, limit: int = 20) -> List[Operation]:
        """Ottieni cronologia operazioni"""
        with self._lock:
            return list(self.operation_history)[-limit:]
    
    def get_operation(self, operation_id: str) -> Optional[Operation]:
        """Ottieni operazione specifica"""
        with self._lock:
            if operation_id in self.active_operations:
                return self.active_operations[operation_id]
            
            # Search in history
            for op in self.operation_history:
                if op.id == operation_id:
                    return op
            
            return None
    
    def get_stats(self) -> Dict[str, Any]:
        """Ottieni statistiche"""
        with self._lock:
            return self.stats.copy()
    
    def _update_average_duration(self):
        """Aggiorna durata media operazioni"""
        if not self.operation_history:
            return
        
        total_duration = 0
        completed_ops = 0
        
        for op in self.operation_history:
            if op.end_time and op.status == OperationStatus.COMPLETED:
                total_duration += op.end_time - op.start_time
                completed_ops += 1
        
        if completed_ops > 0:
            self.stats['average_duration'] = total_duration / completed_ops
    
    def clear_history(self):
        """Pulisci cronologia"""
        with self._lock:
            self.operation_history.clear()
    
    def format_operation_display(self, operation: Operation) -> str:
        """Formatta operazione per display"""
        duration = ""
        if operation.end_time:
            duration = f" ({operation.end_time - operation.start_time:.2f}s)"
        elif operation.status == OperationStatus.RUNNING:
            duration = f" ({time.time() - operation.start_time:.1f}s)"
        
        status_icon = {
            OperationStatus.STARTING: "🔄",
            OperationStatus.RUNNING: "⚡",
            OperationStatus.COMPLETED: "✅",
            OperationStatus.FAILED: "❌",
            OperationStatus.CANCELLED: "🚫"
        }.get(operation.status, "❓")
        
        progress_bar = ""
        if operation.status == OperationStatus.RUNNING and operation.progress > 0:
            filled = int(operation.progress / 10)
            progress_bar = f" [{'█' * filled}{'░' * (10 - filled)}] {operation.progress:.0f}%"
        
        return f"{status_icon} {operation.name}{duration}{progress_bar}"
    
    def get_display_summary(self) -> Dict[str, Any]:
        """Ottieni riassunto per display"""
        active_ops = self.get_active_operations()
        recent_history = self.get_operation_history(5)
        
        return {
            'active_count': len(active_ops),
            'active_operations': [self.format_operation_display(op) for op in active_ops],
            'recent_operations': [self.format_operation_display(op) for op in recent_history],
            'stats': self.get_stats()
        }


class OperationLogger:
    """Logger specializzato per operazioni"""
    
    def __init__(self, monitor: OperationMonitor, log_manager=None):
        self.monitor = monitor
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Add self as observer
        self.monitor.add_observer(self._log_operation_event)
    
    def _log_operation_event(self, operation: Operation, event_type: str):
        """Log eventi operazioni"""
        if not self.logger:
            return
        
        if event_type == "started":
            self.logger.info(f"Operation started: {operation.name} ({operation.id})")
        elif event_type == "completed":
            duration = operation.end_time - operation.start_time if operation.end_time else 0
            if operation.status == OperationStatus.COMPLETED:
                self.logger.info(f"Operation completed: {operation.name} in {duration:.2f}s")
            elif operation.status == OperationStatus.FAILED:
                self.logger.error(f"Operation failed: {operation.name} - {operation.error}")
        elif event_type == "cancelled":
            self.logger.warning(f"Operation cancelled: {operation.name}")


# Global monitor instance
_global_monitor = None

def get_global_monitor() -> OperationMonitor:
    """Ottieni monitor globale"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = OperationMonitor()
    return _global_monitor

def init_global_monitor(log_manager=None) -> OperationMonitor:
    """Inizializza monitor globale"""
    global _global_monitor
    _global_monitor = OperationMonitor()
    
    if log_manager:
        OperationLogger(_global_monitor, log_manager)
    
    return _global_monitor
