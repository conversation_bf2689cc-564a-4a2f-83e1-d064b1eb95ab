#!/usr/bin/env python3
"""
Cybex Enterprise Launcher
Professional launcher for Cybex Enterprise GUI with Ollama integration
"""

import sys
import os
from pathlib import Path

# Add cybex to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_ollama_server():
    """Check if Ollama server is running"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ Ollama server is running with {len(models)} models available")
            
            # Show available models
            if models:
                print("🤖 Available models:")
                for model in models[:5]:  # Show first 5
                    name = model.get('name', 'Unknown')
                    size_gb = round(model.get('size', 0) / (1024**3), 1)
                    print(f"   • {name} ({size_gb} GB)")
                if len(models) > 5:
                    print(f"   ... and {len(models) - 5} more")
            
            return True
        else:
            print("⚠️ Ollama server responded with error")
            return False
    except Exception as e:
        print(f"❌ Ollama server not available: {e}")
        print("💡 To enable AI features:")
        print("   1. Install Ollama: https://ollama.ai/")
        print("   2. Start server: ollama serve")
        print("   3. Download model: ollama pull gemma3:4b")
        return False

def check_dependencies():
    """Check required dependencies"""
    try:
        import tkinter
        print("✅ Tkinter available")
    except ImportError:
        print("❌ Tkinter not available - GUI cannot start")
        return False
    
    try:
        import requests
        print("✅ Requests available")
    except ImportError:
        print("⚠️ Requests not available - install with: pip install requests")
    
    try:
        import psutil
        print("✅ Psutil available")
    except ImportError:
        print("⚠️ Psutil not available - install with: pip install psutil")
    
    return True

def main():
    """Main launcher"""
    print("╔══════════════════════════════════════════════════════════════════════════════╗")
    print("║                    🚀 CYBEX ENTERPRISE LAUNCHER                             ║")
    print("║                      Futuristic GUI with Ollama AI                          ║")
    print("╚══════════════════════════════════════════════════════════════════════════════╝")
    print()
    
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return 1
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} OK")
    
    # Check dependencies
    if not check_dependencies():
        print("❌ Missing critical dependencies")
        return 1
    
    # Check Ollama server
    print("\n🤖 Checking Ollama AI server...")
    ollama_available = check_ollama_server()
    
    if not ollama_available:
        print("\n⚠️ Ollama not available - AI features will be limited")
        response = input("Continue with system tools only? (Y/n): ")
        if response.lower() in ['n', 'no']:
            print("💡 Setup Ollama first, then run the launcher again")
            return 1
    
    try:
        print("\n🎨 Loading Cybex Enterprise GUI...")
        
        # Import and start GUI
        from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI
        
        print("🔧 Initializing core systems...")
        app = CybexFuturisticGUI()
        
        print("✅ Cybex Enterprise GUI ready!")
        print("💡 Use the terminal on the left for commands")
        print("🎯 Try: 'ciao', 'models', 'help', 'system info'")
        print()
        
        app.run()
        
        print("\n👋 Cybex Enterprise session ended")
        return 0
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all Cybex modules are properly installed")
        return 1
    except Exception as e:
        print(f"❌ Startup error: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
