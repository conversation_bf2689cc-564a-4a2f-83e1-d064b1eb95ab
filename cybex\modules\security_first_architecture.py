#!/usr/bin/env python3
"""
Security-First Architecture for CYBEX Enterprise
Implements comprehensive security controls, audit trails, and compliance monitoring
"""

import json
import time
import hashlib
import threading
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, <PERSON><PERSON><PERSON>
from pathlib import Path
from enum import Enum
import uuid
import hmac
import secrets


class SecurityLevel(Enum):
    """Security levels for operations"""
    PUBLIC = "PUBLIC"
    INTERNAL = "INTERNAL"
    CONFIDENTIAL = "CONFIDENTIAL"
    RESTRICTED = "RESTRICTED"
    TOP_SECRET = "TOP_SECRET"


class ThreatLevel(Enum):
    """Threat assessment levels"""
    MINIMAL = "MINIMAL"
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"


@dataclass
class SecurityEvent:
    """Security event record"""
    event_id: str
    timestamp: float
    event_type: str
    severity: str
    source: str
    target: str
    description: str
    user_context: Dict[str, Any]
    system_context: Dict[str, Any]
    risk_score: int
    mitigation_actions: List[str]
    compliance_impact: List[str]


@dataclass
class AuditTrailEntry:
    """Audit trail entry"""
    entry_id: str
    timestamp: float
    user_id: str
    session_id: str
    operation: str
    resource: str
    result: str
    security_level: str
    risk_assessment: str
    data_hash: str
    signature: str


class SecurityFirstArchitecture:
    """Comprehensive security architecture with enterprise controls"""
    
    def __init__(self, log_manager=None, config_manager=None):
        self.log_manager = log_manager
        self.config_manager = config_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Security configuration
        self.security_config = self._load_security_config()
        self.audit_enabled = self.security_config.get('audit_enabled', True)
        self.compliance_mode = self.security_config.get('compliance_mode', 'ENTERPRISE')
        
        # Security state
        self.current_threat_level = ThreatLevel.LOW
        self.active_security_events = []
        self.audit_trail = []
        self.security_policies = self._load_security_policies()
        
        # Cryptographic components
        self.signing_key = self._initialize_signing_key()
        self.encryption_key = self._initialize_encryption_key()
        
        # Monitoring and alerting
        self.security_monitor = SecurityMonitor(self)
        self.compliance_checker = ComplianceChecker(self)
        self.threat_detector = ThreatDetector(self)
        
        # Initialize security subsystems
        self._initialize_security_subsystems()
    
    def _load_security_config(self) -> Dict[str, Any]:
        """Load security configuration"""
        default_config = {
            'audit_enabled': True,
            'compliance_mode': 'ENTERPRISE',
            'max_failed_attempts': 3,
            'session_timeout': 3600,
            'require_mfa': False,
            'encryption_required': True,
            'audit_retention_days': 365,
            'threat_detection_enabled': True,
            'compliance_frameworks': ['SOX', 'GDPR', 'ISO27001'],
            'security_levels': {
                'default': 'INTERNAL',
                'admin_operations': 'RESTRICTED',
                'system_commands': 'CONFIDENTIAL'
            }
        }
        
        if self.config_manager:
            try:
                user_config = self.config_manager.get_section('security')
                default_config.update(user_config)
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Could not load security config: {e}")
        
        return default_config
    
    def _load_security_policies(self) -> Dict[str, Any]:
        """Load security policies"""
        return {
            'password_policy': {
                'min_length': 12,
                'require_uppercase': True,
                'require_lowercase': True,
                'require_numbers': True,
                'require_symbols': True,
                'max_age_days': 90
            },
            'access_control': {
                'principle': 'least_privilege',
                'default_deny': True,
                'require_justification': True,
                'approval_required': ['RESTRICTED', 'TOP_SECRET']
            },
            'data_protection': {
                'encryption_at_rest': True,
                'encryption_in_transit': True,
                'data_classification_required': True,
                'retention_policies': True
            },
            'incident_response': {
                'auto_containment': True,
                'notification_required': True,
                'escalation_thresholds': {
                    'HIGH': 300,  # 5 minutes
                    'CRITICAL': 60  # 1 minute
                }
            }
        }
    
    def _initialize_signing_key(self) -> bytes:
        """Initialize cryptographic signing key"""
        try:
            key_path = Path("cybex/data/security/signing.key")
            if key_path.exists():
                with open(key_path, 'rb') as f:
                    return f.read()
            else:
                # Generate new key
                key = secrets.token_bytes(32)
                key_path.parent.mkdir(parents=True, exist_ok=True)
                with open(key_path, 'wb') as f:
                    f.write(key)
                return key
        except Exception as e:
            if self.logger:
                self.logger.error(f"Could not initialize signing key: {e}")
            return secrets.token_bytes(32)
    
    def _initialize_encryption_key(self) -> bytes:
        """Initialize encryption key"""
        try:
            key_path = Path("cybex/data/security/encryption.key")
            if key_path.exists():
                with open(key_path, 'rb') as f:
                    return f.read()
            else:
                # Generate new key
                key = secrets.token_bytes(32)
                key_path.parent.mkdir(parents=True, exist_ok=True)
                with open(key_path, 'wb') as f:
                    f.write(key)
                return key
        except Exception as e:
            if self.logger:
                self.logger.error(f"Could not initialize encryption key: {e}")
            return secrets.token_bytes(32)
    
    def _initialize_security_subsystems(self):
        """Initialize security monitoring subsystems"""
        try:
            # Start security monitoring
            self.security_monitor.start_monitoring()
            
            # Initialize compliance checking
            self.compliance_checker.initialize()
            
            # Start threat detection
            self.threat_detector.start_detection()
            
            if self.logger:
                self.logger.info("Security subsystems initialized")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Security subsystem initialization failed: {e}")
    
    def validate_operation(self, operation: str, user_context: Dict[str, Any], 
                          resource: str = None, data: Any = None) -> Tuple[bool, str, Dict[str, Any]]:
        """Comprehensive operation validation"""
        try:
            validation_result = {
                'approved': False,
                'security_level': SecurityLevel.INTERNAL.value,
                'risk_score': 0,
                'required_actions': [],
                'compliance_notes': [],
                'audit_required': True
            }
            
            # Determine security level
            security_level = self._determine_security_level(operation, resource)
            validation_result['security_level'] = security_level.value
            
            # Risk assessment
            risk_score = self._assess_operation_risk(operation, user_context, resource, data)
            validation_result['risk_score'] = risk_score
            
            # Policy compliance check
            compliance_result = self.compliance_checker.check_operation_compliance(
                operation, user_context, security_level, risk_score
            )
            validation_result['compliance_notes'] = compliance_result.get('notes', [])
            
            # Threat detection
            threat_indicators = self.threat_detector.analyze_operation(
                operation, user_context, resource, data
            )
            
            # Make approval decision
            if risk_score > 80:
                validation_result['approved'] = False
                validation_result['required_actions'] = ['Risk too high - operation blocked']
            elif security_level in [SecurityLevel.RESTRICTED, SecurityLevel.TOP_SECRET]:
                validation_result['approved'] = False
                validation_result['required_actions'] = ['Requires additional authorization']
            elif threat_indicators:
                validation_result['approved'] = False
                validation_result['required_actions'] = [f'Threat detected: {", ".join(threat_indicators)}']
            elif not compliance_result.get('compliant', False):
                validation_result['approved'] = False
                validation_result['required_actions'] = ['Compliance requirements not met']
            else:
                validation_result['approved'] = True
            
            # Log security event
            if validation_result['approved']:
                self._log_security_event(
                    event_type="OPERATION_APPROVED",
                    severity="INFO",
                    source=user_context.get('user_id', 'unknown'),
                    target=resource or operation,
                    description=f"Operation {operation} approved",
                    user_context=user_context,
                    risk_score=risk_score
                )
            else:
                self._log_security_event(
                    event_type="OPERATION_BLOCKED",
                    severity="WARNING",
                    source=user_context.get('user_id', 'unknown'),
                    target=resource or operation,
                    description=f"Operation {operation} blocked: {', '.join(validation_result['required_actions'])}",
                    user_context=user_context,
                    risk_score=risk_score
                )
            
            return validation_result['approved'], validation_result['required_actions'][0] if validation_result['required_actions'] else "Approved", validation_result
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Operation validation failed: {e}")
            return False, f"Validation error: {str(e)}", {'error': str(e)}
    
    def _determine_security_level(self, operation: str, resource: str = None) -> SecurityLevel:
        """Determine security level for operation"""
        # High-risk operations
        if operation in ['execute_command', 'cleanup_temp_files', 'system_hardening']:
            return SecurityLevel.CONFIDENTIAL
        
        # Admin operations
        if operation.startswith('admin_') or 'admin' in operation.lower():
            return SecurityLevel.RESTRICTED
        
        # Security operations
        if operation in ['security_audit', 'network_security_scan']:
            return SecurityLevel.CONFIDENTIAL
        
        # Default level
        return SecurityLevel.INTERNAL
    
    def _assess_operation_risk(self, operation: str, user_context: Dict[str, Any], 
                              resource: str = None, data: Any = None) -> int:
        """Assess risk score for operation (0-100)"""
        risk_score = 0
        
        # Base risk by operation type
        high_risk_operations = ['execute_command', 'cleanup_temp_files', 'system_hardening']
        medium_risk_operations = ['security_audit', 'network_security_scan', 'performance_analysis']
        
        if operation in high_risk_operations:
            risk_score += 40
        elif operation in medium_risk_operations:
            risk_score += 20
        else:
            risk_score += 10
        
        # User context risk factors
        user_expertise = user_context.get('expertise_level', 'BEGINNER')
        if user_expertise == 'BEGINNER':
            risk_score += 20
        elif user_expertise == 'INTERMEDIATE':
            risk_score += 10
        
        # Session risk factors
        session_duration = user_context.get('session_duration', 0)
        if session_duration > 8 * 3600:  # More than 8 hours
            risk_score += 15
        
        # System state risk factors
        system_load = user_context.get('system_load', {})
        if system_load.get('cpu', 0) > 80:
            risk_score += 10
        if system_load.get('memory', 0) > 90:
            risk_score += 15
        
        return min(risk_score, 100)
    
    def _log_security_event(self, event_type: str, severity: str, source: str, 
                           target: str, description: str, user_context: Dict[str, Any],
                           risk_score: int = 0):
        """Log security event"""
        try:
            event = SecurityEvent(
                event_id=str(uuid.uuid4()),
                timestamp=time.time(),
                event_type=event_type,
                severity=severity,
                source=source,
                target=target,
                description=description,
                user_context=user_context,
                system_context=self._get_system_context(),
                risk_score=risk_score,
                mitigation_actions=[],
                compliance_impact=[]
            )
            
            self.active_security_events.append(event)
            
            # Keep only recent events in memory
            if len(self.active_security_events) > 1000:
                self.active_security_events = self.active_security_events[-1000:]
            
            # Persist to audit log
            if self.audit_enabled:
                self._write_audit_entry(event)
            
            if self.logger:
                self.logger.info(f"Security event logged: {event_type} - {description}")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to log security event: {e}")
    
    def _write_audit_entry(self, event: SecurityEvent):
        """Write entry to audit trail"""
        try:
            audit_entry = AuditTrailEntry(
                entry_id=str(uuid.uuid4()),
                timestamp=event.timestamp,
                user_id=event.user_context.get('user_id', 'unknown'),
                session_id=event.user_context.get('session_id', 'unknown'),
                operation=event.event_type,
                resource=event.target,
                result=event.severity,
                security_level=self._determine_security_level(event.event_type).value,
                risk_assessment=f"Risk Score: {event.risk_score}",
                data_hash=self._calculate_data_hash(event),
                signature=self._sign_audit_entry(event)
            )
            
            self.audit_trail.append(audit_entry)
            
            # Persist audit trail
            self._persist_audit_trail()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to write audit entry: {e}")
    
    def _calculate_data_hash(self, event: SecurityEvent) -> str:
        """Calculate hash of event data for integrity"""
        try:
            event_data = json.dumps(asdict(event), sort_keys=True, default=str)
            return hashlib.sha256(event_data.encode()).hexdigest()
        except Exception:
            return "hash_error"
    
    def _sign_audit_entry(self, event: SecurityEvent) -> str:
        """Sign audit entry for non-repudiation"""
        try:
            event_data = json.dumps(asdict(event), sort_keys=True, default=str)
            signature = hmac.new(
                self.signing_key,
                event_data.encode(),
                hashlib.sha256
            ).hexdigest()
            return signature
        except Exception:
            return "signature_error"
    
    def _get_system_context(self) -> Dict[str, Any]:
        """Get current system context for security events"""
        try:
            import psutil
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'active_connections': len(psutil.net_connections()),
                'process_count': len(psutil.pids()),
                'timestamp': time.time()
            }
        except Exception:
            return {'timestamp': time.time(), 'error': 'context_unavailable'}
    
    def _persist_audit_trail(self):
        """Persist audit trail to disk"""
        try:
            audit_path = Path("cybex/data/security/audit_trail.json")
            audit_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Keep only recent entries in memory
            if len(self.audit_trail) > 10000:
                # Archive old entries
                self._archive_old_audit_entries()
                self.audit_trail = self.audit_trail[-5000:]
            
            # Write current audit trail
            with open(audit_path, 'w') as f:
                json.dump([asdict(entry) for entry in self.audit_trail], f, indent=2, default=str)
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to persist audit trail: {e}")
    
    def _archive_old_audit_entries(self):
        """Archive old audit entries"""
        try:
            archive_path = Path(f"cybex/data/security/audit_archive_{int(time.time())}.json")
            archive_path.parent.mkdir(parents=True, exist_ok=True)
            
            old_entries = self.audit_trail[:-5000]
            with open(archive_path, 'w') as f:
                json.dump([asdict(entry) for entry in old_entries], f, indent=2, default=str)
                
            if self.logger:
                self.logger.info(f"Archived {len(old_entries)} audit entries")
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to archive audit entries: {e}")
    
    def get_security_status(self) -> Dict[str, Any]:
        """Get current security status"""
        return {
            'threat_level': self.current_threat_level.value,
            'active_events': len(self.active_security_events),
            'audit_entries': len(self.audit_trail),
            'compliance_mode': self.compliance_mode,
            'security_policies_active': len(self.security_policies),
            'monitoring_active': self.security_monitor.is_active(),
            'last_threat_assessment': time.time()
        }


class SecurityMonitor:
    """Real-time security monitoring"""
    
    def __init__(self, security_arch):
        self.security_arch = security_arch
        self.monitoring_active = False
        self.monitoring_thread = None
    
    def start_monitoring(self):
        """Start security monitoring"""
        if not self.monitoring_active:
            self.monitoring_active = True
            self.monitoring_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitoring_thread.start()
    
    def _monitor_loop(self):
        """Security monitoring loop"""
        while self.monitoring_active:
            try:
                # Monitor system for security events
                self._check_system_security()
                time.sleep(30)  # Check every 30 seconds
            except Exception as e:
                if self.security_arch.logger:
                    self.security_arch.logger.error(f"Security monitoring error: {e}")
                time.sleep(60)
    
    def _check_system_security(self):
        """Check system security status"""
        # Simplified implementation
        pass
    
    def is_active(self) -> bool:
        """Check if monitoring is active"""
        return self.monitoring_active


class ComplianceChecker:
    """Compliance checking and validation"""
    
    def __init__(self, security_arch):
        self.security_arch = security_arch
        self.compliance_rules = self._load_compliance_rules()
    
    def initialize(self):
        """Initialize compliance checker"""
        pass
    
    def _load_compliance_rules(self) -> Dict[str, Any]:
        """Load compliance rules"""
        return {
            'SOX': {'data_retention': 7, 'audit_required': True},
            'GDPR': {'data_protection': True, 'consent_required': True},
            'ISO27001': {'security_controls': True, 'risk_assessment': True}
        }
    
    def check_operation_compliance(self, operation: str, user_context: Dict[str, Any],
                                  security_level: SecurityLevel, risk_score: int) -> Dict[str, Any]:
        """Check operation compliance"""
        return {
            'compliant': True,
            'notes': [],
            'requirements_met': True
        }


class ThreatDetector:
    """Threat detection and analysis"""
    
    def __init__(self, security_arch):
        self.security_arch = security_arch
        self.detection_active = False
    
    def start_detection(self):
        """Start threat detection"""
        self.detection_active = True
    
    def analyze_operation(self, operation: str, user_context: Dict[str, Any],
                         resource: str = None, data: Any = None) -> List[str]:
        """Analyze operation for threats"""
        threats = []
        
        # Check for suspicious patterns
        if operation == 'execute_command' and data:
            command = str(data).lower()
            if any(suspicious in command for suspicious in ['format', 'del', 'rm -rf', 'shutdown']):
                threats.append('Potentially destructive command detected')
        
        return threats


def create_security_first_architecture(log_manager=None, config_manager=None) -> SecurityFirstArchitecture:
    """Factory function to create security-first architecture"""
    return SecurityFirstArchitecture(log_manager, config_manager)


# Example usage
if __name__ == "__main__":
    security_arch = create_security_first_architecture()
    
    # Test operation validation
    user_context = {
        'user_id': 'test_user',
        'session_id': 'test_session',
        'expertise_level': 'INTERMEDIATE'
    }
    
    approved, message, details = security_arch.validate_operation(
        'security_audit', user_context, 'system'
    )
    
    print(f"Operation approved: {approved}")
    print(f"Message: {message}")
    print(f"Security status: {security_arch.get_security_status()}")
