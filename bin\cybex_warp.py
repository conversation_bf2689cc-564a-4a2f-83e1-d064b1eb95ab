#!/usr/bin/env python3
"""
Cybex Warp - Advanced AI Terminal
Versione avanzata di Warp con AI enterprise e tool avanzati
"""

import os
import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def check_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check required modules
    required_modules = [
        'psutil', 'requests', 'colorama', 'pathlib'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module} missing")
    
    if missing_modules:
        print(f"\n📦 Install missing modules:")
        print(f"pip install {' '.join(missing_modules)}")
        return False
    
    return True

def show_banner():
    """Show Cybex Warp banner"""
    banner = """
╔═══════════════════════════════════════════════════════════════════════════════╗
║                                                                               ║
║   ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ██╗    ██╗ █████╗ ██████╗ ██████╗  ║
║   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██║    ██║██╔══██╗██╔══██╗██╔══██╗ ║
║   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     ██║ █╗ ██║███████║██████╔╝██████╔╝ ║
║   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██║███╗██║██╔══██║██╔══██╗██╔═══╝  ║
║   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ╚███╔███╔╝██║  ██║██║  ██║██║      ║
║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝     ╚══╝╚══╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝      ║
║                                                                               ║
║                        🚀 Advanced AI Terminal                               ║
║                     The Next Generation of Warp                              ║
║                                                                               ║
║  🎯 Features:                                                                 ║
║    • Natural Language AI Commands                                            ║
║    • Advanced System Tools                                                   ║
║    • Real-time Operation Monitoring                                          ║
║    • Warp-style Modern Interface                                             ║
║    • Enterprise Security & Safety                                            ║
║                                                                               ║
║  💡 Examples:                                                                 ║
║    • "Scansiona disco C"                                                     ║
║    • "Elimina file temp dal disco C"                                         ║
║    • "Mostra processi che usano più CPU"                                     ║
║    • systeminfo, tasklist, ping google.com                                   ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_ollama():
    """Check Ollama availability"""
    print("\n🤖 Checking AI capabilities...")
    
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ Ollama server running with {len(models)} models")
            
            # Check for recommended model
            model_names = [model.get('name', '') for model in models]
            if any('gemma' in name for name in model_names):
                print("✅ Recommended AI model available")
            else:
                print("⚠️  Consider installing: ollama pull gemma3:4b")
            
            return True
        else:
            print("❌ Ollama server not responding")
            return False
    
    except Exception as e:
        print(f"❌ Ollama not available: {e}")
        print("💡 Install Ollama from: https://ollama.ai")
        print("💡 Then run: ollama pull gemma3:4b")
        return False

def main():
    """Main launcher"""
    show_banner()
    
    print("🔧 Initializing Cybex Warp...")
    
    # Check requirements
    if not check_requirements():
        print("\n❌ System requirements not met")
        input("Press Enter to exit...")
        return
    
    # Check Ollama (optional but recommended)
    ollama_available = check_ollama()
    if not ollama_available:
        print("\n⚠️  AI features will be limited without Ollama")
        choice = input("Continue anyway? (Y/n): ").strip().lower()
        if choice in ['n', 'no']:
            return
    
    print("\n🚀 Starting Cybex Warp...")
    time.sleep(1)
    
    try:
        # Import and start Warp interface
        from cybex.interfaces.ui_warp_style import CybexWarpInterface
        
        interface = CybexWarpInterface()
        interface.run()
        
    except ImportError as e:
        print(f"❌ Failed to import Cybex components: {e}")
        print("💡 Make sure all Cybex files are in the correct location")
        input("Press Enter to exit...")
    
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
