#!/usr/bin/env python3
"""
Cybex Warp-Style Interface
Interfaccia in stile Warp con AI avanzato e capacità enterprise
"""

import os
import sys
import time
import platform
import subprocess
from typing import Dict, List, Optional, Any
from pathlib import Path
from dataclasses import dataclass

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from cybex.core.cybex_core import CybexCore
from cybex.modules.ollama_interface import OllamaInterface
from cybex.modules.agent_tools import AgentTools
from cybex.modules.operation_monitor import init_global_monitor

class WarpColors:
    """Warp-style color scheme"""
    # Warp's signature colors
    BACKGROUND = "\033[48;2;13;17;23m"      # Dark background
    FOREGROUND = "\033[38;2;205;214;244m"   # Light text
    ACCENT = "\033[38;2;137;180;250m"       # Blue accent
    SUCCESS = "\033[38;2;166;227;161m"      # Green
    WARNING = "\033[38;2;249;226;175m"      # Yellow
    ERROR = "\033[38;2;243;139;168m"        # Red/Pink
    MUTED = "\033[38;2;108;112;134m"        # Gray
    BRIGHT = "\033[38;2;255;255;255m"       # White
    PROMPT = "\033[38;2;137;180;250m"       # Blue for prompt
    RESET = "\033[0m"
    
    # Special effects
    BOLD = "\033[1m"
    DIM = "\033[2m"
    ITALIC = "\033[3m"

@dataclass
class WarpBlock:
    """Represents a Warp-style command block"""
    command: str
    output: str
    status: str  # "running", "success", "error"
    timestamp: float
    execution_time: Optional[float] = None
    ai_suggestion: Optional[str] = None

class CybexWarpInterface:
    """Cybex interface in Warp style"""
    
    def __init__(self):
        self.running = True
        self.current_directory = os.getcwd()
        self.command_history = []
        self.blocks = []  # Command blocks like Warp
        self.ai_mode = True
        
        # Initialize Cybex components
        self._init_cybex_components()
        
        # Warp-style settings
        self.show_ai_suggestions = True
        self.show_command_preview = True
        self.auto_complete = True
        
        print(f"{WarpColors.BACKGROUND}")  # Set background
        self._clear_screen()
        self._show_welcome()
    
    def _init_cybex_components(self):
        """Initialize Cybex core components"""
        try:
            print(f"{WarpColors.MUTED}Initializing Cybex Enterprise...{WarpColors.RESET}")
            
            # Initialize core
            self.core = CybexCore()
            
            # Initialize Ollama interface
            self.ollama_interface = OllamaInterface(
                self.core.config_manager,
                self.core.log_manager
            )
            
            # Initialize agent tools
            self.agent_tools = AgentTools(self.core.log_manager)
            
            # Initialize operation monitor
            self.operation_monitor = init_global_monitor(self.core.log_manager)
            
            print(f"{WarpColors.SUCCESS}✓ Cybex Enterprise initialized{WarpColors.RESET}")
            
        except Exception as e:
            print(f"{WarpColors.ERROR}✗ Failed to initialize Cybex: {e}{WarpColors.RESET}")
            self.core = None
            self.ollama_interface = None
            self.agent_tools = None
    
    def _clear_screen(self):
        """Clear screen Warp-style"""
        os.system('cls' if os.name == 'nt' else 'clear')
    
    def _show_welcome(self):
        """Show Warp-style welcome message"""
        welcome = f"""
{WarpColors.BACKGROUND}{WarpColors.BRIGHT}
╭─────────────────────────────────────────────────────────────────────────────────╮
│                                                                                 │
│  {WarpColors.ACCENT}██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ██╗    ██╗ █████╗ ██████╗ ██████╗{WarpColors.BRIGHT}   │
│  {WarpColors.ACCENT}██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██║    ██║██╔══██╗██╔══██╗██╔══██╗{WarpColors.BRIGHT}  │
│  {WarpColors.ACCENT}██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     ██║ █╗ ██║███████║██████╔╝██████╔╝{WarpColors.BRIGHT}  │
│  {WarpColors.ACCENT}██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██║███╗██║██╔══██║██╔══██╗██╔═══╝{WarpColors.BRIGHT}   │
│  {WarpColors.ACCENT}╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ╚███╔███╔╝██║  ██║██║  ██║██║{WarpColors.BRIGHT}       │
│  {WarpColors.ACCENT}╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝     ╚══╝╚══╝ ╚═╝  ╚═╝╚═╝  ╚═╝╚═╝{WarpColors.BRIGHT}       │
│                                                                                 │
│  {WarpColors.SUCCESS}Enterprise AI Terminal{WarpColors.MUTED} • Powered by Advanced AI • Version 2.0{WarpColors.BRIGHT}           │
│                                                                                 │
│  {WarpColors.FOREGROUND}Welcome to Cybex Warp - The most advanced AI terminal experience{WarpColors.BRIGHT}        │
│  {WarpColors.MUTED}Type commands naturally or use AI suggestions • Press Ctrl+C to exit{WarpColors.BRIGHT}         │
│                                                                                 │
╰─────────────────────────────────────────────────────────────────────────────────╯
{WarpColors.RESET}
"""
        print(welcome)
        
        # Show system info Warp-style
        self._show_system_info()
    
    def _show_system_info(self):
        """Show system info in Warp style"""
        try:
            # Get current model
            current_model = "gemma3:4b"
            if self.core and self.core.config_manager:
                current_model = self.core.config_manager.get('ollama.model', 'gemma3:4b')
            
            # Get system info
            system = platform.system()
            python_version = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
            
            info_bar = f"""
{WarpColors.BACKGROUND}{WarpColors.MUTED}┌─ System Info ────────────────────────────────────────────────────────────────┐
│ {WarpColors.FOREGROUND}OS: {WarpColors.ACCENT}{system}{WarpColors.MUTED} │ {WarpColors.FOREGROUND}Python: {WarpColors.ACCENT}{python_version}{WarpColors.MUTED} │ {WarpColors.FOREGROUND}AI Model: {WarpColors.ACCENT}{current_model}{WarpColors.MUTED} │ {WarpColors.FOREGROUND}Tools: {WarpColors.SUCCESS}7 available{WarpColors.MUTED} │
└──────────────────────────────────────────────────────────────────────────────┘{WarpColors.RESET}
"""
            print(info_bar)
            
        except Exception as e:
            print(f"{WarpColors.ERROR}Could not load system info: {e}{WarpColors.RESET}")
    
    def run(self):
        """Main Warp-style interface loop"""
        try:
            while self.running:
                self._show_prompt()
                
                try:
                    user_input = input().strip()
                    
                    if not user_input:
                        continue
                    
                    # Handle special commands
                    if user_input.lower() in ['exit', 'quit', 'q']:
                        self._handle_exit()
                        break
                    elif user_input.lower() in ['clear', 'cls']:
                        self._clear_screen()
                        self._show_welcome()
                        continue
                    elif user_input.lower() in ['help', '?']:
                        self._show_help()
                        continue
                    elif user_input.startswith('cd '):
                        self._handle_cd(user_input[3:])
                        continue
                    
                    # Process command
                    self._process_command(user_input)
                    
                except KeyboardInterrupt:
                    print(f"\n{WarpColors.MUTED}Use 'exit' to quit{WarpColors.RESET}")
                    continue
                except EOFError:
                    break
        
        except Exception as e:
            print(f"{WarpColors.ERROR}Fatal error: {e}{WarpColors.RESET}")
        finally:
            self._cleanup()
    
    def _show_prompt(self):
        """Show Warp-style prompt"""
        # Get current directory (shortened)
        cwd = os.getcwd()
        if len(cwd) > 50:
            cwd = "..." + cwd[-47:]
        
        # Get git branch if in git repo
        git_branch = self._get_git_branch()
        git_info = f" {WarpColors.SUCCESS}({git_branch}){WarpColors.RESET}" if git_branch else ""
        
        # Show AI status
        ai_status = f"{WarpColors.ACCENT}🤖{WarpColors.RESET}" if self.ai_mode else f"{WarpColors.MUTED}🤖{WarpColors.RESET}"
        
        prompt = f"""
{WarpColors.BACKGROUND}{WarpColors.MUTED}┌─ Command Block ──────────────────────────────────────────────────────────────┐{WarpColors.RESET}
{WarpColors.PROMPT}cybex-warp{WarpColors.MUTED}:{WarpColors.ACCENT}{cwd}{git_info} {ai_status} {WarpColors.PROMPT}❯{WarpColors.RESET} """
        
        print(prompt, end="")
    
    def _get_git_branch(self):
        """Get current git branch"""
        try:
            result = subprocess.run(
                ['git', 'branch', '--show-current'],
                capture_output=True,
                text=True,
                timeout=2
            )
            if result.returncode == 0:
                return result.stdout.strip()
        except:
            pass
        return None
    
    def _process_command(self, command: str):
        """Process command Warp-style with AI integration"""
        start_time = time.time()
        
        # Create command block
        block = WarpBlock(
            command=command,
            output="",
            status="running",
            timestamp=start_time
        )
        
        self.blocks.append(block)
        self.command_history.append(command)
        
        # Show command block header
        self._show_command_block_header(block)
        
        try:
            # Check if it's a system command or AI request
            if self._is_system_command(command):
                # Execute as system command
                result = self._execute_system_command(command)
                block.output = result['output']
                block.status = "success" if result['success'] else "error"
            else:
                # Process with AI
                result = self._process_with_ai(command)
                block.output = result['output']
                block.status = "success" if result['success'] else "error"
                block.ai_suggestion = result.get('suggestion')
            
            block.execution_time = time.time() - start_time
            
            # Show output
            self._show_command_output(block)
            
            # Show AI suggestion if available
            if block.ai_suggestion and self.show_ai_suggestions:
                self._show_ai_suggestion(block.ai_suggestion)
        
        except Exception as e:
            block.status = "error"
            block.output = str(e)
            block.execution_time = time.time() - start_time
            self._show_command_output(block)
    
    def _is_system_command(self, command: str) -> bool:
        """Check if command should be executed as system command"""
        system_commands = [
            'dir', 'ls', 'pwd', 'echo', 'type', 'cat', 'ping', 'ipconfig',
            'systeminfo', 'tasklist', 'netstat', 'whoami', 'date', 'time'
        ]
        
        cmd_name = command.split()[0].lower()
        return cmd_name in system_commands
    
    def _execute_system_command(self, command: str) -> Dict[str, Any]:
        """Execute system command"""
        try:
            if self.agent_tools:
                # Use agent tools for better security and monitoring
                execution_id = self.agent_tools.execute_tool(
                    "execute_command",
                    {"command": command, "shell": "cmd", "timeout": 30}
                )
                
                # Wait for completion
                max_wait = 30
                wait_time = 0
                
                while wait_time < max_wait:
                    execution = self.agent_tools.get_execution_status(execution_id)
                    if execution and execution.status.value in ['completed', 'failed']:
                        break
                    time.sleep(0.5)
                    wait_time += 0.5
                
                execution = self.agent_tools.get_execution_status(execution_id)
                if execution and execution.result:
                    return {
                        'success': execution.result.success,
                        'output': execution.result.output or execution.result.error
                    }
            
            # Fallback to direct execution
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout + result.stderr
            }
            
        except Exception as e:
            return {
                'success': False,
                'output': f"Command execution error: {str(e)}"
            }
    
    def _process_with_ai(self, command: str) -> Dict[str, Any]:
        """Process command with AI"""
        try:
            if not self.ollama_interface:
                return {
                    'success': False,
                    'output': "AI interface not available"
                }
            
            # Prepare context with tools
            context = {
                'mode': 'warp_terminal',
                'current_directory': self.current_directory,
                'agent_tools': self.agent_tools,
                'operation_monitor': self.operation_monitor,
                'system_type': platform.system()
            }
            
            # Generate AI response
            response = self.ollama_interface.generate_response(command, context)
            
            if response.success:
                return {
                    'success': True,
                    'output': response.content,
                    'suggestion': self._generate_suggestion(command, response.content)
                }
            else:
                return {
                    'success': False,
                    'output': f"AI Error: {response.error}"
                }
        
        except Exception as e:
            return {
                'success': False,
                'output': f"AI processing error: {str(e)}"
            }
    
    def _generate_suggestion(self, command: str, response: str) -> str:
        """Generate AI suggestion for next command"""
        # Simple suggestion logic - can be enhanced
        suggestions = [
            "Try: /ops to see operations monitor",
            "Use: /tools to see available tools", 
            "Type: help for more commands",
            "Suggestion: Use 'clear' to clean terminal"
        ]
        
        import random
        return random.choice(suggestions)
    
    def _show_command_block_header(self, block: WarpBlock):
        """Show Warp-style command block header"""
        status_icon = "⚡" if block.status == "running" else ("✓" if block.status == "success" else "✗")
        status_color = WarpColors.ACCENT if block.status == "running" else (WarpColors.SUCCESS if block.status == "success" else WarpColors.ERROR)
        
        header = f"""
{WarpColors.MUTED}├─ {status_color}{status_icon}{WarpColors.MUTED} {WarpColors.FOREGROUND}{block.command}{WarpColors.MUTED} ─────────────────────────────────────────────────────────────┤{WarpColors.RESET}"""
        
        print(header)
    
    def _show_command_output(self, block: WarpBlock):
        """Show command output Warp-style"""
        if not block.output:
            return
        
        # Color output based on status
        output_color = WarpColors.SUCCESS if block.status == "success" else WarpColors.ERROR
        
        # Show execution time
        time_info = ""
        if block.execution_time:
            time_info = f" {WarpColors.MUTED}({block.execution_time:.2f}s){WarpColors.RESET}"
        
        print(f"{output_color}{block.output}{WarpColors.RESET}{time_info}")
    
    def _show_ai_suggestion(self, suggestion: str):
        """Show AI suggestion Warp-style"""
        suggestion_box = f"""
{WarpColors.MUTED}├─ {WarpColors.ACCENT}💡 AI Suggestion{WarpColors.MUTED} ──────────────────────────────────────────────────────────────┤
{WarpColors.MUTED}│ {WarpColors.FOREGROUND}{suggestion}{WarpColors.MUTED}
└──────────────────────────────────────────────────────────────────────────────┘{WarpColors.RESET}"""
        
        print(suggestion_box)
    
    def _handle_cd(self, path: str):
        """Handle cd command"""
        try:
            if not path:
                path = os.path.expanduser("~")
            
            new_path = os.path.abspath(os.path.expanduser(path))
            
            if os.path.exists(new_path) and os.path.isdir(new_path):
                os.chdir(new_path)
                self.current_directory = new_path
                print(f"{WarpColors.SUCCESS}Changed directory to: {new_path}{WarpColors.RESET}")
            else:
                print(f"{WarpColors.ERROR}Directory not found: {path}{WarpColors.RESET}")
        
        except Exception as e:
            print(f"{WarpColors.ERROR}cd error: {e}{WarpColors.RESET}")
    
    def _show_help(self):
        """Show Warp-style help"""
        help_text = f"""
{WarpColors.BACKGROUND}{WarpColors.BRIGHT}
╭─ Cybex Warp Help ───────────────────────────────────────────────────────────────╮
│                                                                                 │
│  {WarpColors.ACCENT}🚀 NATURAL LANGUAGE COMMANDS{WarpColors.BRIGHT}                                                  │
│  {WarpColors.FOREGROUND}• "Scansiona disco C"           - AI disk analysis{WarpColors.BRIGHT}                        │
│  {WarpColors.FOREGROUND}• "Elimina file temp"           - Safe cleanup with preview{WarpColors.BRIGHT}               │
│  {WarpColors.FOREGROUND}• "Mostra processi CPU"         - Process analysis{WarpColors.BRIGHT}                        │
│  {WarpColors.FOREGROUND}• "Stato del sistema"           - System overview{WarpColors.BRIGHT}                         │
│                                                                                 │
│  {WarpColors.ACCENT}⚡ SYSTEM COMMANDS{WarpColors.BRIGHT}                                                             │
│  {WarpColors.FOREGROUND}• dir, ls                       - List directory{WarpColors.BRIGHT}                          │
│  {WarpColors.FOREGROUND}• systeminfo                    - System information{WarpColors.BRIGHT}                      │
│  {WarpColors.FOREGROUND}• tasklist                      - Running processes{WarpColors.BRIGHT}                       │
│  {WarpColors.FOREGROUND}• ping <host>                   - Network ping{WarpColors.BRIGHT}                            │
│                                                                                 │
│  {WarpColors.ACCENT}🛠️  DIRECT TOOLS{WarpColors.BRIGHT}                                                               │
│  {WarpColors.FOREGROUND}• /tools                        - Show available tools{WarpColors.BRIGHT}                    │
│  {WarpColors.FOREGROUND}• /ops                          - Operations monitor{WarpColors.BRIGHT}                      │
│  {WarpColors.FOREGROUND}• /tool scan_disk drive=C       - Direct tool execution{WarpColors.BRIGHT}                  │
│                                                                                 │
│  {WarpColors.ACCENT}🎛️  TERMINAL COMMANDS{WarpColors.BRIGHT}                                                          │
│  {WarpColors.FOREGROUND}• clear, cls                    - Clear terminal{WarpColors.BRIGHT}                          │
│  {WarpColors.FOREGROUND}• cd <path>                     - Change directory{WarpColors.BRIGHT}                        │
│  {WarpColors.FOREGROUND}• exit, quit                    - Exit terminal{WarpColors.BRIGHT}                           │
│  {WarpColors.FOREGROUND}• help, ?                       - Show this help{WarpColors.BRIGHT}                          │
│                                                                                 │
╰─────────────────────────────────────────────────────────────────────────────────╯{WarpColors.RESET}
"""
        print(help_text)
    
    def _handle_exit(self):
        """Handle exit command"""
        print(f"\n{WarpColors.ACCENT}Thanks for using Cybex Warp!{WarpColors.RESET}")
        print(f"{WarpColors.MUTED}Goodbye! 👋{WarpColors.RESET}")
        self.running = False
    
    def _cleanup(self):
        """Cleanup resources"""
        print(f"{WarpColors.RESET}")  # Reset colors
        
        if hasattr(self, 'core') and self.core:
            try:
                self.core.shutdown()
            except:
                pass


def main():
    """Main entry point"""
    try:
        interface = CybexWarpInterface()
        interface.run()
    except KeyboardInterrupt:
        print(f"\n{WarpColors.MUTED}Interrupted by user{WarpColors.RESET}")
    except Exception as e:
        print(f"{WarpColors.ERROR}Fatal error: {e}{WarpColors.RESET}")


if __name__ == "__main__":
    main()
