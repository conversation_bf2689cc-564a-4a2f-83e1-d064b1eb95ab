@echo off
title CYBEX Enterprise - Futuristic Interface Launcher
color 0B

:MAIN_MENU
cls
echo.
echo    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗██╗   ██╗████████╗██╗   ██╗██████╗ ███████╗
echo   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝██║   ██║╚══██╔══╝██║   ██║██╔══██╗██╔════╝
echo   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██║   ██║   ██║   ██║   ██║██████╔╝█████╗  
echo   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║   ██║   ██║   ██║   ██║██╔══██╗██╔══╝  
echo   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ██║     ╚██████╔╝   ██║   ╚██████╔╝██║  ██║███████╗
echo    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚═╝      ╚═════╝    ╚═╝    ╚═════╝ ╚═╝  ╚═╝╚══════╝
echo.
echo                           🚀 CYBEX ENTERPRISE - FUTURISTIC INTERFACE
echo                              Choose Your Experience Level
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                            🎯 INTERFACE OPTIONS                             ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣
echo ║                                                                              ║
echo ║  1. 🌐 WEB INTERFACE (Modern)     - React-like web experience               ║
echo ║     • Monaco Code Editor          • Real-time WebSockets                    ║
echo ║     • Responsive Design           • Performance Monitoring                  ║
echo ║                                                                              ║
echo ║  2. 🖥️ DESKTOP APP (Native)       - Native desktop application              ║
echo ║     • CEF Python Integration      • Native window experience               ║
echo ║     • Offline capable             • System integration                     ║
echo ║                                                                              ║
echo ║  3. 🎨 CLASSIC GUI (Tkinter)      - Traditional desktop interface          ║
echo ║     • Lightweight                 • Always works                           ║
echo ║     • No dependencies             • Familiar interface                     ║
echo ║                                                                              ║
echo ║  4. 💻 TERMINAL MODE (CLI)         - Command-line interface                 ║
echo ║     • Pure terminal               • Maximum performance                     ║
echo ║     • SSH compatible              • Minimal resources                      ║
echo ║                                                                              ║
echo ║  5. 🔧 SYSTEM CHECK                - Check requirements and status          ║
echo ║                                                                              ║
echo ║  0. ❌ EXIT                        - Close launcher                          ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
set /p choice="🎯 Select your interface (1-5, 0 to exit): "

if "%choice%"=="1" goto WEB_INTERFACE
if "%choice%"=="2" goto DESKTOP_APP
if "%choice%"=="3" goto CLASSIC_GUI
if "%choice%"=="4" goto TERMINAL_MODE
if "%choice%"=="5" goto SYSTEM_CHECK
if "%choice%"=="0" goto EXIT
goto INVALID_CHOICE

:WEB_INTERFACE
cls
echo.
echo 🌐 LAUNCHING WEB INTERFACE...
echo ═══════════════════════════════════
echo.
echo 💡 Features:
echo    • Modern React-like interface
echo    • Real-time WebSocket communication
echo    • Monaco Code Editor (VSCode)
echo    • Responsive futuristic design
echo    • Performance monitoring
echo.
echo 🔗 Will open at: http://127.0.0.1:8080
echo 💡 Browser will open automatically
echo.
pause
python launch_web_interface.py
goto MAIN_MENU

:DESKTOP_APP
cls
echo.
echo 🖥️ LAUNCHING DESKTOP APP...
echo ═══════════════════════════════
echo.
echo 💡 Features:
echo    • Native desktop application
echo    • CEF Python integration
echo    • System tray support
echo    • Offline capabilities
echo.
echo ⚠️ Note: Requires CEF Python (will auto-install if needed)
echo.
pause
python cybex\interfaces\desktop_app.py
goto MAIN_MENU

:CLASSIC_GUI
cls
echo.
echo 🎨 LAUNCHING CLASSIC GUI...
echo ═══════════════════════════════
echo.
echo 💡 Features:
echo    • Traditional Tkinter interface
echo    • Lightweight and fast
echo    • No additional dependencies
echo    • Ollama monitor integrated
echo.
pause
python bin\cybex_enterprise_launcher.py
goto MAIN_MENU

:TERMINAL_MODE
cls
echo.
echo 💻 LAUNCHING TERMINAL MODE...
echo ═════════════════════════════════
echo.
echo 💡 Features:
echo    • Pure command-line interface
echo    • Maximum performance
echo    • SSH compatible
echo    • Minimal resource usage
echo.
pause
python -c "from cybex.core.cybex_core import CybexCore; core = CybexCore(); core.run_cli()"
goto MAIN_MENU

:SYSTEM_CHECK
cls
echo.
echo 🔧 SYSTEM CHECK
echo ═══════════════
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
) else (
    for /f "tokens=2" %%i in ('python --version 2^>^&1') do echo ✅ Python %%i found
)

REM Check CYBEX structure
if exist "cybex" (
    echo ✅ CYBEX directory found
) else (
    echo ❌ CYBEX directory missing
)

REM Check web modules
python -c "import fastapi, uvicorn" >nul 2>&1
if errorlevel 1 (
    echo ❌ Web interface modules missing
    echo 💡 Run option 1 to auto-install
) else (
    echo ✅ Web interface modules available
)

REM Check CEF Python
python -c "import cefpython3" >nul 2>&1
if errorlevel 1 (
    echo ❌ CEF Python not available (for desktop app)
    echo 💡 Will auto-install when needed
) else (
    echo ✅ CEF Python available
)

REM Check Ollama
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ❌ Ollama server not running
    echo 💡 Start Ollama for AI features
) else (
    echo ✅ Ollama server running
    for /f %%i in ('curl -s http://localhost:11434/api/tags ^| python -c "import sys,json; data=json.load(sys.stdin); print(len(data.get('models',[])))" 2^>nul') do (
        if defined %%i echo 🤖 %%i models available
    )
)

echo.
echo 📊 SYSTEM STATUS COMPLETE
echo.
pause
goto MAIN_MENU

:INVALID_CHOICE
cls
echo.
echo ❌ Invalid choice. Please select 1-5 or 0 to exit.
echo.
pause
goto MAIN_MENU

:EXIT
cls
echo.
echo 👋 Thank you for using CYBEX Enterprise!
echo.
echo    ╔═══════════════════════════════════════╗
echo    ║     🚀 CYBEX ENTERPRISE CLOSED       ║
echo    ║                                       ║
echo    ║   Advanced AI Assistant               ║
echo    ║   AGTECHdesigne - 2025               ║
echo    ╚═══════════════════════════════════════╝
echo.
timeout /t 3 >nul
exit /b 0
