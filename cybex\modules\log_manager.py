"""
Log Manager Module
Handles logging configuration, file rotation, and log management for Cybex
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional, Dict, Any, List
from pathlib import Path


class LogManager:
    """
    Manages logging configuration and operations for Cybex
    """
    
    def __init__(self, config_manager):
        """Initialize log manager with configuration"""
        self.config_manager = config_manager
        self.logging_config = config_manager.get_section('logging')
        
        # Ensure logs directory exists
        log_path = Path(self.logging_config.get('file_path', 'logs/cybex.log'))
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Setup logging
        self._setup_logging()
        
        # Get main logger
        self.logger = logging.getLogger('cybex')
        self.logger.info("Log Manager initialized")
    
    def _setup_logging(self) -> None:
        """Setup logging configuration"""
        # Clear any existing handlers
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHand<PERSON>(handler)
        
        # Configure root logger
        log_level = getattr(logging, self.logging_config.get('level', 'INFO').upper())
        logging.basicConfig(level=log_level, handlers=[])
        
        # Create formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            '%(levelname)s: %(message)s'
        )
        
        # Setup file handler with rotation
        file_path = self.logging_config.get('file_path', 'logs/cybex.log')
        max_bytes = self._parse_size(self.logging_config.get('max_file_size', '10MB'))
        backup_count = self.logging_config.get('backup_count', 5)
        
        file_handler = logging.handlers.RotatingFileHandler(
            file_path,
            maxBytes=max_bytes,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(detailed_formatter)
        file_handler.setLevel(log_level)
        
        # Setup console handler if enabled
        handlers = [file_handler]
        if self.logging_config.get('console_output', True):
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(simple_formatter)
            console_handler.setLevel(log_level)
            handlers.append(console_handler)
        
        # Configure cybex logger
        cybex_logger = logging.getLogger('cybex')
        cybex_logger.setLevel(log_level)
        cybex_logger.handlers.clear()
        
        for handler in handlers:
            cybex_logger.addHandler(handler)
        
        # Prevent propagation to root logger
        cybex_logger.propagate = False
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string (e.g., '10MB') to bytes"""
        size_str = size_str.upper().strip()
        
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            # Assume bytes
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger instance for a specific module"""
        return logging.getLogger(f'cybex.{name}')
    
    def log_command_execution(self, command: str, result: str, success: bool, 
                            execution_time: float = 0.0) -> None:
        """Log command execution details"""
        logger = self.get_logger('commands')
        
        status = "SUCCESS" if success else "FAILED"
        message = f"[{status}] Command: {command}"
        
        if execution_time > 0:
            message += f" (took {execution_time:.2f}s)"
        
        if success:
            logger.info(message)
            if result:
                logger.debug(f"Result: {result[:200]}...")  # Truncate long results
        else:
            logger.error(message)
            if result:
                logger.error(f"Error: {result}")
    
    def log_security_event(self, event_type: str, details: str, severity: str = 'WARNING') -> None:
        """Log security-related events"""
        logger = self.get_logger('security')
        
        message = f"[{event_type}] {details}"
        
        if severity.upper() == 'CRITICAL':
            logger.critical(message)
        elif severity.upper() == 'ERROR':
            logger.error(message)
        elif severity.upper() == 'WARNING':
            logger.warning(message)
        else:
            logger.info(message)
    
    def log_system_event(self, event: str, details: str = "") -> None:
        """Log system-related events"""
        logger = self.get_logger('system')
        message = f"{event}"
        if details:
            message += f": {details}"
        logger.info(message)
    
    def log_agent_action(self, step: int, action: str, result: str, success: bool) -> None:
        """Log agent mode actions"""
        logger = self.get_logger('agent')
        
        status = "✓" if success else "✗"
        message = f"Step {step} {status}: {action}"
        
        if success:
            logger.info(message)
            if result:
                logger.debug(f"Result: {result[:100]}...")
        else:
            logger.error(message)
            if result:
                logger.error(f"Error: {result}")
    
    def get_recent_logs(self, lines: int = 50, level: str = 'INFO') -> List[str]:
        """Get recent log entries"""
        try:
            log_file = Path(self.logging_config.get('file_path', 'logs/cybex.log'))
            if not log_file.exists():
                return []
            
            with open(log_file, 'r', encoding='utf-8') as f:
                all_lines = f.readlines()
            
            # Filter by level if specified
            if level != 'ALL':
                filtered_lines = []
                for line in all_lines:
                    if f' - {level} - ' in line or level == 'INFO':
                        filtered_lines.append(line)
                all_lines = filtered_lines
            
            # Return last N lines
            return [line.strip() for line in all_lines[-lines:]]
            
        except Exception as e:
            self.logger.error(f"Failed to read recent logs: {e}")
            return []
    
    def clear_logs(self) -> bool:
        """Clear all log files"""
        try:
            log_file = Path(self.logging_config.get('file_path', 'logs/cybex.log'))
            if log_file.exists():
                log_file.unlink()
            
            # Clear backup files
            backup_count = self.logging_config.get('backup_count', 5)
            for i in range(1, backup_count + 1):
                backup_file = Path(f"{log_file}.{i}")
                if backup_file.exists():
                    backup_file.unlink()
            
            self.logger.info("Log files cleared")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to clear logs: {e}")
            return False
    
    def get_log_stats(self) -> Dict[str, Any]:
        """Get logging statistics"""
        try:
            log_file = Path(self.logging_config.get('file_path', 'logs/cybex.log'))
            
            if not log_file.exists():
                return {'exists': False}
            
            stat = log_file.stat()
            
            return {
                'exists': True,
                'size_bytes': stat.st_size,
                'size_mb': round(stat.st_size / (1024 * 1024), 2),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'lines': self._count_lines(log_file)
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get log stats: {e}")
            return {'exists': False, 'error': str(e)}
    
    def _count_lines(self, file_path: Path) -> int:
        """Count lines in a file efficiently"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return sum(1 for _ in f)
        except:
            return 0
    
    def get_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        return datetime.now().isoformat()
    
    def rotate_logs(self) -> bool:
        """Manually rotate log files"""
        try:
            # Get the file handler
            cybex_logger = logging.getLogger('cybex')
            for handler in cybex_logger.handlers:
                if isinstance(handler, logging.handlers.RotatingFileHandler):
                    handler.doRollover()
                    self.logger.info("Log files rotated manually")
                    return True
            
            self.logger.warning("No rotating file handler found")
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to rotate logs: {e}")
            return False
