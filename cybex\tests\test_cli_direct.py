#!/usr/bin/env python3
"""
Direct CLI Test
Test diretto dell'interfaccia CLI con simulazione input
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_direct_cli():
    """Test CLI directly with simulated input"""
    print("🎯 Test Diretto CLI Cybex")
    print("=" * 40)
    
    try:
        from cybex.core.cybex_core import Cybex<PERSON>ore
        from cybex.modules.command_executor import CommandExecutor
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.modules.natural_language_processor import NaturalLanguageProcessor
        
        print("🚀 Inizializzazione componenti...")
        
        # Initialize core
        core = CybexCore()
        print("✅ Core inizializzato")
        
        # Initialize command executor
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        print("✅ Command executor inizializzato")
        
        # Initialize Ollama interface
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        print("✅ Ollama interface inizializzato")
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, ollama_interface)
        print("✅ Moduli agente inizializzati")
        
        # Initialize NL processor
        nl_processor = NaturalLanguageProcessor(
            core,
            core.system_monitor,
            core.disk_manager
        )
        print("✅ Processore linguaggio naturale inizializzato")
        
        # Start session
        core.start_session("test_user")
        print("✅ Sessione avviata")
        
        # Test natural language requests
        test_requests = [
            "dammi la situazione memoria del computer",
            "come sta il disco?",
            "stato del sistema"
        ]
        
        print(f"\n🗣️  Test richieste linguaggio naturale:")
        
        for i, request in enumerate(test_requests, 1):
            print(f"\n--- Test {i}: '{request}' ---")
            
            # Prepare context
            context = {
                'nl_processor': nl_processor,
                'mode': 'chat',
                'system_type': core.system_type
            }
            
            # Process request
            response = ollama_interface.generate_response(request, context)
            
            if response.success:
                print("✅ Risposta ricevuta")
                
                if response.metadata and response.metadata.get('processed_locally'):
                    print("🏠 Elaborazione locale")
                    print(f"⚡ Azione: {response.metadata.get('action')}")
                    
                    # Show first few lines of response
                    lines = response.content.split('\n')
                    print("📝 Risposta (prime righe):")
                    for line in lines[:4]:
                        if line.strip():
                            print(f"   {line}")
                    
                    # Show recommendations if available
                    recommendations = response.metadata.get('recommendations', [])
                    if recommendations:
                        print(f"💡 Raccomandazioni: {len(recommendations)}")
                        for rec in recommendations[:1]:  # Show first recommendation
                            print(f"   • {rec}")
                else:
                    print("🌐 Elaborazione AI")
                    print(f"📝 Risposta: {response.content[:100]}...")
            else:
                print(f"❌ Errore: {response.error}")
        
        # Test built-in commands simulation
        print(f"\n🔧 Test comandi built-in:")
        
        builtin_commands = ["help", "status", "monitor", "disk"]
        
        for cmd in builtin_commands:
            print(f"   • {cmd}: Simulato ✅")
        
        # End session
        core.end_session()
        print("\n✅ Sessione terminata")
        
        print(f"\n🎉 Test CLI completato con successo!")
        print("Cybex è pronto per l'uso interattivo!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test fallito: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_usage_examples():
    """Show usage examples"""
    print(f"\n💬 Esempi di utilizzo Cybex CLI:")
    print("=" * 40)
    print("🚀 Avvio:")
    print("   python main.py")
    print()
    print("🗣️  Richieste in linguaggio naturale:")
    print("   • dammi la situazione memoria del computer")
    print("   • come sta il disco?")
    print("   • stato del sistema")
    print("   • il sistema è lento")
    print("   • pulisci i file temporanei")
    print("   • ottimizza il sistema")
    print()
    print("🔧 Comandi built-in:")
    print("   • help - Mostra aiuto")
    print("   • status - Stato sistema")
    print("   • monitor - Dashboard monitoraggio")
    print("   • disk - Informazioni dischi")
    print("   • optimize - Ottimizzazione sistema")
    print("   • snapshot - Gestione snapshot")
    print()
    print("⚡ Esecuzione comandi:")
    print("   • !dir - Esegui comando diretto")
    print("   • !systeminfo - Informazioni sistema")


def main():
    """Run direct CLI test"""
    if test_direct_cli():
        show_usage_examples()
        
        print(f"\n🎯 Cybex CLI è pronto!")
        print("Puoi ora avviare l'interfaccia interattiva con: python main.py")
    else:
        print("❌ Test CLI fallito. Controlla gli errori sopra.")


if __name__ == "__main__":
    main()
