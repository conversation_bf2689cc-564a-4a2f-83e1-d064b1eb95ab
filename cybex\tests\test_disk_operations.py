#!/usr/bin/env python3
"""
Test Disk Operations
Test delle nuove funzionalità di scansione disco e pulizia
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_disk_scan_tool():
    """Test disk scanning functionality"""
    print("💾 Testing Disk Scan Tool")
    print("=" * 35)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test disk usage scan
        print("Testing disk usage scan for C: drive...")
        
        execution_id = agent_tools.execute_tool(
            "scan_disk",
            {"drive": "C", "scan_type": "usage"}
        )
        
        # Wait for completion
        time.sleep(3)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ Disk usage scan successful")
                
                # Show key information
                lines = result.output.split('\n')[:10]
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
                
                # Show metadata
                if result.metadata and 'data' in result.metadata:
                    data = result.metadata['data']
                    if 'disk_usage' in data:
                        usage = data['disk_usage']
                        print(f"📊 Drive {usage['drive']}: {usage['used_gb']} GB used of {usage['total_gb']} GB ({usage['usage_percent']}%)")
                
                return True
            else:
                print(f"❌ Disk scan failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_temp_files_scan():
    """Test temp files scanning"""
    print(f"\n🗂️  Testing Temp Files Scan")
    print("=" * 35)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test temp files scan
        print("Scanning for temporary files...")
        
        execution_id = agent_tools.execute_tool(
            "scan_disk",
            {"drive": "C", "scan_type": "temp_files", "depth": 1}
        )
        
        # Wait for completion
        time.sleep(5)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ Temp files scan successful")
                
                # Show temp files info
                if result.metadata and 'data' in result.metadata:
                    data = result.metadata['data']
                    if 'temp_files' in data:
                        temp_info = data['temp_files']
                        print(f"📊 Total temp files: {temp_info['total_size_mb']} MB ({temp_info['total_size_gb']} GB)")
                        
                        for location in temp_info['locations'][:3]:  # Show first 3
                            if 'error' not in location:
                                print(f"  📁 {location['size_mb']} MB ({location['file_count']} files) - {Path(location['location']).name}")
                
                return True
            else:
                print(f"❌ Temp files scan failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_cleanup_preview():
    """Test cleanup preview (dry run)"""
    print(f"\n🧹 Testing Cleanup Preview")
    print("=" * 35)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test cleanup preview
        print("Running cleanup preview (dry run)...")
        
        execution_id = agent_tools.execute_tool(
            "cleanup_temp_files",
            {"cleanup_type": "temp", "dry_run": True, "drive": "C"}
        )
        
        # Wait for completion
        time.sleep(5)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ Cleanup preview successful")
                
                # Show preview info
                if result.metadata:
                    print(f"📊 Would free: {result.metadata['total_size_mb']} MB")
                    print(f"📊 Files found: {result.metadata['total_files']}")
                
                # Show first few lines of output
                lines = result.output.split('\n')[:15]
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
                
                return True
            else:
                print(f"❌ Cleanup preview failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_large_files_scan():
    """Test large files scanning"""
    print(f"\n📊 Testing Large Files Scan")
    print("=" * 35)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test large files scan
        print("Scanning for large files (>100MB)...")
        
        execution_id = agent_tools.execute_tool(
            "scan_disk",
            {"drive": "C", "scan_type": "large_files", "depth": 2}
        )
        
        # Wait for completion
        time.sleep(8)  # Large files scan takes longer
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ Large files scan successful")
                
                # Show large files info
                if result.metadata and 'data' in result.metadata:
                    data = result.metadata['data']
                    if 'large_files' in data:
                        large_files = data['large_files']
                        print(f"📊 Found {len(large_files)} large files")
                        
                        for file_info in large_files[:5]:  # Show top 5
                            print(f"  📄 {file_info['size_gb']} GB - {Path(file_info['path']).name}")
                
                return True
            else:
                print(f"❌ Large files scan failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def show_disk_operations_summary():
    """Show summary of disk operations capabilities"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    summary = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                      {SunriseColors.ACCENT}💾 DISK OPERATIONS CAPABILITIES{SunriseColors.SUCCESS}                            ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🔍 DISK SCANNING:{SunriseColors.SUCCESS}                                                               ║
║   • scan_disk - Comprehensive disk analysis                                  ║
║   • Usage analysis (free/used space)                                         ║
║   • Large files detection (>100MB)                                           ║
║   • Temporary files analysis                                                 ║
║   • Basic disk health check                                                  ║
║                                                                               ║
║ {SunriseColors.ACCENT}🧹 SAFE CLEANUP:{SunriseColors.SUCCESS}                                                                ║
║   • cleanup_temp_files - Safe temporary file removal                         ║
║   • Preview mode (dry_run=true) - See what would be deleted                  ║
║   • Multiple cleanup types: temp/cache/recycle/all                           ║
║   • Safety checks: skip files in use, system files, recent files            ║
║   • Browser cache cleaning (Chrome, Firefox, Edge)                           ║
║                                                                               ║
║ {SunriseColors.ACCENT}🔒 SECURITY FEATURES:{SunriseColors.SUCCESS}                                                           ║
║   • Always preview first (dry_run=true by default)                           ║
║   • Skip files in use or protected                                           ║
║   • Skip system files and important extensions                               ║
║   • Skip recent files (less than 1 hour old)                                ║
║   • Detailed logging and error handling                                      ║
║                                                                               ║
║ {SunriseColors.ACCENT}💬 CHAT EXAMPLES:{SunriseColors.SUCCESS}                                                               ║
║   • "Scansiona disco C"                                                      ║
║   • "Mostrami i file più grandi"                                             ║
║   • "Analizza file temporanei"                                               ║
║   • "Elimina file temp dal disco C" (preview first!)                        ║
║   • "Pulisci cache browser"                                                  ║
║                                                                               ║
║ {SunriseColors.ACCENT}⚡ DIRECT COMMANDS:{SunriseColors.SUCCESS}                                                             ║
║   • /tool scan_disk drive=C scan_type=usage                                  ║
║   • /tool scan_disk drive=C scan_type=temp_files                             ║
║   • /tool cleanup_temp_files dry_run=true                                    ║
║   • /tool cleanup_temp_files dry_run=false cleanup_type=temp                 ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(summary)


def main():
    """Run disk operations tests"""
    print("🎯 Cybex Enterprise - Disk Operations Test")
    print("=" * 60)
    
    tests = [
        ("Disk Usage Scan", test_disk_scan_tool),
        ("Temp Files Scan", test_temp_files_scan),
        ("Cleanup Preview", test_cleanup_preview),
        ("Large Files Scan", test_large_files_scan)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= 2:  # At least 2 out of 4 tests should pass
        show_disk_operations_summary()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Disk operations are working!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Your agent can now scan disks and clean temp files safely!{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Try these commands:{SunriseColors.RESET}")
        print(f"  💬 'Scansiona disco C'")
        print(f"  💬 'Mostrami i file temporanei'")
        print(f"  💬 'Elimina file temp dal disco C' (preview first!)")
        print(f"  ⌨️  /tool scan_disk drive=C scan_type=all")
        print(f"  ⌨️  /tool cleanup_temp_files dry_run=true")
    else:
        print(f"⚠️  Some tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
