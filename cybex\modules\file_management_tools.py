#!/usr/bin/env python3
"""
File Management Tools for CYBEX Enterprise
Comprehensive file and directory management capabilities
"""

import os
import shutil
import hashlib
import mimetypes
import zipfile
import tarfile
import json
import csv
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import magic
import filecmp
import tempfile
import send2trash


@dataclass
class FileInfo:
    """File information structure"""
    path: str
    name: str
    size: int
    created: float
    modified: float
    accessed: float
    is_directory: bool
    permissions: str
    owner: str
    mime_type: str
    hash_md5: Optional[str] = None
    hash_sha256: Optional[str] = None


class FileManagementTools:
    """Comprehensive file management tools"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Initialize file type detection
        try:
            self.magic = magic.Magic(mime=True)
        except Exception:
            self.magic = None
    
    def analyze_file(self, file_path: str, include_hash: bool = False) -> Dict[str, Any]:
        """Analyze file and get comprehensive information"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'success': False, 'error': 'File not found'}
            
            stat = path.stat()
            
            # Basic file info
            file_info = {
                'path': str(path.absolute()),
                'name': path.name,
                'stem': path.stem,
                'suffix': path.suffix,
                'size': stat.st_size,
                'size_human': self._format_size(stat.st_size),
                'created': stat.st_ctime,
                'modified': stat.st_mtime,
                'accessed': stat.st_atime,
                'is_directory': path.is_dir(),
                'is_file': path.is_file(),
                'is_symlink': path.is_symlink(),
                'permissions': oct(stat.st_mode)[-3:],
                'owner_uid': stat.st_uid,
                'group_gid': stat.st_gid
            }
            
            # MIME type detection
            if path.is_file():
                file_info['mime_type'] = mimetypes.guess_type(str(path))[0]
                if self.magic:
                    try:
                        file_info['mime_type_magic'] = self.magic.from_file(str(path))
                    except Exception:
                        pass
            
            # Hash calculation for files
            if include_hash and path.is_file() and stat.st_size < 100 * 1024 * 1024:  # Max 100MB
                hashes = self._calculate_hashes(path)
                file_info.update(hashes)
            
            # Directory-specific info
            if path.is_dir():
                dir_info = self._analyze_directory(path)
                file_info.update(dir_info)
            
            # File-specific info
            elif path.is_file():
                file_specific = self._analyze_file_content(path)
                file_info.update(file_specific)
            
            return {'success': True, 'file_info': file_info}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _format_size(self, size_bytes: int) -> str:
        """Format size in human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"
    
    def _calculate_hashes(self, file_path: Path) -> Dict[str, str]:
        """Calculate file hashes"""
        try:
            hashes = {}
            
            with open(file_path, 'rb') as f:
                content = f.read()
                hashes['md5'] = hashlib.md5(content).hexdigest()
                hashes['sha256'] = hashlib.sha256(content).hexdigest()
                hashes['sha1'] = hashlib.sha1(content).hexdigest()
            
            return hashes
            
        except Exception as e:
            return {'hash_error': str(e)}
    
    def _analyze_directory(self, dir_path: Path) -> Dict[str, Any]:
        """Analyze directory contents"""
        try:
            contents = list(dir_path.iterdir())
            
            analysis = {
                'total_items': len(contents),
                'directories': len([p for p in contents if p.is_dir()]),
                'files': len([p for p in contents if p.is_file()]),
                'symlinks': len([p for p in contents if p.is_symlink()]),
                'total_size': 0,
                'file_types': {},
                'largest_files': [],
                'oldest_files': [],
                'newest_files': []
            }
            
            # Analyze files
            files_info = []
            for item in contents:
                if item.is_file():
                    try:
                        stat = item.stat()
                        size = stat.st_size
                        analysis['total_size'] += size
                        
                        # File type counting
                        ext = item.suffix.lower()
                        analysis['file_types'][ext] = analysis['file_types'].get(ext, 0) + 1
                        
                        files_info.append({
                            'name': item.name,
                            'size': size,
                            'modified': stat.st_mtime
                        })
                        
                    except Exception:
                        continue
            
            # Sort files by different criteria
            if files_info:
                analysis['largest_files'] = sorted(files_info, key=lambda x: x['size'], reverse=True)[:10]
                analysis['oldest_files'] = sorted(files_info, key=lambda x: x['modified'])[:10]
                analysis['newest_files'] = sorted(files_info, key=lambda x: x['modified'], reverse=True)[:10]
            
            analysis['total_size_human'] = self._format_size(analysis['total_size'])
            
            return analysis
            
        except Exception as e:
            return {'directory_analysis_error': str(e)}
    
    def _analyze_file_content(self, file_path: Path) -> Dict[str, Any]:
        """Analyze file content"""
        try:
            analysis = {}
            
            # Text file analysis
            if self._is_text_file(file_path):
                text_analysis = self._analyze_text_file(file_path)
                analysis.update(text_analysis)
            
            # Image file analysis
            elif self._is_image_file(file_path):
                image_analysis = self._analyze_image_file(file_path)
                analysis.update(image_analysis)
            
            # Archive file analysis
            elif self._is_archive_file(file_path):
                archive_analysis = self._analyze_archive_file(file_path)
                analysis.update(archive_analysis)
            
            return analysis
            
        except Exception as e:
            return {'content_analysis_error': str(e)}
    
    def _is_text_file(self, file_path: Path) -> bool:
        """Check if file is text file"""
        text_extensions = {'.txt', '.py', '.js', '.html', '.css', '.json', '.xml', '.csv', '.md', '.yml', '.yaml'}
        return file_path.suffix.lower() in text_extensions
    
    def _is_image_file(self, file_path: Path) -> bool:
        """Check if file is image file"""
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.webp', '.svg'}
        return file_path.suffix.lower() in image_extensions
    
    def _is_archive_file(self, file_path: Path) -> bool:
        """Check if file is archive file"""
        archive_extensions = {'.zip', '.rar', '.7z', '.tar', '.gz', '.bz2', '.xz'}
        return file_path.suffix.lower() in archive_extensions
    
    def _analyze_text_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze text file content"""
        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            lines = content.split('\n')
            words = content.split()
            
            return {
                'content_type': 'text',
                'line_count': len(lines),
                'word_count': len(words),
                'character_count': len(content),
                'character_count_no_spaces': len(content.replace(' ', '')),
                'encoding': 'utf-8',
                'is_empty': len(content.strip()) == 0
            }
            
        except Exception as e:
            return {'text_analysis_error': str(e)}
    
    def _analyze_image_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze image file"""
        try:
            # Basic image info (would need PIL/Pillow for detailed analysis)
            return {
                'content_type': 'image',
                'format': file_path.suffix.lower().replace('.', '').upper()
            }
            
        except Exception as e:
            return {'image_analysis_error': str(e)}
    
    def _analyze_archive_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze archive file"""
        try:
            analysis = {'content_type': 'archive'}
            
            if file_path.suffix.lower() == '.zip':
                with zipfile.ZipFile(file_path, 'r') as zf:
                    analysis['archive_type'] = 'zip'
                    analysis['file_count'] = len(zf.namelist())
                    analysis['files'] = zf.namelist()[:20]  # First 20 files
                    
            elif file_path.suffix.lower() in ['.tar', '.gz', '.bz2', '.xz']:
                with tarfile.open(file_path, 'r') as tf:
                    analysis['archive_type'] = 'tar'
                    analysis['file_count'] = len(tf.getnames())
                    analysis['files'] = tf.getnames()[:20]  # First 20 files
            
            return analysis
            
        except Exception as e:
            return {'archive_analysis_error': str(e)}
    
    def copy_file(self, source: str, destination: str, preserve_metadata: bool = True) -> Dict[str, Any]:
        """Copy file or directory"""
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                return {'success': False, 'error': 'Source path does not exist'}
            
            # Create destination directory if needed
            if dest_path.is_dir() or str(dest_path).endswith('/') or str(dest_path).endswith('\\'):
                dest_path = dest_path / source_path.name
            else:
                dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            if source_path.is_file():
                if preserve_metadata:
                    shutil.copy2(source_path, dest_path)
                else:
                    shutil.copy(source_path, dest_path)
            elif source_path.is_dir():
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
            
            return {
                'success': True,
                'source': str(source_path),
                'destination': str(dest_path),
                'message': f'Successfully copied {source_path.name}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def move_file(self, source: str, destination: str) -> Dict[str, Any]:
        """Move file or directory"""
        try:
            source_path = Path(source)
            dest_path = Path(destination)
            
            if not source_path.exists():
                return {'success': False, 'error': 'Source path does not exist'}
            
            # Create destination directory if needed
            if dest_path.is_dir() or str(dest_path).endswith('/') or str(dest_path).endswith('\\'):
                dest_path = dest_path / source_path.name
            else:
                dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(source_path), str(dest_path))
            
            return {
                'success': True,
                'source': str(source_path),
                'destination': str(dest_path),
                'message': f'Successfully moved {source_path.name}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def delete_file(self, file_path: str, use_recycle_bin: bool = True) -> Dict[str, Any]:
        """Delete file or directory"""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {'success': False, 'error': 'Path does not exist'}
            
            if use_recycle_bin:
                try:
                    send2trash.send2trash(str(path))
                    action = 'moved to recycle bin'
                except Exception:
                    # Fallback to permanent deletion
                    if path.is_file():
                        path.unlink()
                    elif path.is_dir():
                        shutil.rmtree(path)
                    action = 'permanently deleted'
            else:
                if path.is_file():
                    path.unlink()
                elif path.is_dir():
                    shutil.rmtree(path)
                action = 'permanently deleted'
            
            return {
                'success': True,
                'path': str(path),
                'action': action,
                'message': f'Successfully {action} {path.name}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_directory(self, dir_path: str, parents: bool = True) -> Dict[str, Any]:
        """Create directory"""
        try:
            path = Path(dir_path)
            path.mkdir(parents=parents, exist_ok=True)
            
            return {
                'success': True,
                'path': str(path.absolute()),
                'message': f'Successfully created directory {path.name}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def search_files(self, search_path: str, pattern: str = "*", 
                    include_content: bool = False, max_results: int = 100) -> Dict[str, Any]:
        """Search for files"""
        try:
            search_dir = Path(search_path)
            if not search_dir.exists():
                return {'success': False, 'error': 'Search path does not exist'}
            
            results = []
            count = 0
            
            for file_path in search_dir.rglob(pattern):
                if count >= max_results:
                    break
                
                try:
                    stat = file_path.stat()
                    result = {
                        'path': str(file_path.absolute()),
                        'name': file_path.name,
                        'size': stat.st_size,
                        'size_human': self._format_size(stat.st_size),
                        'modified': stat.st_mtime,
                        'is_directory': file_path.is_dir()
                    }
                    
                    # Content search for text files
                    if include_content and file_path.is_file() and self._is_text_file(file_path):
                        try:
                            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                                content = f.read()
                                if pattern.replace('*', '') in content:
                                    result['content_match'] = True
                        except Exception:
                            pass
                    
                    results.append(result)
                    count += 1
                    
                except Exception:
                    continue
            
            return {
                'success': True,
                'search_path': str(search_dir),
                'pattern': pattern,
                'results_count': len(results),
                'results': results,
                'truncated': count >= max_results
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def compare_files(self, file1: str, file2: str) -> Dict[str, Any]:
        """Compare two files"""
        try:
            path1 = Path(file1)
            path2 = Path(file2)
            
            if not path1.exists() or not path2.exists():
                return {'success': False, 'error': 'One or both files do not exist'}
            
            # Basic comparison
            are_identical = filecmp.cmp(path1, path2, shallow=False)
            
            # Size comparison
            size1 = path1.stat().st_size
            size2 = path2.stat().st_size
            
            result = {
                'success': True,
                'file1': str(path1),
                'file2': str(path2),
                'identical': are_identical,
                'size1': size1,
                'size2': size2,
                'size1_human': self._format_size(size1),
                'size2_human': self._format_size(size2),
                'size_difference': abs(size1 - size2)
            }
            
            # Hash comparison for small files
            if size1 < 10 * 1024 * 1024 and size2 < 10 * 1024 * 1024:  # Max 10MB
                hash1 = self._calculate_hashes(path1)
                hash2 = self._calculate_hashes(path2)
                result['hash1'] = hash1
                result['hash2'] = hash2
                result['hash_match'] = hash1.get('sha256') == hash2.get('sha256')
            
            return result
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_archive(self, source_path: str, archive_path: str, 
                      archive_type: str = 'zip') -> Dict[str, Any]:
        """Create archive from files/directories"""
        try:
            source = Path(source_path)
            archive = Path(archive_path)
            
            if not source.exists():
                return {'success': False, 'error': 'Source path does not exist'}
            
            # Create archive directory if needed
            archive.parent.mkdir(parents=True, exist_ok=True)
            
            if archive_type.lower() == 'zip':
                with zipfile.ZipFile(archive, 'w', zipfile.ZIP_DEFLATED) as zf:
                    if source.is_file():
                        zf.write(source, source.name)
                    elif source.is_dir():
                        for file_path in source.rglob('*'):
                            if file_path.is_file():
                                arcname = file_path.relative_to(source.parent)
                                zf.write(file_path, arcname)
            
            elif archive_type.lower() in ['tar', 'tar.gz', 'tgz']:
                mode = 'w:gz' if archive_type.lower() in ['tar.gz', 'tgz'] else 'w'
                with tarfile.open(archive, mode) as tf:
                    tf.add(source, arcname=source.name)
            
            else:
                return {'success': False, 'error': f'Unsupported archive type: {archive_type}'}
            
            archive_size = archive.stat().st_size
            
            return {
                'success': True,
                'source': str(source),
                'archive': str(archive),
                'archive_type': archive_type,
                'archive_size': archive_size,
                'archive_size_human': self._format_size(archive_size),
                'message': f'Successfully created {archive_type} archive'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def extract_archive(self, archive_path: str, extract_path: str) -> Dict[str, Any]:
        """Extract archive"""
        try:
            archive = Path(archive_path)
            extract_dir = Path(extract_path)
            
            if not archive.exists():
                return {'success': False, 'error': 'Archive does not exist'}
            
            # Create extraction directory
            extract_dir.mkdir(parents=True, exist_ok=True)
            
            extracted_files = []
            
            if archive.suffix.lower() == '.zip':
                with zipfile.ZipFile(archive, 'r') as zf:
                    zf.extractall(extract_dir)
                    extracted_files = zf.namelist()
            
            elif archive.suffix.lower() in ['.tar', '.gz', '.bz2', '.xz']:
                with tarfile.open(archive, 'r') as tf:
                    tf.extractall(extract_dir)
                    extracted_files = tf.getnames()
            
            else:
                return {'success': False, 'error': f'Unsupported archive type: {archive.suffix}'}
            
            return {
                'success': True,
                'archive': str(archive),
                'extract_path': str(extract_dir),
                'files_extracted': len(extracted_files),
                'extracted_files': extracted_files[:20],  # First 20 files
                'message': f'Successfully extracted {len(extracted_files)} files'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_file_management_tools(log_manager=None) -> FileManagementTools:
    """Factory function to create file management tools"""
    return FileManagementTools(log_manager)
