# 🚀 Cybex Enterprise - Launcher Files

## File di Avvio Disponibili

### 📁 File Batch Creati

1. **`start_cybex.bat`** - Avvio Semplice
2. **`cybex_launcher.bat`** - Launcher Avanzato con Menu
3. **`install_cybex.bat`** - Script di Installazione

---

## 🎯 Come Utilizzare

### 1. **start_cybex.bat** - Avvio Rapido
```batch
# Doppio click per avviare direttamente Cybex Enterprise
```

**Caratteristiche:**
- ✅ Avvio diretto di Cybex Enterprise
- ✅ Controllo automatico Python
- ✅ Verifica Ollama server
- ✅ Gestione errori integrata
- ✅ Banner ASCII professionale

**Quando usarlo:** Per avviare rapidamente Cybex senza opzioni aggiuntive

---

### 2. **cybex_launcher.bat** - Launcher Completo
```batch
# Doppio click per aprire il menu avanzato
```

**Menu Opzioni:**
- **[1]** 🖥️ Start Cybex Enterprise (Interfaccia Completa)
- **[2]** 🧪 Run System Tests (Test di Sistema)
- **[3]** 🔧 Check System Requirements (Verifica Requisiti)
- **[4]** 📋 View Available Ollama Models (Modelli Disponibili)
- **[5]** 🎨 Demo Enterprise Features (Demo Funzionalità)
- **[6]** 📖 View Documentation (Documentazione)
- **[7]** ⚙️ Install/Update Dependencies (Installa Dipendenze)
- **[8]** 🔄 Update Cybex (Aggiornamento)
- **[Q]** ❌ Quit (Esci)

**Quando usarlo:** Per accesso completo a tutte le funzionalità e strumenti

---

### 3. **install_cybex.bat** - Installazione Automatica
```batch
# Esegui SOLO la prima volta per configurare Cybex
```

**Processo di Installazione:**
1. ✅ Verifica Python installato
2. ✅ Controlla pip disponibile
3. ✅ Installa dipendenze Python
4. ✅ Verifica Ollama server
5. ✅ Crea shortcut desktop (opzionale)

**Quando usarlo:** Solo alla prima installazione o per reinstallare dipendenze

---

## 🎨 Caratteristiche dei Launcher

### Design Professionale
- 🎨 **Banner ASCII Cybex** con logo completo
- 🌅 **Colori Sunrise** (palette aziendale)
- 📋 **Menu strutturati** con icone emoji
- ✅ **Feedback visivo** per ogni operazione

### Controlli Automatici
- 🐍 **Python Detection** - Verifica installazione Python
- 📦 **Dependency Check** - Controlla pacchetti richiesti
- 🤖 **Ollama Status** - Verifica server AI
- 💾 **File Integrity** - Controlla file Cybex

### Gestione Errori
- ❌ **Error Handling** - Gestione errori completa
- 💡 **Troubleshooting** - Suggerimenti per risolvere problemi
- 🔄 **Recovery Options** - Opzioni di recupero
- 📝 **Clear Messages** - Messaggi di errore chiari

---

## 🚀 Guida Rapida

### Prima Installazione
1. **Esegui:** `install_cybex.bat`
2. **Segui** il wizard di installazione
3. **Avvia:** `cybex_launcher.bat`

### Uso Quotidiano
- **Avvio Rapido:** `start_cybex.bat`
- **Menu Completo:** `cybex_launcher.bat`

### Risoluzione Problemi
1. Apri `cybex_launcher.bat`
2. Seleziona opzione **[3]** Check System Requirements
3. Seleziona opzione **[7]** Install/Update Dependencies

---

## 🔧 Requisiti di Sistema

### Requisiti Minimi
- **Windows 10/11** (o Windows Server 2016+)
- **Python 3.8+** installato e in PATH
- **4 GB RAM** disponibili
- **2 GB spazio disco** libero

### Requisiti Consigliati
- **Windows 11** ultima versione
- **Python 3.11+** 
- **8 GB RAM** o superiore
- **Ollama** installato con modelli AI
- **SSD** per performance ottimali

---

## 🤖 Configurazione Ollama

### Installazione Ollama
1. Visita: https://ollama.ai
2. Scarica e installa Ollama
3. Apri terminale e esegui:
   ```bash
   ollama pull gemma2:7b
   ollama pull llama3.1:8b
   ```
4. Avvia il servizio Ollama

### Verifica Modelli
- Usa `cybex_launcher.bat` → opzione **[4]**
- Oppure esegui: `ollama list`

---

## 📊 Funzionalità Enterprise

### Quick Actions (1-9)
- **[1]** 🖥️ System Status - Panoramica sistema
- **[2]** 🧠 Memory Status - Analisi RAM
- **[3]** 💾 Disk Status - Stato dischi
- **[4]** ⚡ CPU Status - Performance CPU
- **[5]** 🌐 Network Status - Stato rete
- **[6]** 🔄 Active Processes - Processi attivi
- **[7]** 🧹 System Cleanup - Pulizia sistema
- **[8]** ⚡ Optimize System - Ottimizzazione
- **[9]** 📊 Performance Analysis - Analisi performance

### Linguaggio Naturale
Esempi di comandi in italiano:
- "dammi la situazione memoria del computer"
- "come sta il disco?"
- "stato del sistema"
- "il sistema è lento"
- "pulisci i file temporanei"
- "ottimizza il sistema"

### Configurazione AI
- **Rilevamento Automatico** di tutti i modelli Ollama
- **Switch Dinamico** tra modelli
- **Refresh Real-time** della lista modelli
- **Informazioni Dettagliate** per ogni modello

---

## 🎯 Supporto e Troubleshooting

### Problemi Comuni

**Python non trovato:**
```
❌ ERROR: Python is not installed or not in PATH
```
**Soluzione:** Installa Python da python.org e aggiungi al PATH

**Ollama non disponibile:**
```
⚠️ WARNING: Ollama server not detected
```
**Soluzione:** Installa Ollama da ollama.ai e avvia il servizio

**Dipendenze mancanti:**
```
❌ Some required packages are missing
```
**Soluzione:** Usa `cybex_launcher.bat` → opzione **[7]**

### Contatti
- **Sviluppatore:** AGTECHdesigne
- **Progetto:** Cybex Enterprise
- **Versione:** Enterprise Edition

---

## 🌟 Caratteristiche Uniche

- 🎨 **Sunrise Color Palette** (75bde0, f8d49b, f8bc9b, fb9b9b)
- 🗣️ **Natural Language Processing** in italiano
- 🚀 **9 Quick Actions** preconfigurate
- 🤖 **Dynamic AI Model Detection** 
- 🔄 **Chat/Agent Mode Switching**
- 📊 **Real-time System Monitoring**
- 🎯 **Enterprise-grade Interface**

**Cybex Enterprise by AGTECHdesigne** - Il futuro dell'amministrazione sistema! 🚀
