"""
System Snapshot Module
Creates system state snapshots and provides rollback capabilities
"""

import os
import json
import time
import platform
import tempfile
import shutil
import hashlib
from typing import Dict, List, Optional, Any, Set, Tuple
from datetime import datetime
from pathlib import Path


class SystemSnapshot:
    """
    Creates and manages system state snapshots for rollback operations
    """
    
    def __init__(self, config_manager, log_manager, command_executor):
        """Initialize system snapshot manager"""
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.command_executor = command_executor
        self.logger = log_manager.get_logger(__name__)
        
        # Snapshot configuration
        self.snapshot_config = config_manager.get_section('snapshots')
        self.snapshot_dir = Path(self.snapshot_config.get('directory', 'snapshots'))
        self.max_snapshots = self.snapshot_config.get('max_snapshots', 10)
        self.include_files = self.snapshot_config.get('include_files', True)
        self.include_registry = self.snapshot_config.get('include_registry', True)
        self.include_services = self.snapshot_config.get('include_services', True)
        
        # System type
        self.system_type = platform.system().lower()
        self.is_windows = self.system_type == 'windows'
        
        # Ensure snapshot directory exists
        self.snapshot_dir.mkdir(parents=True, exist_ok=True)
        
        # Active snapshots
        self.active_snapshots: Dict[str, Dict] = {}
    
    def create_snapshot(self, name: str, description: str = "", 
                       include_paths: Optional[List[str]] = None) -> Optional[str]:
        """
        Create a system state snapshot
        Returns snapshot ID if successful, None otherwise
        """
        self.logger.info(f"Creating system snapshot: {name}")
        
        try:
            # Generate snapshot ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            snapshot_id = f"{timestamp}_{name.replace(' ', '_')}"
            
            # Create snapshot directory
            snapshot_path = self.snapshot_dir / snapshot_id
            snapshot_path.mkdir(parents=True, exist_ok=True)
            
            # Initialize snapshot metadata
            metadata = {
                'id': snapshot_id,
                'name': name,
                'description': description,
                'created_at': datetime.now().isoformat(),
                'system_type': self.system_type,
                'components': {}
            }
            
            # Collect system state
            if self.include_files:
                metadata['components']['files'] = self._snapshot_files(
                    snapshot_path, include_paths
                )
            
            if self.include_registry and self.is_windows:
                metadata['components']['registry'] = self._snapshot_registry(snapshot_path)
            
            if self.include_services:
                metadata['components']['services'] = self._snapshot_services(snapshot_path)
            
            # Save metadata
            with open(snapshot_path / 'metadata.json', 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Register active snapshot
            self.active_snapshots[snapshot_id] = {
                'path': str(snapshot_path),
                'metadata': metadata
            }
            
            # Cleanup old snapshots
            self._cleanup_old_snapshots()
            
            self.logger.info(f"Snapshot created successfully: {snapshot_id}")
            return snapshot_id
            
        except Exception as e:
            self.logger.error(f"Failed to create snapshot: {e}")
            return None
    
    def _snapshot_files(self, snapshot_path: Path, 
                       include_paths: Optional[List[str]] = None) -> Dict[str, Any]:
        """Create snapshot of important files"""
        files_path = snapshot_path / 'files'
        files_path.mkdir(exist_ok=True)
        
        # Default paths to snapshot
        default_paths = []
        if self.is_windows:
            default_paths = [
                r'C:\Windows\System32\drivers\etc\hosts',
                r'C:\Windows\System32\config\systemprofile',
                r'C:\ProgramData\Microsoft\Windows\Start Menu\Programs\StartUp'
            ]
        else:
            default_paths = [
                '/etc/hosts',
                '/etc/fstab',
                '/etc/crontab',
                '/etc/passwd',
                '/etc/group'
            ]
        
        # Combine with user-specified paths
        paths_to_snapshot = default_paths + (include_paths or [])
        
        # Create file manifest
        file_manifest = {
            'snapshot_time': datetime.now().isoformat(),
            'files': {}
        }
        
        # Process each path
        for path_str in paths_to_snapshot:
            path = Path(path_str)
            if not path.exists():
                continue
            
            try:
                if path.is_file():
                    file_info = self._snapshot_single_file(path, files_path)
                    if file_info:
                        file_manifest['files'][str(path)] = file_info
                elif path.is_dir():
                    dir_files = self._snapshot_directory(path, files_path)
                    file_manifest['files'].update(dir_files)
            except Exception as e:
                self.logger.warning(f"Failed to snapshot {path}: {e}")
        
        # Save manifest
        with open(files_path / 'manifest.json', 'w') as f:
            json.dump(file_manifest, f, indent=2)
        
        return {
            'count': len(file_manifest['files']),
            'size': sum(f.get('size', 0) for f in file_manifest['files'].values()),
            'manifest_path': str(files_path / 'manifest.json')
        }
    
    def _snapshot_single_file(self, file_path: Path, snapshot_dir: Path) -> Optional[Dict]:
        """Snapshot a single file"""
        if not file_path.exists() or not file_path.is_file():
            return None
        
        try:
            # Get file stats
            stat = file_path.stat()
            
            # Calculate file hash
            file_hash = self._calculate_file_hash(file_path)
            
            # Create relative path for storage
            rel_path = file_path.name
            if len(file_path.parts) > 1:
                rel_path = os.path.join(*file_path.parts[-2:])
            
            # Create backup copy
            backup_path = snapshot_dir / rel_path
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.copy2(file_path, backup_path)
            
            return {
                'path': str(file_path),
                'backup_path': str(backup_path),
                'size': stat.st_size,
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'hash': file_hash,
                'permissions': oct(stat.st_mode)[-3:]
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to snapshot file {file_path}: {e}")
            return None
    
    def _snapshot_directory(self, dir_path: Path, snapshot_dir: Path, 
                          max_files: int = 100) -> Dict[str, Dict]:
        """Snapshot important files in a directory"""
        if not dir_path.exists() or not dir_path.is_dir():
            return {}
        
        result = {}
        count = 0
        
        try:
            # Create directory structure
            for root, dirs, files in os.walk(dir_path):
                if count >= max_files:
                    break
                
                root_path = Path(root)
                
                # Skip hidden directories
                dirs[:] = [d for d in dirs if not d.startswith('.')]
                
                # Process files
                for file in files:
                    if count >= max_files:
                        break
                    
                    # Skip hidden and temporary files
                    if file.startswith('.') or file.endswith('.tmp'):
                        continue
                    
                    file_path = root_path / file
                    file_info = self._snapshot_single_file(file_path, snapshot_dir)
                    
                    if file_info:
                        result[str(file_path)] = file_info
                        count += 1
        
        except Exception as e:
            self.logger.warning(f"Failed to snapshot directory {dir_path}: {e}")
        
        return result
    
    def _snapshot_registry(self, snapshot_path: Path) -> Dict[str, Any]:
        """Create snapshot of important registry keys (Windows only)"""
        if not self.is_windows:
            return {}
        
        registry_path = snapshot_path / 'registry'
        registry_path.mkdir(exist_ok=True)
        
        # Important registry keys to backup
        registry_keys = [
            r'HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\Run',
            r'HKLM\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce',
            r'HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\Run',
            r'HKCU\SOFTWARE\Microsoft\Windows\CurrentVersion\RunOnce',
            r'HKLM\SYSTEM\CurrentControlSet\Services',
            r'HKLM\SOFTWARE\Policies\Microsoft\Windows\System'
        ]
        
        registry_manifest = {
            'snapshot_time': datetime.now().isoformat(),
            'keys': {}
        }
        
        for key in registry_keys:
            try:
                # Export registry key
                key_file = f"{key.replace('\\', '_').replace(':', '')}.reg"
                export_path = registry_path / key_file
                
                cmd = f'reg export "{key}" "{export_path}" /y'
                success, output, _ = self.command_executor.execute_command(
                    cmd, confirm_required=False
                )
                
                if success:
                    registry_manifest['keys'][key] = {
                        'export_path': str(export_path),
                        'size': export_path.stat().st_size if export_path.exists() else 0
                    }
                else:
                    self.logger.warning(f"Failed to export registry key {key}: {output}")
                    
            except Exception as e:
                self.logger.warning(f"Failed to snapshot registry key {key}: {e}")
        
        # Save manifest
        with open(registry_path / 'manifest.json', 'w') as f:
            json.dump(registry_manifest, f, indent=2)
        
        return {
            'count': len(registry_manifest['keys']),
            'manifest_path': str(registry_path / 'manifest.json')
        }
    
    def _snapshot_services(self, snapshot_path: Path) -> Dict[str, Any]:
        """Create snapshot of system services"""
        services_path = snapshot_path / 'services'
        services_path.mkdir(exist_ok=True)
        
        services_manifest = {
            'snapshot_time': datetime.now().isoformat(),
            'services': {}
        }
        
        try:
            if self.is_windows:
                # Windows services
                cmd = 'sc query state= all'
                success, output, _ = self.command_executor.execute_command(
                    cmd, confirm_required=False
                )
                
                if success:
                    # Save raw output
                    with open(services_path / 'services_raw.txt', 'w') as f:
                        f.write(output)
                    
                    # Parse service states
                    current_service = None
                    for line in output.splitlines():
                        line = line.strip()
                        if line.startswith('SERVICE_NAME:'):
                            current_service = line.split(':', 1)[1].strip()
                            services_manifest['services'][current_service] = {}
                        elif current_service and ':' in line:
                            key, value = line.split(':', 1)
                            services_manifest['services'][current_service][key.strip()] = value.strip()
            else:
                # Linux services
                cmd = 'systemctl list-units --type=service --all'
                success, output, _ = self.command_executor.execute_command(
                    cmd, confirm_required=False
                )
                
                if success:
                    # Save raw output
                    with open(services_path / 'services_raw.txt', 'w') as f:
                        f.write(output)
                    
                    # Parse service states
                    for line in output.splitlines()[1:]:  # Skip header
                        parts = line.split()
                        if len(parts) >= 5 and parts[0].endswith('.service'):
                            service_name = parts[0].replace('.service', '')
                            services_manifest['services'][service_name] = {
                                'load': parts[1],
                                'active': parts[2],
                                'sub': parts[3],
                                'description': ' '.join(parts[4:])
                            }
        except Exception as e:
            self.logger.warning(f"Failed to snapshot services: {e}")
        
        # Save manifest
        with open(services_path / 'manifest.json', 'w') as f:
            json.dump(services_manifest, f, indent=2)
        
        return {
            'count': len(services_manifest['services']),
            'manifest_path': str(services_path / 'manifest.json')
        }
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of a file"""
        try:
            hasher = hashlib.sha256()
            with open(file_path, 'rb') as f:
                # Read in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b''):
                    hasher.update(chunk)
            return hasher.hexdigest()
        except Exception:
            return ""
    
    def _cleanup_old_snapshots(self) -> None:
        """Clean up old snapshots exceeding the maximum limit"""
        try:
            snapshots = list(self.snapshot_dir.glob('*'))
            snapshots = [s for s in snapshots if s.is_dir()]
            
            # Sort by creation time (oldest first)
            snapshots.sort(key=lambda p: p.stat().st_ctime)
            
            # Remove oldest snapshots if exceeding limit
            while len(snapshots) > self.max_snapshots:
                oldest = snapshots.pop(0)
                self.logger.info(f"Removing old snapshot: {oldest.name}")
                shutil.rmtree(oldest, ignore_errors=True)
                
                # Remove from active snapshots
                if oldest.name in self.active_snapshots:
                    del self.active_snapshots[oldest.name]
                    
        except Exception as e:
            self.logger.error(f"Failed to cleanup old snapshots: {e}")
    
    def restore_snapshot(self, snapshot_id: str, 
                        components: Optional[List[str]] = None) -> bool:
        """
        Restore system from a snapshot
        """
        self.logger.info(f"Restoring from snapshot: {snapshot_id}")
        
        try:
            # Find snapshot
            snapshot_path = self.snapshot_dir / snapshot_id
            if not snapshot_path.exists():
                self.logger.error(f"Snapshot not found: {snapshot_id}")
                return False
            
            # Load metadata
            metadata_path = snapshot_path / 'metadata.json'
            if not metadata_path.exists():
                self.logger.error(f"Snapshot metadata not found: {snapshot_id}")
                return False
            
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)
            
            # Determine components to restore
            available_components = set(metadata.get('components', {}).keys())
            if not components:
                components_to_restore = available_components
            else:
                components_to_restore = set(components) & available_components
            
            # Restore each component
            success = True
            
            if 'files' in components_to_restore:
                files_success = self._restore_files(snapshot_path)
                success = success and files_success
            
            if 'registry' in components_to_restore and self.is_windows:
                registry_success = self._restore_registry(snapshot_path)
                success = success and registry_success
            
            if 'services' in components_to_restore:
                services_success = self._restore_services(snapshot_path)
                success = success and services_success
            
            if success:
                self.logger.info(f"Snapshot {snapshot_id} restored successfully")
            else:
                self.logger.warning(f"Snapshot {snapshot_id} restored with some errors")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to restore snapshot: {e}")
            return False
    
    def _restore_files(self, snapshot_path: Path) -> bool:
        """Restore files from snapshot"""
        files_path = snapshot_path / 'files'
        manifest_path = files_path / 'manifest.json'
        
        if not manifest_path.exists():
            self.logger.error("File manifest not found in snapshot")
            return False
        
        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            success_count = 0
            error_count = 0
            
            for file_path, file_info in manifest.get('files', {}).items():
                try:
                    backup_path = Path(file_info.get('backup_path', ''))
                    if not backup_path.exists():
                        self.logger.warning(f"Backup file not found: {backup_path}")
                        error_count += 1
                        continue
                    
                    target_path = Path(file_path)
                    target_path.parent.mkdir(parents=True, exist_ok=True)
                    
                    # Copy file back
                    shutil.copy2(backup_path, target_path)
                    
                    # Restore permissions if possible
                    if not self.is_windows and 'permissions' in file_info:
                        try:
                            os.chmod(target_path, int(file_info['permissions'], 8))
                        except:
                            pass
                    
                    success_count += 1
                    
                except Exception as e:
                    self.logger.warning(f"Failed to restore file {file_path}: {e}")
                    error_count += 1
            
            self.logger.info(f"Restored {success_count} files, {error_count} errors")
            return error_count == 0
            
        except Exception as e:
            self.logger.error(f"Failed to restore files: {e}")
            return False
    
    def _restore_registry(self, snapshot_path: Path) -> bool:
        """Restore registry keys from snapshot"""
        if not self.is_windows:
            return False
        
        registry_path = snapshot_path / 'registry'
        manifest_path = registry_path / 'manifest.json'
        
        if not manifest_path.exists():
            self.logger.error("Registry manifest not found in snapshot")
            return False
        
        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            success_count = 0
            error_count = 0
            
            for key, key_info in manifest.get('keys', {}).items():
                try:
                    export_path = Path(key_info.get('export_path', ''))
                    if not export_path.exists():
                        self.logger.warning(f"Registry export not found: {export_path}")
                        error_count += 1
                        continue
                    
                    # Import registry key
                    cmd = f'reg import "{export_path}"'
                    success, output, _ = self.command_executor.execute_command(
                        cmd, confirm_required=False
                    )
                    
                    if success:
                        success_count += 1
                    else:
                        self.logger.warning(f"Failed to import registry key {key}: {output}")
                        error_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"Failed to restore registry key {key}: {e}")
                    error_count += 1
            
            self.logger.info(f"Restored {success_count} registry keys, {error_count} errors")
            return error_count == 0
            
        except Exception as e:
            self.logger.error(f"Failed to restore registry: {e}")
            return False
    
    def _restore_services(self, snapshot_path: Path) -> bool:
        """Restore service states from snapshot"""
        services_path = snapshot_path / 'services'
        manifest_path = services_path / 'manifest.json'
        
        if not manifest_path.exists():
            self.logger.error("Services manifest not found in snapshot")
            return False
        
        try:
            with open(manifest_path, 'r') as f:
                manifest = json.load(f)
            
            success_count = 0
            error_count = 0
            
            for service_name, service_info in manifest.get('services', {}).items():
                try:
                    if self.is_windows:
                        # Windows services
                        if service_info.get('STATE') == 'RUNNING':
                            cmd = f'sc start "{service_name}"'
                        elif service_info.get('STATE') == 'STOPPED':
                            cmd = f'sc stop "{service_name}"'
                        else:
                            continue
                    else:
                        # Linux services
                        if service_info.get('active') == 'active':
                            cmd = f'systemctl start {service_name}.service'
                        elif service_info.get('active') == 'inactive':
                            cmd = f'systemctl stop {service_name}.service'
                        else:
                            continue
                    
                    success, output, _ = self.command_executor.execute_command(
                        cmd, confirm_required=False
                    )
                    
                    if success:
                        success_count += 1
                    else:
                        self.logger.warning(f"Failed to restore service {service_name}: {output}")
                        error_count += 1
                        
                except Exception as e:
                    self.logger.warning(f"Failed to restore service {service_name}: {e}")
                    error_count += 1
            
            self.logger.info(f"Restored {success_count} services, {error_count} errors")
            return error_count == 0
            
        except Exception as e:
            self.logger.error(f"Failed to restore services: {e}")
            return False
    
    def get_snapshots(self) -> List[Dict]:
        """Get list of available snapshots"""
        snapshots = []
        
        try:
            for snapshot_dir in self.snapshot_dir.glob('*'):
                if not snapshot_dir.is_dir():
                    continue
                
                metadata_path = snapshot_dir / 'metadata.json'
                if not metadata_path.exists():
                    continue
                
                try:
                    with open(metadata_path, 'r') as f:
                        metadata = json.load(f)
                    
                    snapshots.append({
                        'id': metadata.get('id', snapshot_dir.name),
                        'name': metadata.get('name', ''),
                        'description': metadata.get('description', ''),
                        'created_at': metadata.get('created_at', ''),
                        'components': list(metadata.get('components', {}).keys())
                    })
                except:
                    pass
        except Exception as e:
            self.logger.error(f"Failed to list snapshots: {e}")
        
        # Sort by creation time (newest first)
        snapshots.sort(key=lambda s: s.get('created_at', ''), reverse=True)
        
        return snapshots
    
    def delete_snapshot(self, snapshot_id: str) -> bool:
        """Delete a snapshot"""
        try:
            snapshot_path = self.snapshot_dir / snapshot_id
            if not snapshot_path.exists():
                self.logger.error(f"Snapshot not found: {snapshot_id}")
                return False
            
            shutil.rmtree(snapshot_path, ignore_errors=True)
            
            if snapshot_id in self.active_snapshots:
                del self.active_snapshots[snapshot_id]
            
            self.logger.info(f"Snapshot {snapshot_id} deleted")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete snapshot: {e}")
            return False
