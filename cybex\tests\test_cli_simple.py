#!/usr/bin/env python3
"""
Simple CLI Test
Test semplice dell'interfaccia CLI senza dipendenze esterne
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_cli_initialization():
    """Test CLI initialization"""
    print("🚀 Testing CLI Initialization...")
    
    try:
        from cybex.interfaces.ui_cli import CybexCLI
        
        # Initialize CLI
        cli = CybexCLI()
        print("✅ CLI initialized successfully")
        
        # Check core components
        if cli.core:
            print("✅ Core component available")
            print(f"   Mode: {cli.core.mode.value}")
            print(f"   System: {cli.core.system_type}")
        
        if cli.command_executor:
            print("✅ Command executor available")
        
        if cli.ollama_interface:
            print("✅ Ollama interface available")
        
        if cli.nl_processor:
            print("✅ Natural language processor available")
        else:
            print("⚠️  Natural language processor not available")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_builtin_commands():
    """Test built-in commands"""
    print("\n🔧 Testing Built-in Commands...")
    
    try:
        from cybex.interfaces.ui_cli import CybexCLI
        
        cli = CybexCLI()
        
        # Test help command
        print("Testing 'help' command...")
        if cli._handle_builtin_commands("help"):
            print("✅ Help command works")
        
        # Test status command
        print("Testing 'status' command...")
        if cli._handle_builtin_commands("status"):
            print("✅ Status command works")
        
        # Test config command
        print("Testing 'config' command...")
        if cli._handle_builtin_commands("config"):
            print("✅ Config command works")
        
        # Test SVP commands
        print("Testing 'monitor' command...")
        if cli._handle_builtin_commands("monitor"):
            print("✅ Monitor command works")
        
        return True
        
    except Exception as e:
        print(f"❌ Built-in commands test failed: {e}")
        return False


def test_natural_language_integration():
    """Test natural language integration"""
    print("\n🗣️  Testing Natural Language Integration...")
    
    try:
        from cybex.interfaces.ui_cli import CybexCLI
        
        cli = CybexCLI()
        
        if cli.nl_processor:
            print("✅ Natural language processor integrated")
            
            # Test a simple request
            test_request = "dammi la situazione memoria del computer"
            print(f"Testing request: '{test_request}'")
            
            # Simulate processing (without actually calling AI)
            context = {
                'nl_processor': cli.nl_processor,
                'mode': 'chat'
            }
            
            print("✅ Context prepared for NL processing")
            return True
        else:
            print("⚠️  Natural language processor not available")
            return False
        
    except Exception as e:
        print(f"❌ Natural language integration test failed: {e}")
        return False


def simulate_cli_session():
    """Simulate a CLI session"""
    print("\n🎭 Simulating CLI Session...")
    
    try:
        from cybex.interfaces.ui_cli import CybexCLI
        
        cli = CybexCLI()
        
        # Start session
        cli.core.start_session("test_user")
        print("✅ Session started")
        
        # Simulate some commands
        test_commands = [
            "help",
            "status", 
            "monitor",
            "disk"
        ]
        
        for cmd in test_commands:
            print(f"Simulating command: '{cmd}'")
            try:
                result = cli._handle_builtin_commands(cmd)
                print(f"   Result: {'✅ Success' if result else '⚠️  Not handled'}")
            except Exception as e:
                print(f"   Result: ❌ Error - {e}")
        
        # End session
        cli.core.end_session()
        print("✅ Session ended")
        
        return True
        
    except Exception as e:
        print(f"❌ CLI session simulation failed: {e}")
        return False


def main():
    """Run CLI tests"""
    print("🎯 Cybex CLI Test Suite")
    print("=" * 40)
    
    tests = [
        ("CLI Initialization", test_cli_initialization),
        ("Built-in Commands", test_builtin_commands),
        ("Natural Language Integration", test_natural_language_integration),
        ("CLI Session Simulation", simulate_cli_session)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 CLI is ready for interactive use!")
        print("\n🚀 To start Cybex CLI:")
        print("   python main.py")
        print("\n💬 Try these natural language requests:")
        print("   • dammi la situazione memoria del computer")
        print("   • come sta il disco?")
        print("   • stato del sistema")
        print("   • ottimizza il sistema")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
