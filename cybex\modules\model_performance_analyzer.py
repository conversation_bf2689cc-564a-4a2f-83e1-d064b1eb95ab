#!/usr/bin/env python3
"""
Model Performance Analyzer
Dynamic timeout and performance analysis for Ollama models
"""

import json
import time
import statistics
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta

@dataclass
class ModelPerformance:
    """Model performance metrics"""
    model_name: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    average_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    recent_response_times: List[float] = None
    last_updated: str = ""
    timeout_seconds: int = 120
    model_size_gb: float = 0.0
    complexity_factor: float = 1.0
    
    def __post_init__(self):
        if self.recent_response_times is None:
            self.recent_response_times = []

@dataclass
class RequestMetrics:
    """Individual request metrics"""
    model_name: str
    prompt_length: int
    response_time: float
    success: bool
    timestamp: str
    error_message: Optional[str] = None

class ModelPerformanceAnalyzer:
    """Analyze and optimize model performance with dynamic timeouts"""
    
    def __init__(self, log_manager, data_file: str = "model_performance.json"):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        self.data_file = Path(data_file)
        self.models: Dict[str, ModelPerformance] = {}
        self.max_recent_samples = 20  # Keep last 20 response times
        
        # Load existing data
        self.load_performance_data()
        
        # Default timeout configurations
        self.base_timeout = 320  # Base timeout in seconds (5+ minutes)
        self.max_timeout = 600   # Maximum timeout (10 minutes)
        self.min_timeout = 120   # Minimum timeout (2 minutes)
        
        self.logger.info("Model Performance Analyzer initialized")
    
    def load_performance_data(self):
        """Load performance data from file"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for model_name, model_data in data.items():
                    self.models[model_name] = ModelPerformance(**model_data)
                
                self.logger.info(f"Loaded performance data for {len(self.models)} models")
            else:
                self.logger.info("No existing performance data found, starting fresh")
                
        except Exception as e:
            self.logger.error(f"Error loading performance data: {e}")
            self.models = {}
    
    def save_performance_data(self):
        """Save performance data to file"""
        try:
            # Convert to serializable format
            data = {}
            for model_name, model_perf in self.models.items():
                data[model_name] = asdict(model_perf)
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
            self.logger.debug("Performance data saved")
            
        except Exception as e:
            self.logger.error(f"Error saving performance data: {e}")
    
    def analyze_model_from_ollama(self, model_info: Dict[str, Any]) -> ModelPerformance:
        """Analyze model from Ollama API info"""
        model_name = model_info.get('name', 'unknown')
        
        if model_name not in self.models:
            # Create new model performance entry
            model_perf = ModelPerformance(model_name=model_name)
            
            # Extract model size
            size_bytes = model_info.get('size', 0)
            model_perf.model_size_gb = size_bytes / (1024**3) if size_bytes > 0 else 0.0
            
            # Calculate complexity factor based on model name and size
            model_perf.complexity_factor = self._calculate_complexity_factor(model_name, model_perf.model_size_gb)
            
            # Calculate initial timeout
            model_perf.timeout_seconds = self._calculate_initial_timeout(model_perf)
            
            self.models[model_name] = model_perf
            self.logger.info(f"New model analyzed: {model_name} ({model_perf.model_size_gb:.1f}GB, timeout: {model_perf.timeout_seconds}s)")
        
        return self.models[model_name]
    
    def _calculate_complexity_factor(self, model_name: str, size_gb: float) -> float:
        """Calculate complexity factor based on model characteristics"""
        factor = 1.0
        
        # Size-based factor
        if size_gb > 10:
            factor += 0.5  # Large models are slower
        elif size_gb > 5:
            factor += 0.3
        elif size_gb > 2:
            factor += 0.1
        
        # Model type factor
        model_lower = model_name.lower()
        if 'llama' in model_lower:
            if '70b' in model_lower or '65b' in model_lower:
                factor += 0.8  # Very large models
            elif '13b' in model_lower or '30b' in model_lower:
                factor += 0.4  # Medium models
        elif 'gemma' in model_lower:
            if '27b' in model_lower:
                factor += 0.6
            elif '9b' in model_lower:
                factor += 0.3
        elif 'mistral' in model_lower:
            if '22b' in model_lower:
                factor += 0.5
            elif '8x7b' in model_lower:
                factor += 0.7  # MoE models can be slower
        elif 'qwen' in model_lower:
            if '72b' in model_lower:
                factor += 0.8
            elif '32b' in model_lower:
                factor += 0.5
        
        return min(factor, 3.0)  # Cap at 3x
    
    def _calculate_initial_timeout(self, model_perf: ModelPerformance) -> int:
        """Calculate initial timeout for a model - conservative approach"""
        # Start with base timeout for all models
        timeout = self.base_timeout

        # Add extra time for larger models (conservative)
        if model_perf.model_size_gb > 20:
            timeout += 120  # Add 2 minutes for very large models (20GB+)
        elif model_perf.model_size_gb > 10:
            timeout += 60   # Add 1 minute for large models (10-20GB)
        elif model_perf.model_size_gb > 5:
            timeout += 30   # Add 30 seconds for medium models (5-10GB)

        # Apply complexity factor (but keep it conservative)
        if model_perf.complexity_factor > 2.0:
            timeout += 60  # Add 1 minute for very complex models
        elif model_perf.complexity_factor > 1.5:
            timeout += 30  # Add 30 seconds for complex models

        # Ensure within bounds
        return max(self.min_timeout, min(timeout, self.max_timeout))
    
    def record_request(self, model_name: str, prompt_length: int, response_time: float, 
                      success: bool, error_message: Optional[str] = None):
        """Record a request and update model performance"""
        try:
            # Ensure model exists
            if model_name not in self.models:
                self.models[model_name] = ModelPerformance(model_name=model_name)
            
            model_perf = self.models[model_name]
            
            # Update counters
            model_perf.total_requests += 1
            if success:
                model_perf.successful_requests += 1
            else:
                model_perf.failed_requests += 1
            
            # Update response times (only for successful requests)
            if success and response_time > 0:
                # Add to recent times
                model_perf.recent_response_times.append(response_time)
                
                # Keep only recent samples
                if len(model_perf.recent_response_times) > self.max_recent_samples:
                    model_perf.recent_response_times = model_perf.recent_response_times[-self.max_recent_samples:]
                
                # Update statistics
                model_perf.min_response_time = min(model_perf.min_response_time, response_time)
                model_perf.max_response_time = max(model_perf.max_response_time, response_time)
                model_perf.average_response_time = statistics.mean(model_perf.recent_response_times)
                
                # Update dynamic timeout
                self._update_dynamic_timeout(model_perf)
            
            # Update timestamp
            model_perf.last_updated = datetime.now().isoformat()
            
            # Save data periodically
            if model_perf.total_requests % 5 == 0:  # Save every 5 requests
                self.save_performance_data()
            
            self.logger.debug(f"Recorded request for {model_name}: {response_time:.2f}s, success: {success}")
            
        except Exception as e:
            self.logger.error(f"Error recording request: {e}")
    
    def _update_dynamic_timeout(self, model_perf: ModelPerformance):
        """Update dynamic timeout based on recent performance - gradual adaptation"""
        if len(model_perf.recent_response_times) < 5:
            return  # Need at least 5 samples for reliable statistics

        try:
            # Calculate statistics
            mean_time = statistics.mean(model_perf.recent_response_times)
            std_dev = statistics.stdev(model_perf.recent_response_times) if len(model_perf.recent_response_times) > 1 else 0
            max_time = max(model_perf.recent_response_times)

            # Conservative timeout calculation
            # Use the higher of: mean + 3*std_dev or max_time + buffer
            buffer = 60  # 60 second buffer (more conservative)
            statistical_timeout = mean_time + (3 * std_dev) + buffer
            max_based_timeout = max_time + buffer

            new_timeout = int(max(statistical_timeout, max_based_timeout))

            # Apply bounds
            new_timeout = max(self.min_timeout, min(new_timeout, self.max_timeout))

            # Gradual adaptation - only change by max 30 seconds at a time
            current_timeout = model_perf.timeout_seconds
            if new_timeout > current_timeout:
                # Increase timeout gradually
                new_timeout = min(new_timeout, current_timeout + 30)
            elif new_timeout < current_timeout:
                # Decrease timeout very gradually (only if consistently fast)
                if mean_time < (current_timeout * 0.3):  # Only if avg response is < 30% of timeout
                    new_timeout = max(new_timeout, current_timeout - 15)
                else:
                    new_timeout = current_timeout  # Don't decrease

            # Only update if significantly different (>20 seconds)
            if abs(new_timeout - current_timeout) > 20:
                old_timeout = model_perf.timeout_seconds
                model_perf.timeout_seconds = new_timeout
                self.logger.info(f"Gradually updated timeout for {model_perf.model_name}: {old_timeout}s → {new_timeout}s (avg: {mean_time:.1f}s, max: {max_time:.1f}s)")

        except Exception as e:
            self.logger.error(f"Error updating dynamic timeout: {e}")
    
    def get_timeout_for_model(self, model_name: str) -> int:
        """Get current timeout for a model"""
        if model_name in self.models:
            return self.models[model_name].timeout_seconds
        else:
            # Return default timeout for unknown models
            return self.base_timeout
    
    def get_model_stats(self, model_name: str) -> Optional[Dict[str, Any]]:
        """Get comprehensive stats for a model"""
        if model_name not in self.models:
            return None
        
        model_perf = self.models[model_name]
        
        success_rate = (model_perf.successful_requests / model_perf.total_requests * 100) if model_perf.total_requests > 0 else 0
        
        return {
            'model_name': model_perf.model_name,
            'total_requests': model_perf.total_requests,
            'success_rate': round(success_rate, 1),
            'average_response_time': round(model_perf.average_response_time, 2),
            'min_response_time': round(model_perf.min_response_time, 2) if model_perf.min_response_time != float('inf') else 0,
            'max_response_time': round(model_perf.max_response_time, 2),
            'current_timeout': model_perf.timeout_seconds,
            'model_size_gb': round(model_perf.model_size_gb, 1),
            'complexity_factor': round(model_perf.complexity_factor, 2),
            'last_updated': model_perf.last_updated
        }
    
    def get_all_models_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get stats for all models"""
        return {name: self.get_model_stats(name) for name in self.models.keys()}
    
    def analyze_all_available_models(self, ollama_interface) -> Dict[str, ModelPerformance]:
        """Analyze all available models from Ollama"""
        try:
            models_info = ollama_interface.get_available_models()
            
            if models_info.get('success') and 'models' in models_info:
                for model_info in models_info['models']:
                    self.analyze_model_from_ollama(model_info)
                
                self.save_performance_data()
                self.logger.info(f"Analyzed {len(models_info['models'])} available models")
            
            return self.models
            
        except Exception as e:
            self.logger.error(f"Error analyzing available models: {e}")
            return self.models
    
    def cleanup_old_data(self, days_old: int = 30):
        """Clean up old performance data"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            models_to_remove = []
            for model_name, model_perf in self.models.items():
                if model_perf.last_updated:
                    last_update = datetime.fromisoformat(model_perf.last_updated)
                    if last_update < cutoff_date:
                        models_to_remove.append(model_name)
            
            for model_name in models_to_remove:
                del self.models[model_name]
                self.logger.info(f"Removed old performance data for: {model_name}")
            
            if models_to_remove:
                self.save_performance_data()
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")

    def reset_all_timeouts_to_base(self):
        """Reset all model timeouts to base timeout (320s)"""
        try:
            reset_count = 0
            for model_name, model_perf in self.models.items():
                if model_perf.timeout_seconds != self.base_timeout:
                    old_timeout = model_perf.timeout_seconds
                    model_perf.timeout_seconds = self.base_timeout
                    reset_count += 1
                    self.logger.info(f"Reset timeout for {model_name}: {old_timeout}s → {self.base_timeout}s")

            if reset_count > 0:
                self.save_performance_data()
                self.logger.info(f"Reset {reset_count} model timeouts to base timeout ({self.base_timeout}s)")
            else:
                self.logger.info("All model timeouts already at base timeout")

            return reset_count

        except Exception as e:
            self.logger.error(f"Error resetting timeouts: {e}")
            return 0

    def set_conservative_timeouts(self):
        """Set conservative timeouts for all models based on size"""
        try:
            updated_count = 0
            for model_name, model_perf in self.models.items():
                # Calculate conservative timeout
                conservative_timeout = self._calculate_initial_timeout(model_perf)

                if model_perf.timeout_seconds != conservative_timeout:
                    old_timeout = model_perf.timeout_seconds
                    model_perf.timeout_seconds = conservative_timeout
                    updated_count += 1
                    self.logger.info(f"Set conservative timeout for {model_name}: {old_timeout}s → {conservative_timeout}s")

            if updated_count > 0:
                self.save_performance_data()
                self.logger.info(f"Set conservative timeouts for {updated_count} models")

            return updated_count

        except Exception as e:
            self.logger.error(f"Error setting conservative timeouts: {e}")
            return 0
