@echo off
title CYBEX ENTERPRISE - FUTURISTIC GUI
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗███╗   ██╗████████╗  ║
echo ║   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝████╗  ██║╚══██╔══╝  ║
echo ║   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██╔██╗ ██║   ██║     ║
echo ║   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║╚██╗██║   ██║     ║
echo ║   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ███████╗██║ ╚████║   ██║     ║
echo ║    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝   ╚═╝     ║
echo ║                                                                              ║
echo ║                    🚀 FUTURISTIC GUI WITH OLLAMA AI 🚀                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if we're in the correct directory
if not exist "bin\cybex_futuristic.py" (
    echo ❌ Error: bin\cybex_futuristic.py not found
    echo 💡 Make sure you're running this from the CYBEX root directory
    echo.
    pause
    exit /b 1
)

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo 📥 Download from: https://python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check Ollama server
echo 🔍 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not running
    echo.
    echo 🔧 OLLAMA SETUP REQUIRED:
    echo 1. Install Ollama: https://ollama.ai/
    echo 2. Start server: ollama serve
    echo 3. Download model: ollama pull gemma3:4b
    echo.
    set /p continue="Continue without AI? (Y/n): "
    if /i "%continue%"=="n" if /i "%continue%"=="no" (
        echo.
        echo 💡 Setup Ollama first, then run CYBEX_GUI.bat again
        pause
        exit /b 1
    )
) else (
    echo ✅ Ollama server is running - AI features enabled
)

echo.
echo 🚀 Starting CYBEX Futuristic GUI...
echo 🎨 Loading cyberpunk interface with Ollama integration...
echo 💡 This may take a few moments...
echo.

REM Launch CYBEX Enterprise GUI
python bin\cybex_enterprise_launcher.py

REM Handle exit
echo.
if errorlevel 1 (
    echo ❌ CYBEX GUI exited with error
    echo 💡 Check the error messages above
) else (
    echo 👋 CYBEX GUI session ended normally
)

echo.
echo 📖 QUICK HELP:
echo • GUI not starting? Install: pip install pillow tkinter
echo • AI not working? Run: ollama serve
echo • Missing models? Run: ollama pull gemma3:4b
echo • Need more tools? Run: pip install -r requirements.txt
echo.
echo 🎯 CYBEX ENTERPRISE FEATURES:
echo • 🎨 Futuristic cyberpunk GUI with terminal
echo • 🤖 Local AI with Ollama (10+ models supported)
echo • 🛠️ 45+ enterprise tools integrated
echo • 🌐 Web browsing and analysis
echo • 📊 Excel creation and analysis  
echo • 🗄️ Multi-database support
echo • 💻 Development and system tools
echo • 🛡️ Security and monitoring
echo.
echo 💬 COMMANDS TO TRY IN GUI:
echo • ciao
echo • models
echo • scansiona disco C
echo • help
echo • analizza sistema
echo.
pause
