#!/usr/bin/env python3
"""
Cybex Enterprise - Main Entry Point
Advanced Cybernetic Expert Agent with Enterprise features
"""

import sys
import argparse
from pathlib import Path

# Add cybex package to path
sys.path.insert(0, str(Path(__file__).parent))

from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI, SunriseColors
from cybex import __version__


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Cybex Enterprise - Advanced Cybernetic Expert Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
{SunriseColors.ACCENT}Examples:{SunriseColors.RESET}
    python main_enterprise.py                    # Start Enterprise UI
    python main_enterprise.py --no-color         # Disable colors
    python main_enterprise.py --debug            # Enable debug mode

{SunriseColors.INFO}Features:{SunriseColors.RESET}
    • Natural Language Processing in Italian
    • Quick Action Menu with 9 preset functions
    • Real-time System Monitoring
    • AI Model Configuration (Ollama)
    • Chat/Agent Mode Switching
    • Sunrise Color Palette
    • Enterprise-grade Interface

{SunriseColors.GRAY}Cybex Enterprise by AGTECHdesigne{SunriseColors.RESET}
        """
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version=f'Cybex Enterprise {__version__}'
    )
    
    parser.add_argument(
        '--no-color',
        action='store_true',
        help='Disable colored output'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )
    
    return parser.parse_args()


def check_requirements():
    """Check if all requirements are met"""
    try:
        import yaml
        import requests
        import psutil
        import colorama
        return True
    except ImportError as e:
        print(f"{SunriseColors.ERROR}❌ Missing required dependency: {e}{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Please install requirements with: pip install -r requirements.txt{SunriseColors.RESET}")
        return False


def check_ollama_server():
    """Check if Ollama server is running"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False


def print_startup_banner():
    """Print startup banner"""
    banner = f"""
{SunriseColors.BLUE}╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   {SunriseColors.WHITE}  ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝                                 {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗                                 {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE}  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝                                {SunriseColors.BLUE}║
║                                                                              ║
║   {SunriseColors.ORANGE}                    Enterprise Edition                                    {SunriseColors.BLUE}║
║   {SunriseColors.GRAY}                      by AGTECHdesigne                                     {SunriseColors.BLUE}║
║                                                                              ║
║   {SunriseColors.SUCCESS}🚀 Starting Advanced System Administration Interface...{SunriseColors.BLUE}                ║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(banner)


def main():
    """Main entry point"""
    # Parse arguments
    args = parse_arguments()
    
    # Print startup banner
    print_startup_banner()
    
    # Check requirements
    print(f"{SunriseColors.INFO}🔍 Checking system requirements...{SunriseColors.RESET}")
    if not check_requirements():
        sys.exit(1)
    print(f"{SunriseColors.SUCCESS}✅ Requirements check passed{SunriseColors.RESET}")
    
    # Check Ollama server
    print(f"{SunriseColors.INFO}🔍 Checking Ollama server...{SunriseColors.RESET}")
    if not check_ollama_server():
        print(f"{SunriseColors.WARNING}⚠️  Ollama server not detected at localhost:11434{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Please ensure Ollama is installed and running:{SunriseColors.RESET}")
        print(f"  1. Install Ollama from https://ollama.ai")
        print(f"  2. Run: ollama pull gemma2:7b")
        print(f"  3. Start Ollama service")
        print()
        
        response = input(f"{SunriseColors.ACCENT}Continue anyway? (y/N): {SunriseColors.RESET}").strip().lower()
        if response not in ['y', 'yes']:
            sys.exit(1)
    else:
        print(f"{SunriseColors.SUCCESS}✅ Ollama server is running{SunriseColors.RESET}")
    
    try:
        print(f"{SunriseColors.INFO}🚀 Initializing Cybex Enterprise...{SunriseColors.RESET}")
        
        # Initialize and start Enterprise UI
        ui = CybexEnterpriseUI()
        
        # Apply command line arguments
        if args.no_color:
            ui.show_colors = False
            print(f"{SunriseColors.INFO}🎨 Color output disabled{SunriseColors.RESET}")
        
        if args.debug:
            print(f"{SunriseColors.INFO}🐛 Debug mode enabled{SunriseColors.RESET}")
        
        print(f"{SunriseColors.SUCCESS}✅ Cybex Enterprise initialized successfully{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}🎯 Starting interactive interface...{SunriseColors.RESET}")
        
        # Start the Enterprise UI
        ui.start()
        
    except KeyboardInterrupt:
        print(f"\n{SunriseColors.WARNING}⚠️  Shutdown requested by user{SunriseColors.RESET}")
        sys.exit(0)
    except Exception as e:
        print(f"{SunriseColors.ERROR}❌ Fatal error: {e}{SunriseColors.RESET}")
        sys.exit(1)


if __name__ == "__main__":
    main()
