#!/usr/bin/env python3
"""
Test Enterprise Final
Verifica finale che tutti i tool enterprise siano operativi nella GUI
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_agent_tools_enterprise():
    """Test AgentTools con enterprise"""
    print("🏢 Testing AgentTools Enterprise Integration")
    print("=" * 50)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        # Initialize agent tools
        agent_tools = AgentTools()
        
        # Get all available tools
        all_tools = agent_tools.get_available_tools()
        
        print(f"✅ AgentTools initialized")
        print(f"✅ Total tools available: {len(all_tools)}")
        
        # List all tools
        print(f"\n📋 ALL AVAILABLE TOOLS:")
        for tool_name, tool_info in all_tools.items():
            print(f"  • {tool_name}: {tool_info.get('description', 'No description')}")
        
        # Check for enterprise tools specifically
        enterprise_tools = [
            "security_audit", "performance_analysis", "network_security_scan",
            "enterprise_health_check", "system_hardening", "backup_analysis"
        ]
        
        found_enterprise = []
        for tool in enterprise_tools:
            if tool in all_tools:
                found_enterprise.append(tool)
        
        print(f"\n🎯 ENTERPRISE TOOLS FOUND: {len(found_enterprise)}/6")
        for tool in found_enterprise:
            print(f"  ✅ {tool}")
        
        missing_enterprise = [tool for tool in enterprise_tools if tool not in all_tools]
        if missing_enterprise:
            print(f"\n❌ MISSING ENTERPRISE TOOLS:")
            for tool in missing_enterprise:
                print(f"  ❌ {tool}")
        
        return len(found_enterprise) >= 6
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_enterprise_execution():
    """Test esecuzione tool enterprise"""
    print(f"\n⚡ Testing Enterprise Tool Execution")
    print("=" * 40)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test security audit
        print("🔒 Testing security_audit...")
        try:
            result = agent_tools.execute_tool("security_audit", {})
            if result.success:
                print("✅ Security audit executed successfully")
                print(f"   Output length: {len(result.output)} characters")
            else:
                print(f"❌ Security audit failed: {result.error}")
        except Exception as e:
            print(f"❌ Security audit error: {e}")
        
        # Test performance analysis
        print("\n⚡ Testing performance_analysis...")
        try:
            result = agent_tools.execute_tool("performance_analysis", {})
            if result.success:
                print("✅ Performance analysis executed successfully")
                print(f"   Output length: {len(result.output)} characters")
            else:
                print(f"❌ Performance analysis failed: {result.error}")
        except Exception as e:
            print(f"❌ Performance analysis error: {e}")
        
        # Test enterprise health check
        print("\n🏥 Testing enterprise_health_check...")
        try:
            result = agent_tools.execute_tool("enterprise_health_check", {})
            if result.success:
                print("✅ Enterprise health check executed successfully")
                print(f"   Output length: {len(result.output)} characters")
            else:
                print(f"❌ Enterprise health check failed: {result.error}")
        except Exception as e:
            print(f"❌ Enterprise health check error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def show_enterprise_ready_summary():
    """Mostra riepilogo enterprise ready"""
    summary = f"""
╔═══════════════════════════════════════════════════════════════════════════════╗
║                    🏢 CYBEX ENTERPRISE VERSION READY!                        ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ ✅ ENTERPRISE TOOLS INTEGRATED IN GUI:                                        ║
║   • security_audit - Professional security analysis                          ║
║   • performance_analysis - Real-time performance monitoring                  ║
║   • network_security_scan - Network vulnerability assessment                 ║
║   • enterprise_health_check - Complete system health check                   ║
║   • system_hardening - Security hardening recommendations                    ║
║   • backup_analysis - Backup and recovery planning                           ║
║                                                                               ║
║ 🚀 LAUNCH WITH ENTERPRISE CAPABILITIES:                                       ║
║   • Double-click: cybex_futuristic.bat                                       ║
║   • Command line: python cybex_futuristic.py                                 ║
║   • All enterprise tools available via AI commands                           ║
║                                                                               ║
║ 💬 ENTERPRISE AI COMMANDS:                                                    ║
║   • "Esegui security audit"                                                  ║
║   • "Analizza performance sistema"                                           ║
║   • "Scansiona sicurezza rete"                                               ║
║   • "Controlla salute enterprise"                                            ║
║   • "Mostra raccomandazioni hardening"                                       ║
║   • "Analizza backup sistema"                                                ║
║                                                                               ║
║ 🎯 ENTERPRISE FEATURES:                                                       ║
║   • Professional-grade security auditing                                     ║
║   • Real-time performance monitoring                                         ║
║   • Network security assessment                                              ║
║   • Comprehensive health checking                                            ║
║   • Security hardening guidance                                              ║
║   • Backup and recovery analysis                                             ║
║   • AI-powered natural language interface                                    ║
║   • Futuristic GUI with enterprise branding                                  ║
║                                                                               ║
║ 💼 ENTERPRISE LEVEL: FULLY OPERATIONAL! 🏢                                   ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝
"""
    print(summary)


def main():
    """Run enterprise final test"""
    print("🎯 Cybex Enterprise Final Test")
    print("=" * 60)
    
    # Test 1: AgentTools integration
    test1_success = test_agent_tools_enterprise()
    
    # Test 2: Enterprise tool execution
    test2_success = test_enterprise_execution()
    
    print(f"\n{'='*60}")
    print(f"🎯 FINAL TEST RESULTS:")
    print(f"  • AgentTools Integration: {'✅ PASSED' if test1_success else '❌ FAILED'}")
    print(f"  • Enterprise Execution: {'✅ PASSED' if test2_success else '❌ FAILED'}")
    
    if test1_success and test2_success:
        show_enterprise_ready_summary()
        
        print(f"\n🎉 ENTERPRISE VERSION FULLY OPERATIONAL!")
        print(f"🏢 cybex_futuristic.bat now launches with ALL enterprise capabilities!")
        
        print(f"\n🚀 READY TO USE:")
        print(f"  1. Launch: cybex_futuristic.bat")
        print(f"  2. Use AI commands for enterprise tools")
        print(f"  3. Professional enterprise analysis available")
        
        print(f"\n💫 ENTERPRISE LEVEL: ACHIEVED! 🏢✨")
        return True
    else:
        print(f"\n❌ ENTERPRISE INTEGRATION INCOMPLETE")
        print(f"💡 Check errors above and fix issues")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
