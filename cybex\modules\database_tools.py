#!/usr/bin/env python3
"""
Database Tools for CYBEX Enterprise
Comprehensive database management and analysis tools
"""

import sqlite3
import json
import csv
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import pandas as pd
import sqlalchemy
from sqlalchemy import create_engine, text, inspect
import pymongo
import redis
import psycopg2
import mysql.connector


@dataclass
class DatabaseConnection:
    """Database connection information"""
    db_type: str
    host: str
    port: int
    database: str
    username: str
    password: str
    connection_string: str


class DatabaseTools:
    """Comprehensive database management tools"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Active connections
        self.connections = {}
        self.engines = {}
    
    def connect_database(self, connection_id: str, db_type: str, **kwargs) -> Dict[str, Any]:
        """Connect to database"""
        try:
            if db_type.lower() == 'sqlite':
                return self._connect_sqlite(connection_id, **kwargs)
            elif db_type.lower() == 'postgresql':
                return self._connect_postgresql(connection_id, **kwargs)
            elif db_type.lower() == 'mysql':
                return self._connect_mysql(connection_id, **kwargs)
            elif db_type.lower() == 'mongodb':
                return self._connect_mongodb(connection_id, **kwargs)
            elif db_type.lower() == 'redis':
                return self._connect_redis(connection_id, **kwargs)
            else:
                return {'success': False, 'error': f'Unsupported database type: {db_type}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _connect_sqlite(self, connection_id: str, database: str, **kwargs) -> Dict[str, Any]:
        """Connect to SQLite database"""
        try:
            conn = sqlite3.connect(database)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            
            self.connections[connection_id] = {
                'type': 'sqlite',
                'connection': conn,
                'database': database
            }
            
            return {
                'success': True,
                'connection_id': connection_id,
                'database_type': 'sqlite',
                'database': database,
                'message': f'Connected to SQLite database: {database}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _connect_postgresql(self, connection_id: str, host: str, port: int, 
                           database: str, username: str, password: str, **kwargs) -> Dict[str, Any]:
        """Connect to PostgreSQL database"""
        try:
            conn_string = f"postgresql://{username}:{password}@{host}:{port}/{database}"
            engine = create_engine(conn_string)
            
            # Test connection
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.engines[connection_id] = {
                'type': 'postgresql',
                'engine': engine,
                'connection_string': conn_string
            }
            
            return {
                'success': True,
                'connection_id': connection_id,
                'database_type': 'postgresql',
                'host': host,
                'port': port,
                'database': database,
                'message': f'Connected to PostgreSQL database: {database}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _connect_mysql(self, connection_id: str, host: str, port: int,
                      database: str, username: str, password: str, **kwargs) -> Dict[str, Any]:
        """Connect to MySQL database"""
        try:
            conn_string = f"mysql+pymysql://{username}:{password}@{host}:{port}/{database}"
            engine = create_engine(conn_string)
            
            # Test connection
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            
            self.engines[connection_id] = {
                'type': 'mysql',
                'engine': engine,
                'connection_string': conn_string
            }
            
            return {
                'success': True,
                'connection_id': connection_id,
                'database_type': 'mysql',
                'host': host,
                'port': port,
                'database': database,
                'message': f'Connected to MySQL database: {database}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _connect_mongodb(self, connection_id: str, host: str, port: int,
                        database: str, username: str = None, password: str = None, **kwargs) -> Dict[str, Any]:
        """Connect to MongoDB database"""
        try:
            if username and password:
                conn_string = f"mongodb://{username}:{password}@{host}:{port}/{database}"
            else:
                conn_string = f"mongodb://{host}:{port}/{database}"
            
            client = pymongo.MongoClient(conn_string)
            db = client[database]
            
            # Test connection
            client.admin.command('ping')
            
            self.connections[connection_id] = {
                'type': 'mongodb',
                'client': client,
                'database': db,
                'connection_string': conn_string
            }
            
            return {
                'success': True,
                'connection_id': connection_id,
                'database_type': 'mongodb',
                'host': host,
                'port': port,
                'database': database,
                'message': f'Connected to MongoDB database: {database}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _connect_redis(self, connection_id: str, host: str, port: int,
                      password: str = None, db: int = 0, **kwargs) -> Dict[str, Any]:
        """Connect to Redis database"""
        try:
            client = redis.Redis(host=host, port=port, password=password, db=db, decode_responses=True)
            
            # Test connection
            client.ping()
            
            self.connections[connection_id] = {
                'type': 'redis',
                'client': client,
                'host': host,
                'port': port,
                'db': db
            }
            
            return {
                'success': True,
                'connection_id': connection_id,
                'database_type': 'redis',
                'host': host,
                'port': port,
                'database': db,
                'message': f'Connected to Redis database: {db}'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def execute_query(self, connection_id: str, query: str, parameters: List = None) -> Dict[str, Any]:
        """Execute SQL query"""
        try:
            if connection_id not in self.connections and connection_id not in self.engines:
                return {'success': False, 'error': 'Connection not found'}
            
            start_time = time.time()
            
            # SQLite
            if connection_id in self.connections:
                conn_info = self.connections[connection_id]
                if conn_info['type'] == 'sqlite':
                    return self._execute_sqlite_query(conn_info, query, parameters)
                elif conn_info['type'] == 'mongodb':
                    return self._execute_mongodb_query(conn_info, query, parameters)
                elif conn_info['type'] == 'redis':
                    return self._execute_redis_command(conn_info, query, parameters)
            
            # SQL databases with SQLAlchemy
            elif connection_id in self.engines:
                engine_info = self.engines[connection_id]
                return self._execute_sqlalchemy_query(engine_info, query, parameters)
            
            return {'success': False, 'error': 'Invalid connection type'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_sqlite_query(self, conn_info: Dict, query: str, parameters: List = None) -> Dict[str, Any]:
        """Execute SQLite query"""
        try:
            conn = conn_info['connection']
            cursor = conn.cursor()
            
            start_time = time.time()
            
            if parameters:
                cursor.execute(query, parameters)
            else:
                cursor.execute(query)
            
            execution_time = time.time() - start_time
            
            # Handle different query types
            if query.strip().upper().startswith('SELECT'):
                rows = cursor.fetchall()
                columns = [description[0] for description in cursor.description]
                data = [dict(zip(columns, row)) for row in rows]
                
                return {
                    'success': True,
                    'query_type': 'SELECT',
                    'rows_returned': len(data),
                    'columns': columns,
                    'data': data,
                    'execution_time': execution_time
                }
            else:
                conn.commit()
                return {
                    'success': True,
                    'query_type': 'MODIFY',
                    'rows_affected': cursor.rowcount,
                    'execution_time': execution_time
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_sqlalchemy_query(self, engine_info: Dict, query: str, parameters: List = None) -> Dict[str, Any]:
        """Execute query using SQLAlchemy"""
        try:
            engine = engine_info['engine']
            
            start_time = time.time()
            
            with engine.connect() as conn:
                if parameters:
                    result = conn.execute(text(query), parameters)
                else:
                    result = conn.execute(text(query))
                
                execution_time = time.time() - start_time
                
                # Handle different query types
                if query.strip().upper().startswith('SELECT'):
                    rows = result.fetchall()
                    columns = list(result.keys())
                    data = [dict(zip(columns, row)) for row in rows]
                    
                    return {
                        'success': True,
                        'query_type': 'SELECT',
                        'rows_returned': len(data),
                        'columns': columns,
                        'data': data,
                        'execution_time': execution_time
                    }
                else:
                    conn.commit()
                    return {
                        'success': True,
                        'query_type': 'MODIFY',
                        'rows_affected': result.rowcount,
                        'execution_time': execution_time
                    }
                    
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_database_schema(self, connection_id: str) -> Dict[str, Any]:
        """Get database schema information"""
        try:
            if connection_id in self.connections:
                conn_info = self.connections[connection_id]
                if conn_info['type'] == 'sqlite':
                    return self._get_sqlite_schema(conn_info)
                elif conn_info['type'] == 'mongodb':
                    return self._get_mongodb_schema(conn_info)
            
            elif connection_id in self.engines:
                engine_info = self.engines[connection_id]
                return self._get_sqlalchemy_schema(engine_info)
            
            return {'success': False, 'error': 'Connection not found'}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_sqlite_schema(self, conn_info: Dict) -> Dict[str, Any]:
        """Get SQLite schema"""
        try:
            conn = conn_info['connection']
            cursor = conn.cursor()
            
            # Get tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            schema = {'tables': {}}
            
            for table in tables:
                # Get table info
                cursor.execute(f"PRAGMA table_info({table})")
                columns = []
                for row in cursor.fetchall():
                    columns.append({
                        'name': row[1],
                        'type': row[2],
                        'not_null': bool(row[3]),
                        'default_value': row[4],
                        'primary_key': bool(row[5])
                    })
                
                # Get row count
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                row_count = cursor.fetchone()[0]
                
                schema['tables'][table] = {
                    'columns': columns,
                    'row_count': row_count
                }
            
            return {
                'success': True,
                'database_type': 'sqlite',
                'table_count': len(tables),
                'schema': schema
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _get_sqlalchemy_schema(self, engine_info: Dict) -> Dict[str, Any]:
        """Get schema using SQLAlchemy"""
        try:
            engine = engine_info['engine']
            inspector = inspect(engine)
            
            tables = inspector.get_table_names()
            schema = {'tables': {}}
            
            for table in tables:
                columns = inspector.get_columns(table)
                
                # Get row count
                with engine.connect() as conn:
                    result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    row_count = result.scalar()
                
                schema['tables'][table] = {
                    'columns': [
                        {
                            'name': col['name'],
                            'type': str(col['type']),
                            'nullable': col['nullable'],
                            'default': col['default']
                        }
                        for col in columns
                    ],
                    'row_count': row_count
                }
            
            return {
                'success': True,
                'database_type': engine_info['type'],
                'table_count': len(tables),
                'schema': schema
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def export_data(self, connection_id: str, table_name: str, 
                   export_format: str = 'csv', file_path: str = None) -> Dict[str, Any]:
        """Export data from database"""
        try:
            if connection_id not in self.connections and connection_id not in self.engines:
                return {'success': False, 'error': 'Connection not found'}
            
            # Execute SELECT query
            query = f"SELECT * FROM {table_name}"
            result = self.execute_query(connection_id, query)
            
            if not result['success']:
                return result
            
            data = result['data']
            
            if not file_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                file_path = f"{table_name}_export_{timestamp}.{export_format}"
            
            # Export based on format
            if export_format.lower() == 'csv':
                return self._export_to_csv(data, file_path)
            elif export_format.lower() == 'json':
                return self._export_to_json(data, file_path)
            elif export_format.lower() == 'excel':
                return self._export_to_excel(data, file_path)
            else:
                return {'success': False, 'error': f'Unsupported export format: {export_format}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _export_to_csv(self, data: List[Dict], file_path: str) -> Dict[str, Any]:
        """Export data to CSV"""
        try:
            if not data:
                return {'success': False, 'error': 'No data to export'}
            
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = data[0].keys()
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(data)
            
            return {
                'success': True,
                'file_path': file_path,
                'format': 'csv',
                'rows_exported': len(data),
                'message': f'Successfully exported {len(data)} rows to CSV'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _export_to_json(self, data: List[Dict], file_path: str) -> Dict[str, Any]:
        """Export data to JSON"""
        try:
            with open(file_path, 'w', encoding='utf-8') as jsonfile:
                json.dump(data, jsonfile, indent=2, default=str)
            
            return {
                'success': True,
                'file_path': file_path,
                'format': 'json',
                'rows_exported': len(data),
                'message': f'Successfully exported {len(data)} rows to JSON'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _export_to_excel(self, data: List[Dict], file_path: str) -> Dict[str, Any]:
        """Export data to Excel"""
        try:
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False)
            
            return {
                'success': True,
                'file_path': file_path,
                'format': 'excel',
                'rows_exported': len(data),
                'message': f'Successfully exported {len(data)} rows to Excel'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def close_connection(self, connection_id: str) -> Dict[str, Any]:
        """Close database connection"""
        try:
            if connection_id in self.connections:
                conn_info = self.connections[connection_id]
                if conn_info['type'] == 'sqlite':
                    conn_info['connection'].close()
                elif conn_info['type'] == 'mongodb':
                    conn_info['client'].close()
                elif conn_info['type'] == 'redis':
                    conn_info['client'].close()
                
                del self.connections[connection_id]
            
            elif connection_id in self.engines:
                engine_info = self.engines[connection_id]
                engine_info['engine'].dispose()
                del self.engines[connection_id]
            
            else:
                return {'success': False, 'error': 'Connection not found'}
            
            return {
                'success': True,
                'connection_id': connection_id,
                'message': f'Connection {connection_id} closed successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def list_connections(self) -> Dict[str, Any]:
        """List active database connections"""
        try:
            connections = {}
            
            for conn_id, conn_info in self.connections.items():
                connections[conn_id] = {
                    'type': conn_info['type'],
                    'status': 'active'
                }
            
            for conn_id, engine_info in self.engines.items():
                connections[conn_id] = {
                    'type': engine_info['type'],
                    'status': 'active'
                }
            
            return {
                'success': True,
                'connection_count': len(connections),
                'connections': connections
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


    def _execute_mongodb_query(self, conn_info: Dict, query: str, parameters: List = None) -> Dict[str, Any]:
        """Execute MongoDB query (simplified)"""
        try:
            db = conn_info['database']
            # This is a simplified implementation
            # In practice, you'd parse the query and execute appropriate MongoDB operations
            return {
                'success': True,
                'message': 'MongoDB query execution not fully implemented',
                'query': query
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _execute_redis_command(self, conn_info: Dict, command: str, parameters: List = None) -> Dict[str, Any]:
        """Execute Redis command"""
        try:
            client = conn_info['client']
            # This is a simplified implementation
            # In practice, you'd parse the command and execute appropriate Redis operations
            return {
                'success': True,
                'message': 'Redis command execution not fully implemented',
                'command': command
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def _get_mongodb_schema(self, conn_info: Dict) -> Dict[str, Any]:
        """Get MongoDB schema (simplified)"""
        try:
            db = conn_info['database']
            collections = db.list_collection_names()

            return {
                'success': True,
                'database_type': 'mongodb',
                'collection_count': len(collections),
                'collections': collections
            }
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_database_tools(log_manager=None) -> DatabaseTools:
    """Factory function to create database tools"""
    return DatabaseTools(log_manager)
