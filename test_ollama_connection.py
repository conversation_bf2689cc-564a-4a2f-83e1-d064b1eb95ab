#!/usr/bin/env python3
"""
Test Ollama connection for CYBEX Enterprise
"""

import sys
import requests
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_ollama_connection():
    """Test basic Ollama connection"""
    print("🔍 Testing Ollama Connection...")
    
    try:
        # Test Ollama API
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"✅ Ollama connected - {len(models)} models available")
            
            for model in models[:5]:  # Show first 5 models
                print(f"   • {model['name']}")
            
            return True
        else:
            print(f"❌ Ollama API error: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Ollama connection failed: {e}")
        return False

def test_cybex_imports():
    """Test CYBEX imports"""
    print("\n🔍 Testing CYBEX Imports...")
    
    try:
        # Test basic imports
        from cybex.core.cybex_core import CybexCore
        print("✅ CybexCore import successful")
        
        from cybex.modules.ollama_interface import OllamaInterface
        print("✅ OllamaInterface import successful")
        
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        print("✅ CybexEnterpriseUI import successful")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cybex_initialization():
    """Test CYBEX initialization"""
    print("\n🔍 Testing CYBEX Initialization...")
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI

        print("Creating CybexEnterpriseUI instance...")
        cli = CybexEnterpriseUI()
        print("✅ CybexEnterpriseUI created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🤖 CYBEX ENTERPRISE - OLLAMA CONNECTION TEST")
    print("=" * 60)
    
    # Test 1: Ollama connection
    ollama_ok = test_ollama_connection()
    
    # Test 2: CYBEX imports
    imports_ok = test_cybex_imports()
    
    # Test 3: CYBEX initialization (only if imports work)
    init_ok = False
    if imports_ok:
        init_ok = test_cybex_initialization()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"Ollama Connection: {'✅ OK' if ollama_ok else '❌ FAILED'}")
    print(f"CYBEX Imports: {'✅ OK' if imports_ok else '❌ FAILED'}")
    print(f"CYBEX Initialization: {'✅ OK' if init_ok else '❌ FAILED'}")
    
    if ollama_ok and imports_ok and init_ok:
        print("\n🎉 ALL TESTS PASSED! CYBEX should work with Ollama.")
        print("\n💡 Try running: python bin\\main.py")
    else:
        print("\n❌ Some tests failed. Check the errors above.")
        
        if not ollama_ok:
            print("\n🔧 Ollama Fix:")
            print("   1. Make sure Ollama is running: ollama serve")
            print("   2. Download a model: ollama pull llama2")
        
        if not imports_ok:
            print("\n🔧 Import Fix:")
            print("   1. Check Python path")
            print("   2. Install missing dependencies")
        
        if not init_ok:
            print("\n🔧 Initialization Fix:")
            print("   1. Check configuration files")
            print("   2. Check log files for errors")

if __name__ == "__main__":
    main()
