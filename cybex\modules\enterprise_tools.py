#!/usr/bin/env python3
"""
Cybex Enterprise Tools
Tool avanzati per capacità enterprise-level
"""

import os
import sys
import json
import time
import psutil
import subprocess
import platform
import shutil
import winreg
from pathlib import Path
from typing import Dict, List, Optional, Any, Tu<PERSON>
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class SecurityAuditResult:
    """Risultato audit sicurezza"""
    category: str
    status: str  # "secure", "warning", "critical"
    description: str
    recommendation: str
    risk_level: int  # 1-10

@dataclass
class BackupJob:
    """Job di backup"""
    id: str
    source_path: str
    destination_path: str
    schedule: str
    last_run: Optional[datetime]
    status: str

@dataclass
class PerformanceMetric:
    """Metrica performance"""
    name: str
    value: float
    unit: str
    status: str  # "good", "warning", "critical"
    threshold: float

class EnterpriseSecurityManager:
    """Gestione sicurezza enterprise"""
    
    def __init__(self, log_manager):
        self.log_manager = log_manager
        self.audit_results = []
    
    def security_audit(self) -> List[SecurityAuditResult]:
        """Esegui audit sicurezza completo"""
        results = []
        
        try:
            # Windows Security Audit
            if platform.system() == "Windows":
                results.extend(self._audit_windows_security())
            else:
                results.extend(self._audit_linux_security())
            
            # Common security checks
            results.extend(self._audit_network_security())
            results.extend(self._audit_file_permissions())
            results.extend(self._audit_running_services())
            
            self.audit_results = results
            return results
            
        except Exception as e:
            self.log_manager.log_error(f"Security audit failed: {e}")
            return []
    
    def _audit_windows_security(self) -> List[SecurityAuditResult]:
        """Audit sicurezza Windows"""
        results = []
        
        try:
            # Check Windows Defender
            result = subprocess.run(
                ['powershell', '-Command', 'Get-MpComputerStatus'],
                capture_output=True, text=True, timeout=30
            )
            
            if "AntivirusEnabled" in result.stdout and "True" in result.stdout:
                results.append(SecurityAuditResult(
                    category="Antivirus",
                    status="secure",
                    description="Windows Defender is enabled",
                    recommendation="Keep antivirus updated",
                    risk_level=2
                ))
            else:
                results.append(SecurityAuditResult(
                    category="Antivirus",
                    status="critical",
                    description="Windows Defender is disabled",
                    recommendation="Enable Windows Defender immediately",
                    risk_level=9
                ))
            
            # Check Windows Firewall
            result = subprocess.run(
                ['netsh', 'advfirewall', 'show', 'allprofiles', 'state'],
                capture_output=True, text=True, timeout=30
            )
            
            if "ON" in result.stdout:
                results.append(SecurityAuditResult(
                    category="Firewall",
                    status="secure",
                    description="Windows Firewall is enabled",
                    recommendation="Review firewall rules periodically",
                    risk_level=2
                ))
            else:
                results.append(SecurityAuditResult(
                    category="Firewall",
                    status="critical",
                    description="Windows Firewall is disabled",
                    recommendation="Enable Windows Firewall",
                    risk_level=8
                ))
            
            # Check UAC
            try:
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                    r"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System")
                uac_value = winreg.QueryValueEx(key, "EnableLUA")[0]
                winreg.CloseKey(key)
                
                if uac_value == 1:
                    results.append(SecurityAuditResult(
                        category="UAC",
                        status="secure",
                        description="User Account Control is enabled",
                        recommendation="Keep UAC enabled",
                        risk_level=2
                    ))
                else:
                    results.append(SecurityAuditResult(
                        category="UAC",
                        status="warning",
                        description="User Account Control is disabled",
                        recommendation="Enable UAC for better security",
                        risk_level=6
                    ))
            except Exception:
                results.append(SecurityAuditResult(
                    category="UAC",
                    status="warning",
                    description="Could not check UAC status",
                    recommendation="Manually verify UAC settings",
                    risk_level=4
                ))
            
        except Exception as e:
            self.log_manager.log_error(f"Windows security audit error: {e}")
        
        return results
    
    def _audit_linux_security(self) -> List[SecurityAuditResult]:
        """Audit sicurezza Linux"""
        results = []
        
        try:
            # Check if firewall is active
            result = subprocess.run(['ufw', 'status'], capture_output=True, text=True)
            if result.returncode == 0:
                if "Status: active" in result.stdout:
                    results.append(SecurityAuditResult(
                        category="Firewall",
                        status="secure",
                        description="UFW firewall is active",
                        recommendation="Review firewall rules",
                        risk_level=2
                    ))
                else:
                    results.append(SecurityAuditResult(
                        category="Firewall",
                        status="warning",
                        description="UFW firewall is inactive",
                        recommendation="Enable UFW firewall",
                        risk_level=6
                    ))
            
            # Check for automatic updates
            if Path("/etc/apt/apt.conf.d/20auto-upgrades").exists():
                results.append(SecurityAuditResult(
                    category="Updates",
                    status="secure",
                    description="Automatic updates are configured",
                    recommendation="Monitor update logs",
                    risk_level=2
                ))
            else:
                results.append(SecurityAuditResult(
                    category="Updates",
                    status="warning",
                    description="Automatic updates not configured",
                    recommendation="Enable automatic security updates",
                    risk_level=5
                ))
            
        except Exception as e:
            self.log_manager.log_error(f"Linux security audit error: {e}")
        
        return results
    
    def _audit_network_security(self) -> List[SecurityAuditResult]:
        """Audit sicurezza rete"""
        results = []
        
        try:
            # Check for open ports
            connections = psutil.net_connections(kind='inet')
            listening_ports = [conn.laddr.port for conn in connections 
                             if conn.status == 'LISTEN']
            
            # Common dangerous ports
            dangerous_ports = [21, 23, 135, 139, 445, 1433, 3389]
            open_dangerous = [port for port in listening_ports if port in dangerous_ports]
            
            if open_dangerous:
                results.append(SecurityAuditResult(
                    category="Network",
                    status="warning",
                    description=f"Potentially dangerous ports open: {open_dangerous}",
                    recommendation="Review and close unnecessary ports",
                    risk_level=7
                ))
            else:
                results.append(SecurityAuditResult(
                    category="Network",
                    status="secure",
                    description="No obviously dangerous ports detected",
                    recommendation="Continue monitoring network activity",
                    risk_level=3
                ))
            
        except Exception as e:
            self.log_manager.log_error(f"Network security audit error: {e}")
        
        return results
    
    def _audit_file_permissions(self) -> List[SecurityAuditResult]:
        """Audit permessi file"""
        results = []
        
        try:
            if platform.system() == "Windows":
                # Check system directories permissions
                system_dirs = [r"C:\Windows\System32", r"C:\Program Files"]
                for dir_path in system_dirs:
                    if os.path.exists(dir_path):
                        # Basic check - in real enterprise would be more thorough
                        results.append(SecurityAuditResult(
                            category="File Permissions",
                            status="secure",
                            description=f"System directory {dir_path} exists",
                            recommendation="Monitor for unauthorized changes",
                            risk_level=3
                        ))
            else:
                # Check critical Linux directories
                critical_dirs = ["/etc", "/usr/bin", "/usr/sbin"]
                for dir_path in critical_dirs:
                    if os.path.exists(dir_path):
                        stat = os.stat(dir_path)
                        # Check if world-writable
                        if stat.st_mode & 0o002:
                            results.append(SecurityAuditResult(
                                category="File Permissions",
                                status="critical",
                                description=f"Critical directory {dir_path} is world-writable",
                                recommendation="Fix permissions immediately",
                                risk_level=9
                            ))
                        else:
                            results.append(SecurityAuditResult(
                                category="File Permissions",
                                status="secure",
                                description=f"Critical directory {dir_path} has secure permissions",
                                recommendation="Continue monitoring",
                                risk_level=2
                            ))
            
        except Exception as e:
            self.log_manager.log_error(f"File permissions audit error: {e}")
        
        return results
    
    def _audit_running_services(self) -> List[SecurityAuditResult]:
        """Audit servizi in esecuzione"""
        results = []
        
        try:
            # Get running processes
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Check for suspicious processes
            suspicious_names = ['nc.exe', 'netcat', 'ncat', 'telnet']
            suspicious_found = [p for p in processes 
                              if any(sus in p['name'].lower() for sus in suspicious_names)]
            
            if suspicious_found:
                results.append(SecurityAuditResult(
                    category="Processes",
                    status="warning",
                    description=f"Potentially suspicious processes: {[p['name'] for p in suspicious_found]}",
                    recommendation="Investigate these processes",
                    risk_level=6
                ))
            
            # Count total processes
            total_processes = len(processes)
            if total_processes > 200:
                results.append(SecurityAuditResult(
                    category="Processes",
                    status="warning",
                    description=f"High number of running processes: {total_processes}",
                    recommendation="Review and optimize running processes",
                    risk_level=4
                ))
            else:
                results.append(SecurityAuditResult(
                    category="Processes",
                    status="secure",
                    description=f"Normal number of processes: {total_processes}",
                    recommendation="Continue monitoring",
                    risk_level=2
                ))
            
        except Exception as e:
            self.log_manager.log_error(f"Process audit error: {e}")
        
        return results
    
    def generate_security_report(self) -> str:
        """Genera report sicurezza"""
        if not self.audit_results:
            return "No security audit results available. Run security audit first."
        
        report = "=== CYBEX ENTERPRISE SECURITY AUDIT REPORT ===\n\n"
        report += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"Total checks: {len(self.audit_results)}\n\n"
        
        # Group by status
        critical = [r for r in self.audit_results if r.status == "critical"]
        warnings = [r for r in self.audit_results if r.status == "warning"]
        secure = [r for r in self.audit_results if r.status == "secure"]
        
        report += f"🔴 CRITICAL ISSUES: {len(critical)}\n"
        for result in critical:
            report += f"  • {result.category}: {result.description}\n"
            report += f"    Recommendation: {result.recommendation}\n"
            report += f"    Risk Level: {result.risk_level}/10\n\n"
        
        report += f"🟡 WARNINGS: {len(warnings)}\n"
        for result in warnings:
            report += f"  • {result.category}: {result.description}\n"
            report += f"    Recommendation: {result.recommendation}\n"
            report += f"    Risk Level: {result.risk_level}/10\n\n"
        
        report += f"🟢 SECURE: {len(secure)}\n"
        for result in secure:
            report += f"  • {result.category}: {result.description}\n\n"
        
        # Overall risk score
        avg_risk = sum(r.risk_level for r in self.audit_results) / len(self.audit_results)
        report += f"OVERALL RISK SCORE: {avg_risk:.1f}/10\n"
        
        if avg_risk >= 7:
            report += "STATUS: HIGH RISK - Immediate action required\n"
        elif avg_risk >= 4:
            report += "STATUS: MEDIUM RISK - Review and improve\n"
        else:
            report += "STATUS: LOW RISK - Good security posture\n"
        
        return report


class EnterpriseBackupManager:
    """Gestione backup enterprise"""
    
    def __init__(self, log_manager):
        self.log_manager = log_manager
        self.backup_jobs = []
        self.backup_dir = Path.home() / "CybexBackups"
        self.backup_dir.mkdir(exist_ok=True)
    
    def create_backup_job(self, source: str, destination: str = None, 
                         schedule: str = "manual") -> str:
        """Crea job di backup"""
        try:
            job_id = f"backup_{int(time.time())}"
            
            if not destination:
                destination = str(self.backup_dir / f"{Path(source).name}_{job_id}")
            
            job = BackupJob(
                id=job_id,
                source_path=source,
                destination_path=destination,
                schedule=schedule,
                last_run=None,
                status="created"
            )
            
            self.backup_jobs.append(job)
            self.log_manager.log_info(f"Backup job created: {job_id}")
            
            return job_id
            
        except Exception as e:
            self.log_manager.log_error(f"Failed to create backup job: {e}")
            return ""
    
    def execute_backup(self, job_id: str) -> bool:
        """Esegui backup"""
        try:
            job = next((j for j in self.backup_jobs if j.id == job_id), None)
            if not job:
                return False
            
            source_path = Path(job.source_path)
            dest_path = Path(job.destination_path)
            
            if not source_path.exists():
                job.status = "failed"
                return False
            
            # Create destination directory
            dest_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Perform backup
            if source_path.is_file():
                shutil.copy2(source_path, dest_path)
            else:
                shutil.copytree(source_path, dest_path, dirs_exist_ok=True)
            
            job.last_run = datetime.now()
            job.status = "completed"
            
            self.log_manager.log_info(f"Backup completed: {job_id}")
            return True
            
        except Exception as e:
            self.log_manager.log_error(f"Backup failed: {e}")
            if job:
                job.status = "failed"
            return False
    
    def list_backups(self) -> List[Dict]:
        """Lista backup"""
        return [
            {
                "id": job.id,
                "source": job.source_path,
                "destination": job.destination_path,
                "schedule": job.schedule,
                "last_run": job.last_run.isoformat() if job.last_run else None,
                "status": job.status
            }
            for job in self.backup_jobs
        ]


class EnterprisePerformanceAnalyzer:
    """Analizzatore performance enterprise"""
    
    def __init__(self, log_manager):
        self.log_manager = log_manager
    
    def analyze_performance(self) -> List[PerformanceMetric]:
        """Analizza performance sistema"""
        metrics = []
        
        try:
            # CPU Analysis
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = "good" if cpu_percent < 70 else "warning" if cpu_percent < 90 else "critical"
            metrics.append(PerformanceMetric(
                name="CPU Usage",
                value=cpu_percent,
                unit="%",
                status=cpu_status,
                threshold=70
            ))
            
            # Memory Analysis
            memory = psutil.virtual_memory()
            mem_status = "good" if memory.percent < 80 else "warning" if memory.percent < 95 else "critical"
            metrics.append(PerformanceMetric(
                name="Memory Usage",
                value=memory.percent,
                unit="%",
                status=mem_status,
                threshold=80
            ))
            
            # Disk Analysis
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_percent = (usage.used / usage.total) * 100
                    disk_status = "good" if disk_percent < 85 else "warning" if disk_percent < 95 else "critical"
                    
                    metrics.append(PerformanceMetric(
                        name=f"Disk {partition.device}",
                        value=disk_percent,
                        unit="%",
                        status=disk_status,
                        threshold=85
                    ))
                except PermissionError:
                    continue
            
            # Network Analysis
            net_io = psutil.net_io_counters()
            if net_io:
                # Simple network activity indicator
                total_bytes = net_io.bytes_sent + net_io.bytes_recv
                metrics.append(PerformanceMetric(
                    name="Network Total",
                    value=total_bytes / (1024**3),  # GB
                    unit="GB",
                    status="good",
                    threshold=float('inf')
                ))
            
        except Exception as e:
            self.log_manager.log_error(f"Performance analysis error: {e}")
        
        return metrics
    
    def generate_performance_report(self) -> str:
        """Genera report performance"""
        metrics = self.analyze_performance()
        
        report = "=== CYBEX ENTERPRISE PERFORMANCE REPORT ===\n\n"
        report += f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        critical_metrics = [m for m in metrics if m.status == "critical"]
        warning_metrics = [m for m in metrics if m.status == "warning"]
        good_metrics = [m for m in metrics if m.status == "good"]
        
        if critical_metrics:
            report += "🔴 CRITICAL PERFORMANCE ISSUES:\n"
            for metric in critical_metrics:
                report += f"  • {metric.name}: {metric.value:.1f}{metric.unit} (threshold: {metric.threshold}{metric.unit})\n"
            report += "\n"
        
        if warning_metrics:
            report += "🟡 PERFORMANCE WARNINGS:\n"
            for metric in warning_metrics:
                report += f"  • {metric.name}: {metric.value:.1f}{metric.unit} (threshold: {metric.threshold}{metric.unit})\n"
            report += "\n"
        
        if good_metrics:
            report += "🟢 GOOD PERFORMANCE:\n"
            for metric in good_metrics:
                report += f"  • {metric.name}: {metric.value:.1f}{metric.unit}\n"
            report += "\n"
        
        # Performance recommendations
        report += "RECOMMENDATIONS:\n"
        if critical_metrics or warning_metrics:
            report += "• Monitor resource usage closely\n"
            report += "• Consider upgrading hardware if issues persist\n"
            report += "• Review running processes and services\n"
        else:
            report += "• System performance is optimal\n"
            report += "• Continue regular monitoring\n"
        
        return report


class EnterpriseNetworkManager:
    """Gestione rete enterprise"""

    def __init__(self, log_manager):
        self.log_manager = log_manager

    def network_security_scan(self) -> Dict[str, Any]:
        """Scansione sicurezza rete"""
        results = {
            "timestamp": datetime.now().isoformat(),
            "open_ports": [],
            "active_connections": [],
            "network_interfaces": [],
            "security_issues": []
        }

        try:
            # Scan open ports
            connections = psutil.net_connections(kind='inet')
            listening_ports = []

            for conn in connections:
                if conn.status == 'LISTEN':
                    port_info = {
                        "port": conn.laddr.port,
                        "address": conn.laddr.ip,
                        "pid": conn.pid,
                        "process": None
                    }

                    # Get process name
                    try:
                        if conn.pid:
                            process = psutil.Process(conn.pid)
                            port_info["process"] = process.name()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass

                    listening_ports.append(port_info)

            results["open_ports"] = listening_ports

            # Active connections
            active_connections = []
            for conn in connections:
                if conn.status == 'ESTABLISHED':
                    conn_info = {
                        "local": f"{conn.laddr.ip}:{conn.laddr.port}",
                        "remote": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A",
                        "status": conn.status,
                        "pid": conn.pid
                    }
                    active_connections.append(conn_info)

            results["active_connections"] = active_connections

            # Network interfaces
            interfaces = psutil.net_if_addrs()
            for interface, addrs in interfaces.items():
                interface_info = {
                    "name": interface,
                    "addresses": []
                }

                for addr in addrs:
                    interface_info["addresses"].append({
                        "family": str(addr.family),
                        "address": addr.address,
                        "netmask": addr.netmask,
                        "broadcast": addr.broadcast
                    })

                results["network_interfaces"].append(interface_info)

            # Security analysis
            dangerous_ports = [21, 23, 135, 139, 445, 1433, 3389, 5900]
            for port_info in listening_ports:
                if port_info["port"] in dangerous_ports:
                    results["security_issues"].append({
                        "type": "dangerous_port",
                        "description": f"Potentially dangerous port {port_info['port']} is open",
                        "severity": "high",
                        "recommendation": f"Review necessity of port {port_info['port']}"
                    })

        except Exception as e:
            self.log_manager.log_error(f"Network security scan error: {e}")
            results["error"] = str(e)

        return results

    def bandwidth_monitor(self, duration: int = 10) -> Dict[str, Any]:
        """Monitor bandwidth per durata specificata"""
        try:
            # Initial measurement
            initial_stats = psutil.net_io_counters()
            time.sleep(duration)
            final_stats = psutil.net_io_counters()

            # Calculate rates
            bytes_sent_rate = (final_stats.bytes_sent - initial_stats.bytes_sent) / duration
            bytes_recv_rate = (final_stats.bytes_recv - initial_stats.bytes_recv) / duration

            return {
                "duration": duration,
                "upload_rate_mbps": (bytes_sent_rate * 8) / (1024 * 1024),
                "download_rate_mbps": (bytes_recv_rate * 8) / (1024 * 1024),
                "total_sent_gb": final_stats.bytes_sent / (1024**3),
                "total_recv_gb": final_stats.bytes_recv / (1024**3),
                "packets_sent": final_stats.packets_sent,
                "packets_recv": final_stats.packets_recv,
                "errors_in": final_stats.errin,
                "errors_out": final_stats.errout,
                "drops_in": final_stats.dropin,
                "drops_out": final_stats.dropout
            }

        except Exception as e:
            self.log_manager.log_error(f"Bandwidth monitor error: {e}")
            return {"error": str(e)}
