#!/usr/bin/env python3
"""
Cybex - Cybernetic Expert Agent
Main entry point for the application

Usage:
    python main.py [options]
    
Options:
    --mode [chat|agent]     Set operating mode
    --config PATH           Path to configuration file
    --help                  Show this help message
"""

import sys
import argparse
from pathlib import Path

# Add cybex package to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from cybex.interfaces.ui_cli import CybexCLI
from cybex import __version__


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Cybex - Cybernetic Expert Agent",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    python main.py                    # Start in default mode
    python main.py --mode agent       # Start in agent mode
    python main.py --config custom.yaml  # Use custom config
        """
    )
    
    parser.add_argument(
        '--version',
        action='version',
        version=f'Cybex {__version__}'
    )
    
    parser.add_argument(
        '--mode',
        choices=['chat', 'agent'],
        help='Set operating mode (default: chat)'
    )
    
    parser.add_argument(
        '--config',
        type=str,
        help='Path to configuration file'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='Enable debug logging'
    )
    
    parser.add_argument(
        '--no-color',
        action='store_true',
        help='Disable colored output'
    )
    
    return parser.parse_args()


def check_requirements():
    """Check if all requirements are met"""
    try:
        import yaml
        import requests
        import psutil
        import colorama
    except ImportError as e:
        print(f"Error: Missing required dependency: {e}")
        print("Please install requirements with: pip install -r requirements.txt")
        return False
    
    return True


def check_ollama_server():
    """Check if Ollama server is running"""
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        return response.status_code == 200
    except:
        return False


def main():
    """Main entry point"""
    # Parse arguments
    args = parse_arguments()
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Check Ollama server
    if not check_ollama_server():
        print("Warning: Ollama server not detected at localhost:11434")
        print("Please ensure Ollama is installed and running:")
        print("  1. Install Ollama from https://ollama.ai")
        print("  2. Run: ollama pull gemma2:7b")
        print("  3. Start Ollama service")
        print()
        
        response = input("Continue anyway? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            sys.exit(1)
    
    try:
        # Initialize and start CLI
        cli = CybexCLI()
        
        # Apply command line arguments
        if args.mode:
            cli.core.switch_mode(args.mode)
        
        if args.config:
            # TODO: Implement custom config loading
            print(f"Custom config not yet implemented: {args.config}")
        
        if args.debug:
            # TODO: Implement debug mode
            print("Debug mode enabled")
        
        if args.no_color:
            # TODO: Implement no-color mode
            print("Color output disabled")
        
        # Start the CLI
        cli.start()
        
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
        sys.exit(0)
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
