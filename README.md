# Cybex - Cybernetic Expert Agent 🤖

![Python](https://img.shields.io/badge/python-3.11+-blue.svg)
![License](https://img.shields.io/badge/license-MIT-green.svg)
![Status](https://img.shields.io/badge/status-development-orange.svg)

Cybex è un agente AI locale basato su Gemma 3.3-7B Turbo via Ollama, che emula un esperto con 40+ anni di esperienza in sicurezza informatica, ingegneria dei sistemi, sviluppo software e gestione professionale dei dischi.

## 🚀 Caratteristiche Principali

- **Modalità Chat**: Assistenza interattiva con conferme di sicurezza
- **Modalità Agente**: Esecuzione autonoma di piani multi-step
- **Sicurezza Integrata**: Controlli di sicurezza e conferme per operazioni critiche
- **Cross-Platform**: Supporto per Windows e Linux
- **Logging Avanzato**: Tracciamento completo delle operazioni
- **Configurazione Flessibile**: Personalizzazione comportamenti e policy

## 📋 Fasi di Sviluppo

### MVP (Minimum Viable Product) ✅
- [x] Struttura progetto base
- [x] Modalità chat classica
- [x] Comandi sistema essenziali
- [x] Sicurezza base e logging
- [ ] Interfaccia CLI funzionante

### SVP (Strategic Viable Product) 🚧
- [ ] Modalità agente autonoma
- [ ] Monitoraggio sistema avanzato
- [ ] Gestione dischi e SMART check
- [ ] Scripting PowerShell/Bash
- [ ] Sistema di persistenza

### Enterprise 🔮
- [ ] Modalità GhostOps
- [ ] Integrazione VirusTotal
- [ ] Backup cloud criptato
- [ ] GUI avanzata
- [ ] Sistema plugin

## 🛠️ Installazione

### Prerequisiti
- Python 3.11+
- Ollama installato e configurato
- Modello Gemma 3.3-7B Turbo

### Setup
```bash
# Clone del repository
git clone <repository-url>
cd cybex

# Creazione ambiente virtuale
python -m venv venv
source venv/bin/activate  # Linux/Mac
# oppure
venv\Scripts\activate     # Windows

# Installazione dipendenze
pip install -r requirements.txt

# Configurazione iniziale
cp cybex/config/cybex_config.yaml.example cybex/config/cybex_config.yaml
```

## 🎯 Utilizzo

### Avvio Base
```bash
python main.py
```

### Modalità Disponibili
```bash
# Modalità chat (default)
python main.py --mode chat

# Modalità agente
python main.py --mode agent

# Controllo stato sistema
python main.py --status

# Visualizza report
python main.py --report
```

## 🔧 Configurazione

Modifica `cybex/config/cybex_config.yaml` per personalizzare:
- Livello di conferma operazioni
- Comandi critici bloccati
- Configurazione Ollama
- Impostazioni logging
- Policy di sicurezza

## 🛡️ Sicurezza

Cybex implementa multiple misure di sicurezza:
- **Conferme obbligatorie** per operazioni distruttive
- **Sandbox mode** per limitare l'accesso al sistema
- **Whitelist/Blacklist** comandi e directory
- **Logging completo** di tutte le operazioni
- **Validazione input** per prevenire injection

## 📁 Struttura Progetto

```
cybex/
├── core/                 # Logica principale
├── modules/             # Moduli specializzati
├── interfaces/          # UI CLI/GUI
├── config/             # File di configurazione
├── logs/               # File di log
├── tests/              # Test suite
├── docs/               # Documentazione
└── main.py             # Entry point
```

## 🤝 Contributi

I contributi sono benvenuti! Per favore:
1. Fork del repository
2. Crea un branch per la feature
3. Commit delle modifiche
4. Push al branch
5. Apri una Pull Request

## 📄 Licenza

Questo progetto è rilasciato sotto licenza MIT. Vedi il file `LICENSE` per i dettagli.

## ⚠️ Disclaimer

Cybex è progettato per uso legale e professionale. Gli utenti sono responsabili dell'uso appropriato del software nel rispetto delle leggi locali e delle policy aziendali.

---

**Sviluppato con ❤️ per la community degli amministratori di sistema**
