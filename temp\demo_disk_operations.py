#!/usr/bin/env python3
"""
Demo Disk Operations
Dimostrazione delle capacità di scansione disco e pulizia
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def demo_disk_scan():
    """Demo scansione disco"""
    print("🎯 DEMO: 'Scansiona disco C'")
    print("=" * 50)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        print("🤖 Agente: Eseguo scansione completa del disco C...")
        print("⚡ Comando: scan_disk(drive='C', scan_type='all')")
        print()
        
        # Execute comprehensive disk scan
        execution_id = agent_tools.execute_tool(
            "scan_disk",
            {"drive": "C", "scan_type": "all", "depth": 2}
        )
        
        print("⏳ Scansione in corso...")
        
        # Wait and show progress
        import time
        for i in range(10):
            time.sleep(1)
            print(f"⏳ Scansione... {i+1}/10")
            
            execution = agent_tools.get_execution_status(execution_id)
            if execution and execution.status.value in ['completed', 'failed']:
                break
        
        # Get results
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print("\n🎉 Scansione completata!")
                print("📊 RISULTATI:")
                print("-" * 40)
                print(result.output)
                
                return True
            else:
                print(f"\n❌ Errore durante la scansione: {result.error}")
                return False
        else:
            print("\n⚠️  Scansione non completata nei tempi previsti")
            return False
        
    except Exception as e:
        print(f"\n❌ Errore: {e}")
        return False


def demo_temp_files_analysis():
    """Demo analisi file temporanei"""
    print(f"\n🎯 DEMO: 'Mostrami i file temporanei'")
    print("=" * 50)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        print("🤖 Agente: Analizzo i file temporanei del sistema...")
        print("⚡ Comando: scan_disk(drive='C', scan_type='temp_files')")
        print()
        
        # Execute temp files scan
        execution_id = agent_tools.execute_tool(
            "scan_disk",
            {"drive": "C", "scan_type": "temp_files"}
        )
        
        print("⏳ Analisi in corso...")
        
        # Wait for completion
        import time
        time.sleep(5)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print("\n📊 ANALISI FILE TEMPORANEI:")
                print("-" * 40)
                
                # Show formatted results
                lines = result.output.split('\n')
                for line in lines:
                    if line.strip():
                        print(line)
                
                return True
            else:
                print(f"\n❌ Errore durante l'analisi: {result.error}")
                return False
        else:
            print("\n⚠️  Analisi non completata")
            return False
        
    except Exception as e:
        print(f"\n❌ Errore: {e}")
        return False


def demo_cleanup_preview():
    """Demo preview pulizia"""
    print(f"\n🎯 DEMO: 'Elimina file temp dal disco C' (PREVIEW)")
    print("=" * 60)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        print("🤖 Agente: Prima mostro cosa verrebbe eliminato (modalità sicura)...")
        print("⚡ Comando: cleanup_temp_files(dry_run=True)")
        print("🔒 SICUREZZA: Modalità preview attiva - nessun file verrà eliminato")
        print()
        
        # Execute cleanup preview
        execution_id = agent_tools.execute_tool(
            "cleanup_temp_files",
            {"cleanup_type": "temp", "dry_run": True, "drive": "C"}
        )
        
        print("⏳ Analisi pulizia in corso...")
        
        # Wait for completion
        import time
        time.sleep(5)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print("\n🧹 PREVIEW PULIZIA:")
                print("-" * 40)
                
                # Show preview results
                lines = result.output.split('\n')
                for line in lines:
                    if line.strip():
                        print(line)
                
                print("\n" + "="*60)
                print("💡 PROSSIMI PASSI:")
                print("   1. Rivedi i file che verrebbero eliminati")
                print("   2. Se sei sicuro, usa: dry_run=false")
                print("   3. ATTENZIONE: L'eliminazione è irreversibile!")
                
                return True
            else:
                print(f"\n❌ Errore durante il preview: {result.error}")
                return False
        else:
            print("\n⚠️  Preview non completato")
            return False
        
    except Exception as e:
        print(f"\n❌ Errore: {e}")
        return False


def show_agent_capabilities():
    """Mostra le capacità dell'agente"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    capabilities = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                    {SunriseColors.ACCENT}🤖 CAPACITÀ DELL'AGENTE CYBEX{SunriseColors.SUCCESS}                              ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}✅ COSA PUÒ FARE ORA:{SunriseColors.SUCCESS}                                                          ║
║                                                                               ║
║ {SunriseColors.INFO}💬 "Scansiona disco C"{SunriseColors.SUCCESS}                                                         ║
║   → Analisi completa: spazio, file grandi, temp, salute disco                ║
║   → Mostra utilizzo dettagliato e raccomandazioni                            ║
║                                                                               ║
║ {SunriseColors.INFO}💬 "Mostrami i file temporanei"{SunriseColors.SUCCESS}                                                ║
║   → Scansiona tutte le cartelle temp del sistema                             ║
║   → Calcola spazio occupato e numero file                                    ║
║   → Identifica cartelle temp di utenti e sistema                             ║
║                                                                               ║
║ {SunriseColors.INFO}💬 "Elimina file temp dal disco C"{SunriseColors.SUCCESS}                                             ║
║   → SEMPRE preview prima (modalità sicura)                                   ║
║   → Mostra cosa verrebbe eliminato                                           ║
║   → Richiede conferma esplicita per eliminazione reale                       ║
║   → Salta file in uso, di sistema, e recenti                                 ║
║                                                                               ║
║ {SunriseColors.INFO}💬 "Trova file grandi sul disco"{SunriseColors.SUCCESS}                                               ║
║   → Identifica file >100MB                                                   ║
║   → Ordina per dimensione                                                    ║
║   → Mostra percorsi completi                                                 ║
║                                                                               ║
║ {SunriseColors.INFO}💬 "Pulisci cache browser"{SunriseColors.SUCCESS}                                                     ║
║   → Chrome, Firefox, Edge cache                                              ║
║   → Preview sicuro prima dell'eliminazione                                   ║
║                                                                               ║
║ {SunriseColors.ACCENT}🔒 SICUREZZA INTEGRATA:{SunriseColors.SUCCESS}                                                       ║
║   • Preview obbligatorio per eliminazioni                                    ║
║   • Protezione file di sistema                                               ║
║   • Skip file in uso o protetti                                              ║
║   • Logging completo di tutte le operazioni                                  ║
║                                                                               ║
║ {SunriseColors.ACCENT}⚡ COMANDI DIRETTI:{SunriseColors.SUCCESS}                                                           ║
║   • /tool scan_disk drive=C scan_type=usage                                  ║
║   • /tool scan_disk drive=C scan_type=temp_files                             ║
║   • /tool cleanup_temp_files dry_run=true                                    ║
║   • /ops - Mostra operazioni in tempo reale                                  ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(capabilities)


def main():
    """Run disk operations demo"""
    print("🎯 Cybex Enterprise - Demo Operazioni Disco")
    print("=" * 60)
    print("Dimostrazione delle nuove capacità dell'agente")
    print()
    
    demos = [
        ("Scansione Disco", demo_disk_scan),
        ("Analisi File Temporanei", demo_temp_files_analysis),
        ("Preview Pulizia", demo_cleanup_preview)
    ]
    
    successful_demos = 0
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                successful_demos += 1
                print(f"\n✅ {demo_name}: COMPLETATO")
            else:
                print(f"\n❌ {demo_name}: FALLITO")
        except Exception as e:
            print(f"\n❌ {demo_name}: ERRORE - {e}")
        
        print("\n" + "="*60)
    
    print(f"\n🎯 Demo Results: {successful_demos}/{len(demos)} completate con successo")
    
    if successful_demos >= 2:
        show_agent_capabilities()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 L'agente è pronto per le operazioni disco!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Ora puoi chiedere all'agente di scansionare e pulire il disco in sicurezza!{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Prova ora:{SunriseColors.RESET}")
        print(f"  1. python main_enterprise.py")
        print(f"  2. Scrivi: 'Scansiona disco C'")
        print(f"  3. Oppure: 'Elimina file temp dal disco C'")
        print(f"  4. Usa /ops per vedere le operazioni in tempo reale")
    else:
        print(f"⚠️  Alcune demo sono fallite. L'agente potrebbe avere funzionalità limitate.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
