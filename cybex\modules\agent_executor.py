"""
Agent Executor Module
Autonomous command execution with progress tracking and error recovery
"""

import time
import asyncio
import threading
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime
from enum import Enum

from .agent_planner import AgentPlan, AgentTask, TaskStatus
from .command_executor import CommandExecutor


class ExecutionMode(Enum):
    """Execution modes for agent"""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    ADAPTIVE = "adaptive"


class ExecutionResult:
    """Result of task execution"""
    def __init__(self, task_id: str, success: bool, output: str, 
                 execution_time: float, error: Optional[str] = None):
        self.task_id = task_id
        self.success = success
        self.output = output
        self.execution_time = execution_time
        self.error = error
        self.timestamp = datetime.now()


class AgentExecutor:
    """
    Autonomous execution engine for agent plans
    """
    
    def __init__(self, config_manager, security_manager, log_manager, 
                 command_executor: CommandExecutor, agent_planner):
        """Initialize agent executor"""
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.log_manager = log_manager
        self.command_executor = command_executor
        self.agent_planner = agent_planner
        self.logger = log_manager.get_logger(__name__)
        
        # Execution configuration
        self.agent_config = config_manager.get_section('agent')
        self.max_parallel_tasks = self.agent_config.get('max_parallel_tasks', 3)
        self.execution_mode = ExecutionMode(self.agent_config.get('execution_mode', 'sequential'))
        self.auto_retry = self.agent_config.get('auto_retry', True)
        self.rollback_on_failure = self.agent_config.get('rollback_on_failure', True)
        
        # Execution state
        self.active_executions: Dict[str, Dict] = {}
        self.execution_callbacks: Dict[str, List[Callable]] = {}
        self.execution_lock = threading.Lock()
        
        # Performance metrics
        self.execution_metrics: Dict[str, Any] = {
            'total_executions': 0,
            'successful_executions': 0,
            'failed_executions': 0,
            'average_execution_time': 0.0,
            'total_execution_time': 0.0
        }
    
    def execute_plan(self, plan: AgentPlan, 
                    progress_callback: Optional[Callable] = None,
                    confirmation_callback: Optional[Callable] = None) -> bool:
        """
        Execute an agent plan autonomously
        """
        self.logger.info(f"Starting execution of plan: {plan.name}")
        
        # Check if confirmation is required
        if plan.requires_confirmation and confirmation_callback:
            if not confirmation_callback(plan):
                self.logger.info("Plan execution cancelled by user")
                plan.status = TaskStatus.CANCELLED
                return False
        
        # Initialize execution state
        execution_id = plan.id
        self.active_executions[execution_id] = {
            'plan': plan,
            'start_time': time.time(),
            'current_task': None,
            'completed_tasks': 0,
            'failed_tasks': 0,
            'progress_callback': progress_callback
        }
        
        try:
            # Update plan status
            plan.status = TaskStatus.IN_PROGRESS
            
            # Execute based on mode
            if self.execution_mode == ExecutionMode.SEQUENTIAL:
                success = self._execute_sequential(plan, execution_id)
            elif self.execution_mode == ExecutionMode.PARALLEL:
                success = self._execute_parallel(plan, execution_id)
            else:  # ADAPTIVE
                success = self._execute_adaptive(plan, execution_id)
            
            # Update final status
            if success:
                plan.status = TaskStatus.COMPLETED
                self.logger.info(f"Plan '{plan.name}' completed successfully")
            else:
                plan.status = TaskStatus.FAILED
                self.logger.error(f"Plan '{plan.name}' failed")
                
                # Attempt rollback if enabled
                if self.rollback_on_failure:
                    self._execute_rollback(plan)
            
            # Update metrics
            self._update_execution_metrics(execution_id, success)
            
            return success
            
        except Exception as e:
            self.logger.error(f"Plan execution error: {e}")
            plan.status = TaskStatus.FAILED
            return False
        finally:
            # Cleanup execution state
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]
    
    def _execute_sequential(self, plan: AgentPlan, execution_id: str) -> bool:
        """Execute tasks sequentially"""
        execution_state = self.active_executions[execution_id]
        
        for task in plan.tasks:
            if plan.status == TaskStatus.CANCELLED:
                break
            
            # Check dependencies
            if not self._check_task_dependencies(task, plan.tasks):
                self.logger.warning(f"Task '{task.name}' dependencies not met, skipping")
                task.status = TaskStatus.SKIPPED
                continue
            
            # Execute task
            execution_state['current_task'] = task
            result = self._execute_task(task)
            
            # Update progress
            if execution_state['progress_callback']:
                progress = (execution_state['completed_tasks'] + 1) / len(plan.tasks) * 100
                execution_state['progress_callback'](progress, task, result)
            
            if result.success:
                execution_state['completed_tasks'] += 1
                self.log_manager.log_agent_action(
                    execution_state['completed_tasks'], 
                    task.name, 
                    result.output, 
                    True
                )
            else:
                execution_state['failed_tasks'] += 1
                self.log_manager.log_agent_action(
                    execution_state['completed_tasks'] + execution_state['failed_tasks'], 
                    task.name, 
                    result.error or result.output, 
                    False
                )
                
                # Check if we should continue on failure
                if not self._should_continue_on_failure(task, plan):
                    return False
        
        return execution_state['failed_tasks'] == 0
    
    def _execute_parallel(self, plan: AgentPlan, execution_id: str) -> bool:
        """Execute tasks in parallel where possible"""
        # This is a simplified parallel execution
        # In a full implementation, this would use proper dependency resolution
        
        execution_state = self.active_executions[execution_id]
        ready_tasks = [task for task in plan.tasks if not task.dependencies]
        completed_tasks = set()
        
        while ready_tasks and plan.status != TaskStatus.CANCELLED:
            # Execute batch of ready tasks
            batch_size = min(len(ready_tasks), self.max_parallel_tasks)
            current_batch = ready_tasks[:batch_size]
            ready_tasks = ready_tasks[batch_size:]
            
            # Execute batch
            batch_results = []
            for task in current_batch:
                result = self._execute_task(task)
                batch_results.append((task, result))
                
                if result.success:
                    completed_tasks.add(task.id)
                    execution_state['completed_tasks'] += 1
                else:
                    execution_state['failed_tasks'] += 1
            
            # Check for newly ready tasks
            for task in plan.tasks:
                if (task.status == TaskStatus.PENDING and 
                    task not in ready_tasks and 
                    task not in current_batch and
                    all(dep_id in completed_tasks for dep_id in task.dependencies)):
                    ready_tasks.append(task)
        
        return execution_state['failed_tasks'] == 0
    
    def _execute_adaptive(self, plan: AgentPlan, execution_id: str) -> bool:
        """Execute with adaptive strategy based on task characteristics"""
        # Start with sequential, switch to parallel for safe tasks
        critical_tasks = [t for t in plan.tasks if t.priority.value >= 3]
        
        if critical_tasks:
            # Execute critical tasks sequentially first
            for task in critical_tasks:
                if task in plan.tasks:
                    plan.tasks.remove(task)
                    plan.tasks.insert(0, task)
            
            return self._execute_sequential(plan, execution_id)
        else:
            return self._execute_parallel(plan, execution_id)
    
    def _execute_task(self, task: AgentTask) -> ExecutionResult:
        """Execute a single task"""
        self.logger.info(f"Executing task: {task.name}")
        
        task.status = TaskStatus.IN_PROGRESS
        task.started_at = datetime.now()
        
        start_time = time.time()
        
        try:
            # Skip placeholder commands
            if task.command.startswith('#'):
                self.logger.info(f"Skipping placeholder task: {task.name}")
                task.status = TaskStatus.SKIPPED
                return ExecutionResult(task.id, True, "Skipped placeholder task", 0.0)
            
            # Execute command with retries
            success, output, metadata = self._execute_with_retry(task)
            
            execution_time = time.time() - start_time
            task.execution_time = execution_time
            task.completed_at = datetime.now()
            
            if success:
                task.status = TaskStatus.COMPLETED
                task.output = output
                
                # Run validation commands if specified
                if task.validation_commands:
                    validation_success = self._run_validation_commands(task)
                    if not validation_success:
                        task.status = TaskStatus.FAILED
                        task.error_message = "Validation failed"
                        return ExecutionResult(task.id, False, output, execution_time, "Validation failed")
                
                return ExecutionResult(task.id, True, output, execution_time)
            else:
                task.status = TaskStatus.FAILED
                task.error_message = output
                return ExecutionResult(task.id, False, output, execution_time, output)
                
        except Exception as e:
            execution_time = time.time() - start_time
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.execution_time = execution_time
            task.completed_at = datetime.now()
            
            self.logger.error(f"Task execution error: {e}")
            return ExecutionResult(task.id, False, "", execution_time, str(e))
    
    def _execute_with_retry(self, task: AgentTask) -> tuple:
        """Execute command with retry logic"""
        last_error = None
        
        for attempt in range(task.retry_count + 1):
            try:
                if attempt > 0:
                    self.logger.info(f"Retrying task '{task.name}' (attempt {attempt + 1})")
                    time.sleep(2 ** attempt)  # Exponential backoff
                
                success, output, metadata = self.command_executor.execute_command(
                    task.command,
                    timeout=task.timeout,
                    confirm_required=False  # Agent mode doesn't require confirmation
                )
                
                if success:
                    return success, output, metadata
                else:
                    last_error = output
                    
            except Exception as e:
                last_error = str(e)
                self.logger.warning(f"Task execution attempt {attempt + 1} failed: {e}")
        
        return False, last_error or "All retry attempts failed", {}
    
    def _run_validation_commands(self, task: AgentTask) -> bool:
        """Run validation commands for a task"""
        for validation_cmd in task.validation_commands:
            try:
                success, output, _ = self.command_executor.execute_command(
                    validation_cmd,
                    timeout=30,
                    confirm_required=False
                )
                
                if not success:
                    self.logger.warning(f"Validation failed for task '{task.name}': {output}")
                    return False
                    
            except Exception as e:
                self.logger.error(f"Validation error for task '{task.name}': {e}")
                return False
        
        return True
    
    def _check_task_dependencies(self, task: AgentTask, all_tasks: List[AgentTask]) -> bool:
        """Check if task dependencies are satisfied"""
        if not task.dependencies:
            return True
        
        task_dict = {t.id: t for t in all_tasks}
        
        for dep_id in task.dependencies:
            if dep_id not in task_dict:
                return False
            
            dep_task = task_dict[dep_id]
            if dep_task.status != TaskStatus.COMPLETED:
                return False
        
        return True
    
    def _should_continue_on_failure(self, failed_task: AgentTask, plan: AgentPlan) -> bool:
        """Determine if execution should continue after task failure"""
        # Stop on critical task failure
        if failed_task.priority == failed_task.priority.CRITICAL:
            return False
        
        # Check plan-level failure tolerance
        failed_count = sum(1 for t in plan.tasks if t.status == TaskStatus.FAILED)
        total_count = len(plan.tasks)
        
        failure_rate = failed_count / total_count
        max_failure_rate = self.agent_config.get('max_failure_rate', 0.3)
        
        return failure_rate <= max_failure_rate
    
    def _execute_rollback(self, plan: AgentPlan) -> bool:
        """Execute rollback plan"""
        self.logger.info(f"Executing rollback for plan: {plan.name}")
        
        try:
            # Execute plan-level rollback commands
            for rollback_cmd in plan.rollback_plan:
                success, output, _ = self.command_executor.execute_command(
                    rollback_cmd,
                    timeout=60,
                    confirm_required=False
                )
                
                if not success:
                    self.logger.error(f"Rollback command failed: {rollback_cmd}")
            
            # Execute task-level rollback commands (in reverse order)
            completed_tasks = [t for t in reversed(plan.tasks) if t.status == TaskStatus.COMPLETED]
            
            for task in completed_tasks:
                for rollback_cmd in task.rollback_commands:
                    success, output, _ = self.command_executor.execute_command(
                        rollback_cmd,
                        timeout=60,
                        confirm_required=False
                    )
                    
                    if not success:
                        self.logger.error(f"Task rollback failed for '{task.name}': {rollback_cmd}")
            
            self.logger.info("Rollback completed")
            return True
            
        except Exception as e:
            self.logger.error(f"Rollback execution error: {e}")
            return False
    
    def _update_execution_metrics(self, execution_id: str, success: bool) -> None:
        """Update execution performance metrics"""
        execution_state = self.active_executions.get(execution_id)
        if not execution_state:
            return
        
        execution_time = time.time() - execution_state['start_time']
        
        self.execution_metrics['total_executions'] += 1
        self.execution_metrics['total_execution_time'] += execution_time
        
        if success:
            self.execution_metrics['successful_executions'] += 1
        else:
            self.execution_metrics['failed_executions'] += 1
        
        # Update average
        self.execution_metrics['average_execution_time'] = (
            self.execution_metrics['total_execution_time'] / 
            self.execution_metrics['total_executions']
        )
    
    def get_execution_status(self, plan_id: str) -> Optional[Dict]:
        """Get current execution status"""
        return self.active_executions.get(plan_id)
    
    def cancel_execution(self, plan_id: str) -> bool:
        """Cancel ongoing execution"""
        if plan_id in self.active_executions:
            execution_state = self.active_executions[plan_id]
            plan = execution_state['plan']
            plan.status = TaskStatus.CANCELLED
            
            # Cancel current task
            current_task = execution_state.get('current_task')
            if current_task:
                current_task.status = TaskStatus.CANCELLED
            
            self.logger.info(f"Execution cancelled for plan: {plan.name}")
            return True
        
        return False
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get execution performance metrics"""
        return self.execution_metrics.copy()
