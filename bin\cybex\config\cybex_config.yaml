agent:
  auto_confirm_safe: false
  max_steps: 10
  plan_review_required: true
  step_timeout: 60
confirm_level: strict
critical_ops:
- rm
- del
- format
- fdisk
- apt remove
- yum remove
- net stop
- systemctl stop
- reg delete
- diskpart
enterprise:
  auto_patching: false
  cloud_backup_enabled: false
  ghostops_mode: false
  plugin_system: false
  virustotal_api_key: ''
logging:
  backup_count: 5
  console_output: true
  file_path: logs/cybex.log
  level: INFO
  max_file_size: 10MB
mode: chat
ollama:
  host: localhost
  max_tokens: 3000
  model: gemma2:7b
  port: 11434
  timeout: 30
security:
  allowed_directories:
  - /home
  - /tmp
  - C:\Users
  - C:\Temp
  blocked_commands:
  - sudo rm -rf /
  - 'format c:'
  - del /f /s /q c:\
  enable_sandbox: true
  max_command_length: 1000
system: auto
ui:
  auto_save_session: true
  command_history_size: 100
  show_timestamps: true
  theme: dark
