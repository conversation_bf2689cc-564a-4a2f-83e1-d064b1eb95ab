#!/usr/bin/env python3
"""
Quick Enterprise Test
Test rapido per verificare i tool enterprise
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def main():
    print("🏢 Quick Enterprise Test")
    print("=" * 30)
    
    try:
        # Test import
        print("📦 Testing imports...")
        from cybex.modules.enterprise_integration import EnterpriseTools
        print("✅ EnterpriseTools imported")
        
        from cybex.modules.agent_tools import AgentTools
        print("✅ AgentTools imported")
        
        # Test AgentTools initialization
        print("\n🔧 Testing AgentTools...")
        agent_tools = AgentTools()
        print("✅ AgentTools initialized")
        
        # Get tools
        all_tools = agent_tools.get_available_tools()
        print(f"✅ Total tools: {len(all_tools)}")
        
        # Check enterprise tools
        enterprise_tools = [
            "security_audit", "performance_analysis", "network_security_scan",
            "enterprise_health_check", "system_hardening", "backup_analysis"
        ]
        
        found = 0
        for tool in enterprise_tools:
            if tool in all_tools:
                found += 1
                print(f"  ✅ {tool}")
            else:
                print(f"  ❌ {tool}")
        
        print(f"\n🎯 Enterprise tools: {found}/6")
        
        if found >= 6:
            print("🎉 ALL ENTERPRISE TOOLS AVAILABLE!")
            print("🏢 cybex_futuristic.bat is ENTERPRISE READY!")
            return True
        else:
            print("❌ Some enterprise tools missing")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n💼 ENTERPRISE VERSION: OPERATIONAL! 🏢")
    else:
        print("\n❌ ENTERPRISE VERSION: NEEDS FIXES")
