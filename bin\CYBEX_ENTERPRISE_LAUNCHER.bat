@echo off
title CYBEX ENTERPRISE - AI AGENT LAUNCHER
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗███╗   ██╗████████╗  ║
echo ║   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝████╗  ██║╚══██╔══╝  ║
echo ║   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██╔██╗ ██║   ██║     ║
echo ║   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║╚██╗██║   ██║     ║
echo ║   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ███████╗██║ ╚████║   ██║     ║
echo ║    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝   ╚═╝     ║
echo ║                                                                              ║
echo ║                    🤖 ENTERPRISE AI AGENT WITH OLLAMA 🤖                    ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if we're in the correct directory
if not exist "bin\main.py" (
    echo ❌ Error: bin\main.py not found
    echo 💡 Make sure you're running this from the CYBEX root directory
    echo.
    pause
    exit /b 1
)

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo 📥 Download from: https://python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check Ollama server
echo 🔍 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not running
    echo.
    echo 🔧 OLLAMA SETUP REQUIRED:
    echo 1. Install Ollama: https://ollama.ai/
    echo 2. Start server: ollama serve
    echo 3. Download model: ollama pull llama2
    echo.
    set /p continue="Continue anyway? (y/N): "
    if /i not "%continue%"=="y" if /i not "%continue%"=="yes" (
        echo.
        echo 💡 Setup Ollama first, then run this launcher again
        pause
        exit /b 1
    )
) else (
    echo ✅ Ollama server is running
)

echo.
echo 🚀 Starting CYBEX Enterprise...
echo 💡 Loading AI agent with Ollama integration...
echo.

REM Launch CYBEX Enterprise
python bin\main.py

REM Handle exit
echo.
if errorlevel 1 (
    echo ❌ CYBEX Enterprise exited with error
    echo 💡 Check the error messages above
) else (
    echo 👋 CYBEX Enterprise session ended normally
)

echo.
echo 📖 TROUBLESHOOTING:
echo • If Ollama errors: Run 'ollama serve' in another terminal
echo • If import errors: Run 'pip install -r requirements.txt'
echo • If web errors: Run 'pip install -r requirements_web.txt'
echo • If Excel errors: Run 'pip install -r requirements_excel.txt'
echo.
pause
