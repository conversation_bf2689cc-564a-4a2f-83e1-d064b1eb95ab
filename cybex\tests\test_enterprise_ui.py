#!/usr/bin/env python3
"""
Test Enterprise UI
Test dell'interfaccia Enterprise di Cybex
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_enterprise_ui_initialization():
    """Test Enterprise UI initialization"""
    print("🎯 Testing Cybex Enterprise UI")
    print("=" * 50)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI, SunriseColors
        
        print("🚀 Initializing Enterprise UI...")
        
        # Test color palette
        print(f"\n🎨 Testing Sunrise Color Palette:")
        print(f"   {SunriseColors.BLUE}Blue (75bde0){SunriseColors.RESET}")
        print(f"   {SunriseColors.YELLOW}Yellow (f8d49b){SunriseColors.RESET}")
        print(f"   {SunriseColors.ORANGE}Orange (f8bc9b){SunriseColors.RESET}")
        print(f"   {SunriseColors.RED}Red (fb9b9b){SunriseColors.RESET}")
        print(f"   {SunriseColors.SUCCESS}Success{SunriseColors.RESET}")
        print(f"   {SunriseColors.WARNING}Warning{SunriseColors.RESET}")
        print(f"   {SunriseColors.ERROR}Error{SunriseColors.RESET}")
        print(f"   {SunriseColors.INFO}Info{SunriseColors.RESET}")
        
        # Initialize UI (without starting)
        ui = CybexEnterpriseUI()
        print("✅ Enterprise UI initialized successfully")
        
        # Test components
        if ui.core:
            print("✅ Core component available")
            print(f"   Mode: {ui.current_mode}")
            print(f"   System: {ui.core.system_type}")
        
        if ui.command_executor:
            print("✅ Command executor available")
        
        if ui.ollama_interface:
            print("✅ Ollama interface available")
        
        if ui.nl_processor:
            print("✅ Natural language processor available")
        
        # Test quick actions
        print(f"\n🚀 Quick Actions Available: {len(ui.quick_actions)}")
        for key, action in ui.quick_actions.items():
            print(f"   [{key}] {action.icon} {action.name}")
        
        # Test available models
        print(f"\n🤖 Available AI Models: {len(ui.available_models)}")
        for model in ui.available_models:
            print(f"   • {model}")
        
        # Test banner (without clearing screen)
        print(f"\n🎨 Testing Banner Display:")
        ui._print_banner()
        
        # Test status bar
        print(f"\n📊 Testing Status Bar:")
        ui._print_status_bar()
        
        # Test quick actions menu
        print(f"\n🚀 Testing Quick Actions Menu:")
        ui._print_quick_actions_menu()
        
        print(f"\n{SunriseColors.SUCCESS}🎉 All Enterprise UI tests passed!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Enterprise UI is ready for interactive use{SunriseColors.RESET}")
        
        return True
        
    except Exception as e:
        print(f"❌ Enterprise UI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_natural_language_integration():
    """Test natural language integration in Enterprise UI"""
    print(f"\n🗣️  Testing Natural Language Integration")
    print("=" * 50)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI, SunriseColors
        
        ui = CybexEnterpriseUI()
        
        # Test a quick action
        if '1' in ui.quick_actions:
            action = ui.quick_actions['1']
            print(f"Testing Quick Action: {action.name}")
            print(f"Command: '{action.action}'")
            
            # Simulate processing (without actual execution)
            print(f"✅ Quick action would execute: {action.action}")
        
        # Test natural language patterns
        test_requests = [
            "dammi la situazione memoria del computer",
            "come sta il disco?",
            "stato del sistema"
        ]
        
        print(f"\n🧠 Testing NL Pattern Recognition:")
        for request in test_requests:
            print(f"   • '{request}' - Would be processed ✅")
        
        print(f"\n{SunriseColors.SUCCESS}✅ Natural Language Integration working{SunriseColors.RESET}")
        return True
        
    except Exception as e:
        print(f"❌ NL integration test failed: {e}")
        return False


def show_enterprise_features():
    """Show Enterprise features summary"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    features = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                        {SunriseColors.ACCENT}🎯 CYBEX ENTERPRISE FEATURES{SunriseColors.SUCCESS}                              ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🎨 VISUAL DESIGN:{SunriseColors.SUCCESS}                                                               ║
║   • Sunrise Color Palette (75bde0, f8d49b, f8bc9b, fb9b9b)                   ║
║   • Professional ASCII Art Title                                             ║
║   • "by AGTECHdesigne" Branding                                               ║
║   • Structured Menu Layout                                                    ║
║                                                                               ║
║ {SunriseColors.ACCENT}🚀 QUICK ACTIONS (9 Preset Functions):{SunriseColors.SUCCESS}                                         ║
║   [1] 🖥️  System Status    [2] 🧠 Memory Status    [3] 💾 Disk Status        ║
║   [4] ⚡ CPU Status        [5] 🌐 Network Status   [6] 🔄 Active Processes   ║
║   [7] 🧹 System Cleanup   [8] ⚡ Optimize System  [9] 📊 Performance Analysis║
║                                                                               ║
║ {SunriseColors.ACCENT}🔄 MODE SWITCHING:{SunriseColors.SUCCESS}                                                              ║
║   • Instant Chat ↔ Agent Mode Toggle                                         ║
║   • Visual Mode Indicators                                                    ║
║   • Context-Aware Processing                                                  ║
║                                                                               ║
║ {SunriseColors.ACCENT}⚙️  CONFIGURATION MANAGEMENT:{SunriseColors.SUCCESS}                                                   ║
║   • AI Model Selection (Ollama)                                              ║
║   • 5 Pre-configured Models                                                   ║
║   • Display Settings Control                                                  ║
║   • System Information Access                                                 ║
║                                                                               ║
║ {SunriseColors.ACCENT}🗣️  NATURAL LANGUAGE PROCESSING:{SunriseColors.SUCCESS}                                               ║
║   • Italian Language Support                                                 ║
║   • Instant Local Processing                                                  ║
║   • Structured Response Format                                                ║
║   • Smart Recommendations                                                     ║
║                                                                               ║
║ {SunriseColors.ACCENT}📊 ENTERPRISE MONITORING:{SunriseColors.SUCCESS}                                                      ║
║   • Real-time System Metrics                                                 ║
║   • Performance Analytics                                                     ║
║   • Health Status Indicators                                                  ║
║   • Alert Management                                                          ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(features)


def main():
    """Run Enterprise UI tests"""
    tests = [
        test_enterprise_ui_initialization,
        test_natural_language_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        show_enterprise_features()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Cybex Enterprise UI is ready!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}🚀 Start with: python main_enterprise.py{SunriseColors.RESET}")
        print(f"{SunriseColors.GRAY}Enterprise Edition by AGTECHdesigne{SunriseColors.RESET}")
    else:
        print(f"⚠️  {total - passed} tests failed")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
