#!/usr/bin/env python3
"""
Development Tools for CYBEX Enterprise
Comprehensive development and programming assistance tools
"""

import os
import subprocess
import json
import ast
import re
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import git
import requests
import zipfile
import tempfile
import shutil


@dataclass
class CodeAnalysis:
    """Code analysis results"""
    file_path: str
    language: str
    lines_of_code: int
    complexity_score: int
    issues: List[Dict[str, Any]]
    suggestions: List[str]


class DevelopmentTools:
    """Comprehensive development tools"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Supported languages and their extensions
        self.language_extensions = {
            'python': ['.py', '.pyw'],
            'javascript': ['.js', '.jsx', '.mjs'],
            'typescript': ['.ts', '.tsx'],
            'html': ['.html', '.htm'],
            'css': ['.css', '.scss', '.sass', '.less'],
            'java': ['.java'],
            'c': ['.c', '.h'],
            'cpp': ['.cpp', '.cxx', '.cc', '.hpp', '.hxx'],
            'csharp': ['.cs'],
            'php': ['.php'],
            'ruby': ['.rb'],
            'go': ['.go'],
            'rust': ['.rs'],
            'sql': ['.sql'],
            'json': ['.json'],
            'xml': ['.xml'],
            'yaml': ['.yml', '.yaml'],
            'markdown': ['.md', '.markdown']
        }
    
    def create_project(self, project_name: str, project_type: str, 
                      base_path: str = ".", template: str = None) -> Dict[str, Any]:
        """Create new development project"""
        try:
            project_path = Path(base_path) / project_name
            
            if project_path.exists():
                return {'success': False, 'error': 'Project directory already exists'}
            
            project_path.mkdir(parents=True, exist_ok=True)
            
            # Create project structure based on type
            if project_type.lower() == 'python':
                return self._create_python_project(project_path, project_name, template)
            elif project_type.lower() == 'javascript':
                return self._create_javascript_project(project_path, project_name, template)
            elif project_type.lower() == 'react':
                return self._create_react_project(project_path, project_name, template)
            elif project_type.lower() == 'flask':
                return self._create_flask_project(project_path, project_name, template)
            elif project_type.lower() == 'django':
                return self._create_django_project(project_path, project_name, template)
            elif project_type.lower() == 'fastapi':
                return self._create_fastapi_project(project_path, project_name, template)
            else:
                return self._create_generic_project(project_path, project_name, project_type)
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_python_project(self, project_path: Path, project_name: str, template: str) -> Dict[str, Any]:
        """Create Python project structure"""
        try:
            # Create directories
            (project_path / project_name).mkdir()
            (project_path / 'tests').mkdir()
            (project_path / 'docs').mkdir()
            
            # Create files
            files_created = []
            
            # Main module
            main_file = project_path / project_name / '__init__.py'
            main_file.write_text('"""Main module for {}"""\n\n__version__ = "0.1.0"\n'.format(project_name))
            files_created.append(str(main_file))
            
            # Main script
            main_script = project_path / project_name / 'main.py'
            main_script.write_text('''#!/usr/bin/env python3
"""
Main script for {}
"""

def main():
    """Main function"""
    print("Hello from {}!")

if __name__ == "__main__":
    main()
'''.format(project_name, project_name))
            files_created.append(str(main_script))
            
            # Requirements
            requirements = project_path / 'requirements.txt'
            requirements.write_text('# Add your dependencies here\n')
            files_created.append(str(requirements))
            
            # Setup.py
            setup_py = project_path / 'setup.py'
            setup_py.write_text('''from setuptools import setup, find_packages

setup(
    name="{}",
    version="0.1.0",
    packages=find_packages(),
    install_requires=[],
    author="Your Name",
    author_email="<EMAIL>",
    description="A Python project",
    python_requires=">=3.7",
)
'''.format(project_name))
            files_created.append(str(setup_py))
            
            # README
            readme = project_path / 'README.md'
            readme.write_text('''# {}

## Description
A Python project created with CYBEX Enterprise.

## Installation
```bash
pip install -r requirements.txt
```

## Usage
```bash
python -m {}
```
'''.format(project_name, project_name))
            files_created.append(str(readme))
            
            # .gitignore
            gitignore = project_path / '.gitignore'
            gitignore.write_text('''__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
''')
            files_created.append(str(gitignore))
            
            return {
                'success': True,
                'project_path': str(project_path),
                'project_type': 'python',
                'files_created': files_created,
                'message': f'Python project {project_name} created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_javascript_project(self, project_path: Path, project_name: str, template: str) -> Dict[str, Any]:
        """Create JavaScript project structure"""
        try:
            # Create directories
            (project_path / 'src').mkdir()
            (project_path / 'tests').mkdir()
            (project_path / 'docs').mkdir()
            
            files_created = []
            
            # Package.json
            package_json = {
                "name": project_name,
                "version": "1.0.0",
                "description": "A JavaScript project created with CYBEX Enterprise",
                "main": "src/index.js",
                "scripts": {
                    "start": "node src/index.js",
                    "test": "echo \"Error: no test specified\" && exit 1"
                },
                "keywords": [],
                "author": "Your Name",
                "license": "MIT"
            }
            
            package_file = project_path / 'package.json'
            package_file.write_text(json.dumps(package_json, indent=2))
            files_created.append(str(package_file))
            
            # Main JavaScript file
            main_js = project_path / 'src' / 'index.js'
            main_js.write_text('''/**
 * Main entry point for {}
 */

console.log('Hello from {}!');

function main() {
    // Your code here
}

if (require.main === module) {
    main();
}

module.exports = { main };
'''.format(project_name, project_name))
            files_created.append(str(main_js))
            
            # README
            readme = project_path / 'README.md'
            readme.write_text('''# {}

## Description
A JavaScript project created with CYBEX Enterprise.

## Installation
```bash
npm install
```

## Usage
```bash
npm start
```
'''.format(project_name))
            files_created.append(str(readme))
            
            # .gitignore
            gitignore = project_path / '.gitignore'
            gitignore.write_text('''node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
dist/
build/
''')
            files_created.append(str(gitignore))
            
            return {
                'success': True,
                'project_path': str(project_path),
                'project_type': 'javascript',
                'files_created': files_created,
                'message': f'JavaScript project {project_name} created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_react_project(self, project_path: Path, project_name: str, template: str) -> Dict[str, Any]:
        """Create React project using create-react-app"""
        try:
            # Use create-react-app if available
            result = subprocess.run(
                ['npx', 'create-react-app', project_name],
                cwd=project_path.parent,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if result.returncode == 0:
                return {
                    'success': True,
                    'project_path': str(project_path),
                    'project_type': 'react',
                    'message': f'React project {project_name} created successfully',
                    'output': result.stdout
                }
            else:
                return {
                    'success': False,
                    'error': f'create-react-app failed: {result.stderr}'
                }
                
        except subprocess.TimeoutExpired:
            return {'success': False, 'error': 'Project creation timed out'}
        except FileNotFoundError:
            return {'success': False, 'error': 'npx/create-react-app not found'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_flask_project(self, project_path: Path, project_name: str, template: str) -> Dict[str, Any]:
        """Create Flask project structure"""
        try:
            # Create directories
            (project_path / 'app').mkdir()
            (project_path / 'app' / 'templates').mkdir()
            (project_path / 'app' / 'static').mkdir()
            (project_path / 'tests').mkdir()
            
            files_created = []
            
            # Main Flask app
            app_py = project_path / 'app' / '__init__.py'
            app_py.write_text('''from flask import Flask

def create_app():
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return f'Hello from {project_name}!'
    
    return app
'''.format(project_name=project_name))
            files_created.append(str(app_py))
            
            # Run script
            run_py = project_path / 'run.py'
            run_py.write_text('''#!/usr/bin/env python3
from app import create_app

app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
''')
            files_created.append(str(run_py))
            
            # Requirements
            requirements = project_path / 'requirements.txt'
            requirements.write_text('''Flask==2.3.3
python-dotenv==1.0.0
''')
            files_created.append(str(requirements))
            
            return {
                'success': True,
                'project_path': str(project_path),
                'project_type': 'flask',
                'files_created': files_created,
                'message': f'Flask project {project_name} created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_generic_project(self, project_path: Path, project_name: str, project_type: str) -> Dict[str, Any]:
        """Create generic project structure"""
        try:
            # Create basic directories
            (project_path / 'src').mkdir()
            (project_path / 'docs').mkdir()
            (project_path / 'tests').mkdir()
            
            files_created = []
            
            # README
            readme = project_path / 'README.md'
            readme.write_text(f'''# {project_name}

## Description
A {project_type} project created with CYBEX Enterprise.

## Getting Started
Add your project setup instructions here.
''')
            files_created.append(str(readme))
            
            # .gitignore
            gitignore = project_path / '.gitignore'
            gitignore.write_text('''# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Build files
build/
dist/
*.log
''')
            files_created.append(str(gitignore))
            
            return {
                'success': True,
                'project_path': str(project_path),
                'project_type': project_type,
                'files_created': files_created,
                'message': f'{project_type} project {project_name} created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def analyze_code(self, file_path: str) -> Dict[str, Any]:
        """Analyze code file for quality and issues"""
        try:
            path = Path(file_path)
            if not path.exists():
                return {'success': False, 'error': 'File not found'}
            
            # Detect language
            language = self._detect_language(path)
            
            # Read file content
            with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            analysis = {
                'file_path': str(path),
                'language': language,
                'file_size': len(content),
                'line_count': len(content.splitlines()),
                'character_count': len(content),
                'issues': [],
                'suggestions': [],
                'metrics': {}
            }
            
            # Language-specific analysis
            if language == 'python':
                python_analysis = self._analyze_python_code(content)
                analysis.update(python_analysis)
            elif language == 'javascript':
                js_analysis = self._analyze_javascript_code(content)
                analysis.update(js_analysis)
            
            # General code analysis
            general_analysis = self._analyze_general_code(content)
            analysis.update(general_analysis)
            
            return {'success': True, 'analysis': analysis}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _detect_language(self, file_path: Path) -> str:
        """Detect programming language from file extension"""
        extension = file_path.suffix.lower()
        
        for language, extensions in self.language_extensions.items():
            if extension in extensions:
                return language
        
        return 'unknown'
    
    def _analyze_python_code(self, content: str) -> Dict[str, Any]:
        """Analyze Python code"""
        try:
            analysis = {
                'syntax_valid': True,
                'imports': [],
                'functions': [],
                'classes': [],
                'complexity_score': 0
            }
            
            try:
                tree = ast.parse(content)
                
                # Extract imports, functions, classes
                for node in ast.walk(tree):
                    if isinstance(node, ast.Import):
                        for alias in node.names:
                            analysis['imports'].append(alias.name)
                    elif isinstance(node, ast.ImportFrom):
                        module = node.module or ''
                        for alias in node.names:
                            analysis['imports'].append(f"{module}.{alias.name}")
                    elif isinstance(node, ast.FunctionDef):
                        analysis['functions'].append({
                            'name': node.name,
                            'line': node.lineno,
                            'args': len(node.args.args)
                        })
                    elif isinstance(node, ast.ClassDef):
                        analysis['classes'].append({
                            'name': node.name,
                            'line': node.lineno
                        })
                
                # Calculate complexity (simplified)
                analysis['complexity_score'] = len(analysis['functions']) + len(analysis['classes']) * 2
                
            except SyntaxError as e:
                analysis['syntax_valid'] = False
                analysis['syntax_error'] = str(e)
            
            return analysis
            
        except Exception as e:
            return {'python_analysis_error': str(e)}
    
    def _analyze_javascript_code(self, content: str) -> Dict[str, Any]:
        """Analyze JavaScript code (basic)"""
        try:
            analysis = {
                'functions': [],
                'variables': [],
                'complexity_score': 0
            }
            
            # Simple regex-based analysis
            function_pattern = r'function\s+(\w+)\s*\('
            functions = re.findall(function_pattern, content)
            analysis['functions'] = functions
            
            # Count var/let/const declarations
            var_pattern = r'(?:var|let|const)\s+(\w+)'
            variables = re.findall(var_pattern, content)
            analysis['variables'] = variables
            
            analysis['complexity_score'] = len(functions) + len(variables)
            
            return analysis
            
        except Exception as e:
            return {'javascript_analysis_error': str(e)}
    
    def _analyze_general_code(self, content: str) -> Dict[str, Any]:
        """General code analysis"""
        try:
            lines = content.splitlines()
            
            analysis = {
                'blank_lines': 0,
                'comment_lines': 0,
                'code_lines': 0,
                'long_lines': 0,
                'max_line_length': 0
            }
            
            for line in lines:
                stripped = line.strip()
                
                if not stripped:
                    analysis['blank_lines'] += 1
                elif stripped.startswith('#') or stripped.startswith('//') or stripped.startswith('/*'):
                    analysis['comment_lines'] += 1
                else:
                    analysis['code_lines'] += 1
                
                line_length = len(line)
                if line_length > 80:
                    analysis['long_lines'] += 1
                
                analysis['max_line_length'] = max(analysis['max_line_length'], line_length)
            
            # Calculate ratios
            total_lines = len(lines)
            if total_lines > 0:
                analysis['comment_ratio'] = analysis['comment_lines'] / total_lines
                analysis['blank_ratio'] = analysis['blank_lines'] / total_lines
                analysis['code_ratio'] = analysis['code_lines'] / total_lines
            
            return analysis
            
        except Exception as e:
            return {'general_analysis_error': str(e)}
    
    def run_tests(self, project_path: str, test_framework: str = 'auto') -> Dict[str, Any]:
        """Run tests for the project"""
        try:
            project_dir = Path(project_path)
            if not project_dir.exists():
                return {'success': False, 'error': 'Project directory not found'}
            
            # Auto-detect test framework
            if test_framework == 'auto':
                test_framework = self._detect_test_framework(project_dir)
            
            if test_framework == 'pytest':
                return self._run_pytest(project_dir)
            elif test_framework == 'unittest':
                return self._run_unittest(project_dir)
            elif test_framework == 'jest':
                return self._run_jest(project_dir)
            elif test_framework == 'mocha':
                return self._run_mocha(project_dir)
            else:
                return {'success': False, 'error': f'Unsupported test framework: {test_framework}'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _detect_test_framework(self, project_dir: Path) -> str:
        """Detect test framework used in project"""
        # Check for Python test frameworks
        if (project_dir / 'pytest.ini').exists() or (project_dir / 'pyproject.toml').exists():
            return 'pytest'
        elif any(project_dir.rglob('test_*.py')):
            return 'unittest'
        
        # Check for JavaScript test frameworks
        package_json = project_dir / 'package.json'
        if package_json.exists():
            try:
                with open(package_json) as f:
                    package_data = json.load(f)
                    dependencies = {**package_data.get('dependencies', {}), **package_data.get('devDependencies', {})}
                    
                    if 'jest' in dependencies:
                        return 'jest'
                    elif 'mocha' in dependencies:
                        return 'mocha'
            except Exception:
                pass
        
        return 'unknown'
    
    def _run_pytest(self, project_dir: Path) -> Dict[str, Any]:
        """Run pytest"""
        try:
            result = subprocess.run(
                ['python', '-m', 'pytest', '-v'],
                cwd=project_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return {
                'success': result.returncode == 0,
                'framework': 'pytest',
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _run_unittest(self, project_dir: Path) -> Dict[str, Any]:
        """Run unittest"""
        try:
            result = subprocess.run(
                ['python', '-m', 'unittest', 'discover', '-v'],
                cwd=project_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return {
                'success': result.returncode == 0,
                'framework': 'unittest',
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _run_jest(self, project_dir: Path) -> Dict[str, Any]:
        """Run Jest tests"""
        try:
            result = subprocess.run(
                ['npm', 'test'],
                cwd=project_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return {
                'success': result.returncode == 0,
                'framework': 'jest',
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _run_mocha(self, project_dir: Path) -> Dict[str, Any]:
        """Run Mocha tests"""
        try:
            result = subprocess.run(
                ['npx', 'mocha'],
                cwd=project_dir,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return {
                'success': result.returncode == 0,
                'framework': 'mocha',
                'output': result.stdout,
                'error': result.stderr,
                'return_code': result.returncode
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_development_tools(log_manager=None) -> DevelopmentTools:
    """Factory function to create development tools"""
    return DevelopmentTools(log_manager)
