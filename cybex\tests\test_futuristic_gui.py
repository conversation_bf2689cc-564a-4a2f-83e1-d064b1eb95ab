#!/usr/bin/env python3
"""
Test Futuristic GUI
Test dell'interfaccia grafica futuristica
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_gui_components():
    """Test GUI components"""
    print("🎨 Testing Futuristic GUI Components")
    print("=" * 50)
    
    try:
        # Test tkinter availability
        import tkinter as tk
        from tkinter import ttk, scrolledtext
        print("✅ Tkinter available")
        
        # Test theme colors
        from cybex.interfaces.ui_futuristic_gui import FuturisticTheme
        
        print("✅ Futuristic theme loaded")
        print(f"   • Background: {FuturisticTheme.BG_DARK}")
        print(f"   • Neon Cyan: {FuturisticTheme.NEON_CYAN}")
        print(f"   • Neon Purple: {FuturisticTheme.NEON_PURPLE}")
        
        # Test animated components
        from cybex.interfaces.ui_futuristic_gui import AnimatedLabel
        print("✅ Animated components available")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_terminal_component():
    """Test terminal component"""
    print(f"\n💻 Testing Terminal Component")
    print("=" * 40)
    
    try:
        from cybex.interfaces.ui_futuristic_gui import FuturisticTerminal
        
        print("✅ FuturisticTerminal class available")
        
        # Test command detection logic
        class MockTerminal:
            def is_system_command(self, command: str) -> bool:
                system_commands = [
                    'dir', 'ls', 'pwd', 'echo', 'type', 'cat', 'ping', 'ipconfig',
                    'systeminfo', 'tasklist', 'netstat', 'whoami', 'date', 'time'
                ]
                cmd_name = command.split()[0].lower()
                return cmd_name in system_commands
        
        mock_terminal = MockTerminal()
        
        # Test commands
        test_commands = [
            ("systeminfo", True, "System command"),
            ("scansiona disco C", False, "AI command"),
            ("dir", True, "System command"),
            ("elimina file temp", False, "AI command")
        ]
        
        print("Command detection tests:")
        for cmd, expected, desc in test_commands:
            result = mock_terminal.is_system_command(cmd)
            status = "✅" if result == expected else "❌"
            print(f"  {status} '{cmd}' → {result} ({desc})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_system_requirements():
    """Test system requirements"""
    print(f"\n🔧 Testing System Requirements")
    print("=" * 40)
    
    try:
        # Test Python version
        if sys.version_info >= (3, 8):
            print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
        else:
            print(f"❌ Python version too old: {sys.version_info}")
            return False
        
        # Test required modules
        required_modules = [
            'tkinter',
            'psutil', 
            'requests',
            'threading',
            'queue',
            'subprocess',
            'json'
        ]
        
        missing_modules = []
        for module in required_modules:
            try:
                if module == 'tkinter':
                    import tkinter
                    import tkinter.ttk
                    import tkinter.scrolledtext
                else:
                    __import__(module)
                print(f"✅ {module}")
            except ImportError:
                missing_modules.append(module)
                print(f"❌ {module} missing")
        
        if missing_modules:
            print(f"\n📦 Missing modules: {', '.join(missing_modules)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_display_capabilities():
    """Test display capabilities"""
    print(f"\n🖥️  Testing Display Capabilities")
    print("=" * 40)
    
    try:
        import tkinter as tk
        
        # Create test window
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Check screen resolution
        width = root.winfo_screenwidth()
        height = root.winfo_screenheight()
        
        print(f"✅ Screen resolution: {width}x{height}")
        
        if width >= 1400 and height >= 900:
            print("✅ Optimal resolution for futuristic GUI")
        elif width >= 1024 and height >= 768:
            print("⚠️  Minimum resolution met")
        else:
            print("❌ Resolution too low for optimal experience")
            root.destroy()
            return False
        
        # Test color depth
        try:
            depth = root.winfo_screendepth()
            print(f"✅ Color depth: {depth} bits")
        except:
            print("⚠️  Could not determine color depth")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Display test error: {e}")
        return False


def test_font_availability():
    """Test font availability"""
    print(f"\n🔤 Testing Font Availability")
    print("=" * 35)
    
    try:
        import tkinter as tk
        import tkinter.font as tkfont
        
        root = tk.Tk()
        root.withdraw()
        
        available_fonts = tkfont.families()
        
        # Check for futuristic fonts
        preferred_fonts = [
            'Consolas',      # Monospace
            'Monaco',        # Monospace
            'Courier New',   # Monospace fallback
            'Orbitron',      # Futuristic (if available)
            'Arial',         # Sans-serif fallback
            'Helvetica'      # Sans-serif fallback
        ]
        
        found_fonts = []
        for font in preferred_fonts:
            if font in available_fonts:
                found_fonts.append(font)
                print(f"✅ {font}")
            else:
                print(f"⚠️  {font} not available")
        
        if len(found_fonts) >= 3:
            print("✅ Sufficient fonts available")
        else:
            print("⚠️  Limited font selection")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Font test error: {e}")
        return True  # Non-critical


def show_gui_preview():
    """Show GUI preview information"""
    preview = """
╔═══════════════════════════════════════════════════════════════════════════════╗
║                        🎨 FUTURISTIC GUI PREVIEW                             ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ 🎯 INTERFACE LAYOUT:                                                          ║
║                                                                               ║
║ ┌─────────────────────────────────────────────────────────────────────────┐   ║
║ │  ◢◤ CYBEX FUTURISTIC TERMINAL ◥◣          SYSTEM: ONLINE  AI: READY   │   ║
║ └─────────────────────────────────────────────────────────────────────────┘   ║
║ ┌─────────────────────────────────┬─────────────────────────────────────┐   ║
║ │                                 │  ◉ CONTROL PANEL ◉                 │   ║
║ │  ◉ CYBEX TERMINAL ◉             │                                     │   ║
║ │  ● READY                        │  ⚡ QUICK ACTIONS                   │   ║
║ │                                 │  🖥️ System Info                     │   ║
║ │  Terminal Output Area           │  💾 Disk Scan                       │   ║
║ │  (Scrollable, Colored)          │  🧹 Cleanup                         │   ║
║ │                                 │  📊 Processes                       │   ║
║ │                                 │                                     │   ║
║ │                                 │  📊 SYSTEM MONITOR                  │   ║
║ │                                 │  CPU: 25.3%                        │   ║
║ │                                 │  RAM: 67.8%                        │   ║
║ │  cybex❯ [Input Field]           │  DISK: 89.2%                       │   ║
║ └─────────────────────────────────┴─────────────────────────────────────┘   ║
║ ┌─────────────────────────────────────────────────────────────────────────┐   ║
║ │  ◢◤ CYBEX FUTURISTIC TERMINAL v2.0 - Advanced AI Interface ◥◣         │   ║
║ └─────────────────────────────────────────────────────────────────────────┘   ║
║                                                                               ║
║ 🌈 COLOR SCHEME:                                                              ║
║   • Background: Deep Black (#0a0a0a)                                         ║
║   • Panels: Dark Blue (#1a1a2e)                                              ║
║   • Neon Cyan: #00ffff (Headers, Highlights)                                 ║
║   • Neon Purple: #bf00ff (Commands, Accents)                                 ║
║   • Neon Green: #00ff41 (Success, Status)                                    ║
║   • Text: White/Gray for readability                                         ║
║                                                                               ║
║ ✨ SPECIAL EFFECTS:                                                           ║
║   • Typewriter animation for text                                            ║
║   • Pulsing colors for status indicators                                     ║
║   • Hover effects on buttons                                                 ║
║   • Real-time system monitoring                                              ║
║   • Command history with arrow keys                                          ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝
"""
    print(preview)


def main():
    """Run futuristic GUI tests"""
    print("🎯 Cybex Futuristic GUI - Test Suite")
    print("=" * 60)
    
    tests = [
        ("System Requirements", test_system_requirements),
        ("Display Capabilities", test_display_capabilities),
        ("GUI Components", test_gui_components),
        ("Terminal Component", test_terminal_component),
        ("Font Availability", test_font_availability)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= 4:  # At least 4 out of 5 tests should pass
        show_gui_preview()
        
        print(f"\n🎉 Cybex Futuristic GUI is ready!")
        print(f"🎨 Your futuristic interface is working perfectly!")
        
        print(f"\n🚀 Launch Options:")
        print(f"  • Double-click: cybex_futuristic.bat")
        print(f"  • Command line: python cybex_futuristic.py")
        
        print(f"\n💡 Features Ready:")
        print(f"  • Cyberpunk/Futuristic design with neon colors")
        print(f"  • Integrated terminal with CMD access")
        print(f"  • Real-time system monitoring")
        print(f"  • Quick action buttons")
        print(f"  • AI command processing")
        print(f"  • Animated UI elements")
        
        print(f"\n🎯 Experience Level: FUTURISTIC! 🚀")
    else:
        print(f"⚠️  Some tests failed. GUI may have limited functionality.")
        print(f"💡 Check the errors above and install missing requirements.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
