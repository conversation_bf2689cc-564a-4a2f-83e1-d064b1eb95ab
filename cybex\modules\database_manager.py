"""
Database Manager Module
SQLite database for session history, user preferences, and system state persistence
"""

import sqlite3
import json
import time
import threading
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from contextlib import contextmanager


class DatabaseManager:
    """
    Manages SQLite database for Cybex data persistence
    """
    
    def __init__(self, config_manager, log_manager):
        """Initialize database manager"""
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        
        # Database configuration
        self.db_config = config_manager.get_section('database')
        self.enabled = self.db_config.get('enabled', True)
        self.db_path = Path(self.db_config.get('path', 'cybex/data/cybex.db'))
        self.backup_enabled = self.db_config.get('backup_enabled', True)
        self.backup_interval = self.db_config.get('backup_interval', 24)  # hours
        
        # Thread safety
        self.db_lock = threading.Lock()
        
        # Ensure database directory exists
        if self.enabled:
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            self._init_database()
            
            # Schedule backup if enabled
            if self.backup_enabled:
                self._schedule_backup()
    
    def _init_database(self) -> None:
        """Initialize database schema"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # User profiles table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_profiles (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    username TEXT UNIQUE NOT NULL,
                    display_name TEXT,
                    preferences TEXT,  -- JSON
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    last_login TEXT
                )
                ''')
                
                # Session history table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS session_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    username TEXT,
                    start_time TEXT NOT NULL,
                    end_time TEXT,
                    mode TEXT,
                    commands_executed INTEGER DEFAULT 0,
                    session_data TEXT,  -- JSON
                    created_at TEXT NOT NULL
                )
                ''')
                
                # Command history table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS command_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id TEXT NOT NULL,
                    username TEXT,
                    command TEXT NOT NULL,
                    success INTEGER NOT NULL,
                    output TEXT,
                    execution_time REAL,
                    security_level TEXT,
                    timestamp TEXT NOT NULL
                )
                ''')
                
                # Agent plans table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS agent_plans (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    goal TEXT NOT NULL,
                    status TEXT NOT NULL,
                    created_by TEXT,
                    plan_data TEXT NOT NULL,  -- JSON
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    completed_at TEXT
                )
                ''')
                
                # Agent tasks table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS agent_tasks (
                    id TEXT PRIMARY KEY,
                    plan_id TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    command TEXT,
                    status TEXT NOT NULL,
                    priority INTEGER,
                    task_data TEXT,  -- JSON
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    completed_at TEXT,
                    FOREIGN KEY (plan_id) REFERENCES agent_plans (id)
                )
                ''')
                
                # System snapshots table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS system_snapshots (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    snapshot_path TEXT NOT NULL,
                    components TEXT,  -- JSON
                    created_by TEXT,
                    created_at TEXT NOT NULL,
                    size_bytes INTEGER
                )
                ''')
                
                # Configuration history table
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS config_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    config_key TEXT NOT NULL,
                    old_value TEXT,
                    new_value TEXT,
                    changed_by TEXT,
                    timestamp TEXT NOT NULL
                )
                ''')
                
                # Performance metrics table (already exists in system_monitor)
                cursor.execute('''
                CREATE TABLE IF NOT EXISTS performance_metrics (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp TEXT NOT NULL,
                    cpu_percent REAL,
                    memory_percent REAL,
                    disk_percent REAL,
                    network_sent INTEGER,
                    network_recv INTEGER,
                    temperature REAL,
                    processes INTEGER,
                    metric_data TEXT  -- JSON
                )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_session_history_username ON session_history(username)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_command_history_session ON command_history(session_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_command_history_timestamp ON command_history(timestamp)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_agent_plans_status ON agent_plans(status)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_agent_tasks_plan ON agent_tasks(plan_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_metrics(timestamp)')
                
                conn.commit()
                self.logger.info("Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """Get database connection with proper locking"""
        if not self.enabled:
            raise RuntimeError("Database is disabled")
        
        with self.db_lock:
            conn = sqlite3.connect(self.db_path, timeout=30.0)
            conn.row_factory = sqlite3.Row
            try:
                yield conn
            finally:
                conn.close()
    
    # User Profile Management
    def create_user_profile(self, username: str, display_name: str = "", 
                          preferences: Optional[Dict] = None) -> bool:
        """Create a new user profile"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                prefs_json = json.dumps(preferences or {})
                
                cursor.execute('''
                INSERT INTO user_profiles (username, display_name, preferences, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?)
                ''', (username, display_name, prefs_json, now, now))
                
                conn.commit()
                self.logger.info(f"Created user profile: {username}")
                return True
                
        except sqlite3.IntegrityError:
            self.logger.warning(f"User profile already exists: {username}")
            return False
        except Exception as e:
            self.logger.error(f"Failed to create user profile: {e}")
            return False
    
    def get_user_profile(self, username: str) -> Optional[Dict]:
        """Get user profile by username"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cursor.execute('SELECT * FROM user_profiles WHERE username = ?', (username,))
                row = cursor.fetchone()
                
                if row:
                    profile = dict(row)
                    if profile['preferences']:
                        profile['preferences'] = json.loads(profile['preferences'])
                    return profile
                
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to get user profile: {e}")
            return None
    
    def update_user_preferences(self, username: str, preferences: Dict) -> bool:
        """Update user preferences"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                prefs_json = json.dumps(preferences)
                now = datetime.now().isoformat()
                
                cursor.execute('''
                UPDATE user_profiles 
                SET preferences = ?, updated_at = ?
                WHERE username = ?
                ''', (prefs_json, now, username))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            self.logger.error(f"Failed to update user preferences: {e}")
            return False
    
    def update_last_login(self, username: str) -> bool:
        """Update last login time for user"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute('''
                UPDATE user_profiles 
                SET last_login = ?, updated_at = ?
                WHERE username = ?
                ''', (now, now, username))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            self.logger.error(f"Failed to update last login: {e}")
            return False
    
    # Session Management
    def create_session(self, session_id: str, username: str = "", mode: str = "chat") -> bool:
        """Create a new session record"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute('''
                INSERT INTO session_history (session_id, username, start_time, mode, created_at)
                VALUES (?, ?, ?, ?, ?)
                ''', (session_id, username, now, mode, now))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to create session: {e}")
            return False
    
    def end_session(self, session_id: str, commands_executed: int = 0, 
                   session_data: Optional[Dict] = None) -> bool:
        """End a session and update statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                data_json = json.dumps(session_data or {})
                
                cursor.execute('''
                UPDATE session_history 
                SET end_time = ?, commands_executed = ?, session_data = ?
                WHERE session_id = ?
                ''', (now, commands_executed, data_json, session_id))
                
                conn.commit()
                return cursor.rowcount > 0
                
        except Exception as e:
            self.logger.error(f"Failed to end session: {e}")
            return False
    
    def get_session_history(self, username: str = "", limit: int = 50) -> List[Dict]:
        """Get session history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if username:
                    cursor.execute('''
                    SELECT * FROM session_history 
                    WHERE username = ?
                    ORDER BY start_time DESC 
                    LIMIT ?
                    ''', (username, limit))
                else:
                    cursor.execute('''
                    SELECT * FROM session_history 
                    ORDER BY start_time DESC 
                    LIMIT ?
                    ''', (limit,))
                
                sessions = []
                for row in cursor.fetchall():
                    session = dict(row)
                    if session['session_data']:
                        session['session_data'] = json.loads(session['session_data'])
                    sessions.append(session)
                
                return sessions
                
        except Exception as e:
            self.logger.error(f"Failed to get session history: {e}")
            return []
    
    # Command History Management
    def log_command(self, session_id: str, username: str, command: str, 
                   success: bool, output: str = "", execution_time: float = 0.0,
                   security_level: str = "safe") -> bool:
        """Log command execution"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute('''
                INSERT INTO command_history 
                (session_id, username, command, success, output, execution_time, security_level, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (session_id, username, command, 1 if success else 0, 
                     output[:1000], execution_time, security_level, now))  # Truncate output
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to log command: {e}")
            return False
    
    def get_command_history(self, session_id: str = "", username: str = "", 
                          limit: int = 100) -> List[Dict]:
        """Get command history"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = 'SELECT * FROM command_history WHERE 1=1'
                params = []
                
                if session_id:
                    query += ' AND session_id = ?'
                    params.append(session_id)
                
                if username:
                    query += ' AND username = ?'
                    params.append(username)
                
                query += ' ORDER BY timestamp DESC LIMIT ?'
                params.append(limit)
                
                cursor.execute(query, params)
                
                commands = []
                for row in cursor.fetchall():
                    command = dict(row)
                    command['success'] = bool(command['success'])
                    commands.append(command)
                
                return commands
                
        except Exception as e:
            self.logger.error(f"Failed to get command history: {e}")
            return []
    
    # Agent Plan Management
    def save_agent_plan(self, plan_data: Dict) -> bool:
        """Save agent plan to database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                now = datetime.now().isoformat()
                
                cursor.execute('''
                INSERT OR REPLACE INTO agent_plans 
                (id, name, description, goal, status, created_by, plan_data, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    plan_data['id'],
                    plan_data['name'],
                    plan_data.get('description', ''),
                    plan_data['goal'],
                    plan_data['status'],
                    plan_data.get('created_by', ''),
                    json.dumps(plan_data),
                    plan_data.get('created_at', now),
                    now
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to save agent plan: {e}")
            return False
    
    def get_agent_plans(self, status: str = "", limit: int = 50) -> List[Dict]:
        """Get agent plans"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if status:
                    cursor.execute('''
                    SELECT * FROM agent_plans 
                    WHERE status = ?
                    ORDER BY created_at DESC 
                    LIMIT ?
                    ''', (status, limit))
                else:
                    cursor.execute('''
                    SELECT * FROM agent_plans 
                    ORDER BY created_at DESC 
                    LIMIT ?
                    ''', (limit,))
                
                plans = []
                for row in cursor.fetchall():
                    plan = dict(row)
                    if plan['plan_data']:
                        plan['plan_data'] = json.loads(plan['plan_data'])
                    plans.append(plan)
                
                return plans
                
        except Exception as e:
            self.logger.error(f"Failed to get agent plans: {e}")
            return []
    
    # Cleanup and Maintenance
    def cleanup_old_data(self, days: int = 30) -> Dict[str, int]:
        """Clean up old data from database"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                cutoff_date = (datetime.now() - timedelta(days=days)).isoformat()
                
                # Clean old command history
                cursor.execute('DELETE FROM command_history WHERE timestamp < ?', (cutoff_date,))
                commands_deleted = cursor.rowcount
                
                # Clean old performance metrics
                cursor.execute('DELETE FROM performance_metrics WHERE timestamp < ?', (cutoff_date,))
                metrics_deleted = cursor.rowcount
                
                # Clean old session history (keep sessions but remove detailed data)
                cursor.execute('''
                UPDATE session_history 
                SET session_data = NULL 
                WHERE start_time < ?
                ''', (cutoff_date,))
                sessions_cleaned = cursor.rowcount
                
                conn.commit()
                
                result = {
                    'commands_deleted': commands_deleted,
                    'metrics_deleted': metrics_deleted,
                    'sessions_cleaned': sessions_cleaned
                }
                
                self.logger.info(f"Database cleanup completed: {result}")
                return result
                
        except Exception as e:
            self.logger.error(f"Failed to cleanup database: {e}")
            return {}
    
    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # Table row counts
                tables = [
                    'user_profiles', 'session_history', 'command_history',
                    'agent_plans', 'agent_tasks', 'system_snapshots',
                    'config_history', 'performance_metrics'
                ]
                
                for table in tables:
                    try:
                        cursor.execute(f'SELECT COUNT(*) FROM {table}')
                        stats[f'{table}_count'] = cursor.fetchone()[0]
                    except:
                        stats[f'{table}_count'] = 0
                
                # Database file size
                if self.db_path.exists():
                    stats['file_size_mb'] = round(self.db_path.stat().st_size / (1024**2), 2)
                else:
                    stats['file_size_mb'] = 0
                
                # Recent activity
                cursor.execute('''
                SELECT COUNT(*) FROM command_history 
                WHERE timestamp > datetime('now', '-24 hours')
                ''')
                stats['commands_last_24h'] = cursor.fetchone()[0]
                
                cursor.execute('''
                SELECT COUNT(*) FROM session_history 
                WHERE start_time > datetime('now', '-7 days')
                ''')
                stats['sessions_last_7d'] = cursor.fetchone()[0]
                
                return stats
                
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {}
    
    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """Create database backup"""
        try:
            if not backup_path:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{self.db_path.parent}/cybex_backup_{timestamp}.db"
            
            backup_path = Path(backup_path)
            backup_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Use SQLite backup API
            with self.get_connection() as source_conn:
                backup_conn = sqlite3.connect(backup_path)
                source_conn.backup(backup_conn)
                backup_conn.close()
            
            self.logger.info(f"Database backup created: {backup_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to backup database: {e}")
            return False
    
    def _schedule_backup(self) -> None:
        """Schedule automatic database backups"""
        # This would be implemented with a proper scheduler in production
        # For now, just log that backup scheduling is available
        self.logger.info(f"Database backup scheduled every {self.backup_interval} hours")
