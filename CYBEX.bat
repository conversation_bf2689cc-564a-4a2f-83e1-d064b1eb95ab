@echo off
title CYBEX ENTERPRISE - FUTURISTIC GUI
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗███╗   ██╗████████╗  ║
echo ║   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝████╗  ██║╚══██╔══╝  ║
echo ║   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██╔██╗ ██║   ██║     ║
echo ║   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║╚██╗██║   ██║     ║
echo ║   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ███████╗██║ ╚████║   ██║     ║
echo ║    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝   ╚═╝     ║
echo ║                                                                              ║
echo ║                    🚀 FUTURISTIC GUI WITH OLLAMA AI 🚀                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if we're in the correct directory
if not exist "bin\cybex_futuristic.py" (
    echo ❌ Error: bin\cybex_futuristic.py not found
    echo 💡 Make sure you're running this from the CYBEX root directory
    echo.
    pause
    exit /b 1
)

REM Check Python installation
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python 3.8+ first.
    echo 📥 Download from: https://python.org/downloads/
    echo.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Check required packages
echo 🔍 Checking required packages...
python -c "import tkinter; import PIL" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Missing GUI packages (tkinter, PIL)
    echo 📦 Installing required packages...
    pip install pillow
    if errorlevel 1 (
        echo ❌ Failed to install packages
        echo 💡 Try: pip install pillow
        pause
        exit /b 1
    )
)

echo ✅ GUI packages available
echo.

REM Check Ollama server (optional)
echo 🔍 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not running (AI features will be limited)
    echo.
    echo 🔧 TO ENABLE AI FEATURES:
    echo 1. Install Ollama: https://ollama.ai/
    echo 2. Start server: ollama serve
    echo 3. Download model: ollama pull llama2
    echo.
    echo 💡 GUI will still work with system tools
    echo.
    set /p continue="Continue without AI? (Y/n): "
    if /i "%continue%"=="n" if /i "%continue%"=="no" (
        echo.
        echo 💡 Setup Ollama first, then run CYBEX.bat again
        pause
        exit /b 1
    )
) else (
    echo ✅ Ollama server is running - AI features enabled
)

echo.
echo 🚀 Starting CYBEX Futuristic GUI...
echo 🎨 Loading cyberpunk interface...
echo 💡 This may take a few moments...
echo.

REM Launch CYBEX Futuristic GUI
python bin\cybex_futuristic.py

REM Handle exit
echo.
if errorlevel 1 (
    echo ❌ CYBEX GUI exited with error
    echo 💡 Check the error messages above
) else (
    echo 👋 CYBEX GUI session ended normally
)

echo.
echo 📖 TROUBLESHOOTING:
echo • If GUI doesn't start: Install pillow with 'pip install pillow'
echo • If AI errors: Run 'ollama serve' in another terminal
echo • If import errors: Run 'pip install -r requirements.txt'
echo • For full features: Run 'pip install -r requirements_web.txt requirements_excel.txt'
echo.
echo 🎯 CYBEX ENTERPRISE FEATURES:
echo • 🎨 Futuristic cyberpunk GUI
echo • 🤖 Local AI with Ollama integration
echo • 🛠️ 45+ enterprise tools
echo • 🌐 Web browsing and analysis
echo • 📊 Excel creation and analysis
echo • 🗄️ Multi-database support
echo • 💻 Development tools
echo • 🛡️ Security and system administration
echo.
pause
