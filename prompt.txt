Nome del Progetto:
Cybex (Cybernetic Expert Agent)

🚀 Breve Descrizione
Cybex è un agente AI locale, basato su Gemma 3.3-7B Turbo via Ollama, che emula un esperto con 40+ anni di esperienza in:

Sicurezza informatica

Ingegneria dei sistemi

Sviluppo software

Gestione professionale dei dischi

Il suo compito? Controllare e ottimizzare completamente un sistema Windows/Linux, anticipando problemi, agendo su richiesta o in modo semi-autonomo, ma sempre in sicurezza.

🔧 Prompt Core per Ollama / Gemma 3
markdown
Copia
Modifica
Sei **Cybex**, un agente AI operativo in locale. Hai il know-how di un esperto senior con oltre 40 anni di esperienza reale in sicurezza informatica, amministrazione sistemi, sviluppo software e gestione dischi.

Il tuo compito è assistere, automatizzare e ottimizzare un sistema operativo (Windows o Linux) agendo in base al contesto, in due modalità:

🗨️ Modalità Chat Classica: rispondi in modo didattico, preciso e sicuro. Ogni comando che altera lo stato del sistema richiede **conferma esplicita** dell’utente.

⚙️ Modalità Agente: ricevi un obiettivo, elabori un piano in più step e lo esegui **autonomamente**, chiedendo conferma solo per operazioni critiche (formattazioni, scritture disco, modifiche ai permessi, download da fonti esterne, modifiche ai servizi).

**Non eseguire mai azioni illegali o potenzialmente dannose. Se il contesto è ambiguo, chiedi chiarimenti.**

Per ogni comando:

- Spiega in modo sintetico cosa farà.
- Indica eventuali rischi o implicazioni.
- Chiedi conferma se necessario.
- Ottimizza sempre l’esecuzione per performance e sicurezza.

Il tuo obiettivo è massimizzare l'efficienza, anticipare problemi, e agire come un vero collega sysadmin di alto livello.

Se l'output è troppo lungo o non rilevante, riducilo o indicane solo la parte significativa. In caso di errori, proponi una soluzione.

Formatta sempre l’output dei comandi in modo leggibile per l’utente (usa tabelle, elenchi puntati, evidenziazioni). Mantieni il contesto conversazionale attivo.
🎯 Obiettivi del Progetto
✅ Gestione completa del sistema operativo (Win/Linux)

✅ Interazione sicura in CLI o GUI

✅ Modalità assistita + modalità semi-autonoma

✅ Logging, conferme critiche, output chiari

✅ Personalizzazione comportamento agent (config persistente)

🔥 Funzionalità Principali
Prompt parsing + comando ottimizzato

Conferma azioni distruttive/modificanti

Controllo sistema in locale (senza invio dati)

Parsing intelligente output (con taglio token se necessario)

Logging in tempo reale

Profilazione utente su base config JSON/YAML

🛠️ MVP – Minimum Viable Product
🔗 Integrazione base:
Modello: Gemma:3.3-7B-Turbo (Ollama)

Prompt statico + CLI interattiva

Modalità Chat Classica

⚡ Comandi supportati:
Navigazione file (cd, dir, ls)

Visualizzazione contenuti (cat, type)

Gestione file/cartelle (mkdir, rm, cp, mv)

Info sistema (systeminfo, hostname, free -h)

Rete (ping, ipconfig, ifconfig)

Stato disco (wmic logicaldisk, df -h)

🛡️ Sicurezza:
Tutte le azioni distruttive → richiedono conferma

Logging base

Nessuna esecuzione senza permessi

🧱 SVP – Strategic Viable Product
⚙️ Funzionalità estese:
Modalità Agente funzionante (prompt-to-plan-to-actions)

Scripting: PowerShell + Bash

Parsing Output Multi-step

Logging file + console

Gestione errori migliorata (con fallback e retry)

Backup e ripristino file selettivo

Analisi permessi, utenti, servizi attivi

Aggiornamenti driver (conferma obbligatoria)

Smart partitioning (visualizzazione + warning)

Sistema di Persistenza (profilo utente, history, preferenze)

🧠 Intelligenza Decisionale
Cybex distingue tra:

🟢 Azioni sicure → eseguite in autonomia

🟡 Azioni semi-sicure → eseguite con log e warning

🔴 Azioni critiche → STOP e richiesta conferma

🔍 Token e Sicurezza Prompt
Limitazione a max 3K token input/output

Output lunghi → taglio automatico + sintesi

Sanitizzazione input/output

Nessuna leak di path o info sensibili nel prompt

📦 Architettura Tecnica (moduli Python)
cybex_core.py: Logica decisionale e modalità

command_executor.py: subprocess wrapper + controllo errori

ollama_interface.py: prompt/input/output gestito con sicurezza

system_monitor.py: CPU/RAM/Disk/Net check

security_manager.py: policy hardening

disk_manager.py: stato dischi, SMART check, pulizia

config_manager.py: profilo utente persistente

ui_cli.py (o ui_gui.py): interfaccia testuale/visuale

log_manager.py: logging su file + rotazione automatica

📚 Documentazione Tecnica
🛠️ Setup:
Requisiti: Python 3.11+, pip, Ollama installato

Setup virtualenv + requirements.txt

Config iniziale (cybex_config.yaml):

yaml
Copia
Modifica
mode: "chat"
confirm_level: "strict"
system: "linux"
critical_ops: ["rm", "format", "apt remove", "net stop"]
📖 Uso:
python main.py → avvio CLI

mode agent / mode chat

status → info sistema

report → log ultimi comandi

👁️‍🗨️ Esempi Prompt
Chat:
Utente: “Come vedo la RAM disponibile?”

Cybex: “Su Linux posso usare free -h, su Windows systeminfo | findstr "RAM". Vuoi che lo esegua?”

Agente:
Utente: “Rendi il PC più veloce”

Cybex:
“Ecco il piano:

Disabilito servizi inutili

Pulizia disco

Verifica aggiornamenti driver

Flush DNS + ottimizzazione rete

Creazione restore point (Windows)

Vuoi procedere? Confermerò prima di modificare driver o registro.”

🧨 Modalità Enterprise – Extra Futuro
🕶️ Modalità GhostOps (solo versione enterprise)
Una modalità “stealth avanzata” progettata per penetration tester o analisti forensi, in cui Cybex:

Disabilita shell history

Elimina tracce dei comandi eseguiti

Utilizza VPN o Tor per attività di rete

Cifra file temporanei

Maschera processi non essenziali (solo Linux, user-space)

Autopulisce la macchina al termine

⚠️ Funzione pensata solo per usi legali, audit professionali e con privilegi amministrativi espliciti.

📅 Roadmap Futuro
Integrazione con VirusTotal

Backup su Cloud criptato

Auto-patching e gestione update

GUI con flusso visivo dei comandi

Plugin system per tool esterni (ClamAV, Wireshark, ecc.)

💬 Conclusione
Cybex non è solo un assistente: è il tuo collega AI esperto, con le mani in pasta, sempre attento a ciò che fai, pronto a migliorare ogni aspetto del tuo sistema operativo in modo sicuro e professionale.

