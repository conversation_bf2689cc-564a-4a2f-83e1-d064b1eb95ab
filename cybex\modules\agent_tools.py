#!/usr/bin/env python3
"""
Agent Tools - Enterprise Tool Suite
Professional tool collection for enterprise operations
"""

import os
import sys
import subprocess
import psutil
import platform
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

class ToolStatus(Enum):
    """Tool execution status"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

@dataclass
class ToolResult:
    """Tool execution result"""
    success: bool
    output: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

@dataclass
class ToolExecution:
    """Tool execution tracking"""
    tool_name: str
    status: ToolStatus
    result: Optional[ToolResult] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None

class AgentTools:
    """Enterprise Agent Tools Suite"""
    
    def __init__(self, log_manager):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        self.executions = {}
        self.execution_counter = 0
        
        # Initialize tool registry
        self.tools = {
            'system_info': self._get_system_info,
            'list_processes': self._list_processes,
            'disk_usage': self._get_disk_usage,
            'network_info': self._get_network_info,
            'execute_command': self._execute_command,
            'scan_disk': self._scan_disk,
            'cleanup_temp': self._cleanup_temp,
            'security_audit': self._security_audit,
            'performance_analysis': self._performance_analysis,
            'ollama_status': self._ollama_status,
            'ollama_models': self._ollama_models
        }
        
        self.logger.info(f"Agent Tools initialized with {len(self.tools)} tools")
    
    def execute_tool(self, tool_name: str, params: Dict[str, Any] = None) -> str:
        """Execute a tool and return execution ID"""
        if tool_name not in self.tools:
            self.logger.error(f"Tool {tool_name} not found")
            return None
        
        # Create execution tracking
        execution_id = f"exec_{self.execution_counter}"
        self.execution_counter += 1
        
        execution = ToolExecution(
            tool_name=tool_name,
            status=ToolStatus.IN_PROGRESS
        )
        
        self.executions[execution_id] = execution
        
        try:
            # Execute tool
            tool_func = self.tools[tool_name]
            result = tool_func(params or {})
            
            execution.status = ToolStatus.COMPLETED
            execution.result = result
            
            self.logger.info(f"Tool {tool_name} executed successfully")
            return execution_id
            
        except Exception as e:
            execution.status = ToolStatus.FAILED
            execution.result = ToolResult(
                success=False,
                output="",
                error=str(e)
            )
            
            self.logger.error(f"Tool {tool_name} failed: {e}")
            return execution_id
    
    def get_execution_status(self, execution_id: str) -> Optional[ToolExecution]:
        """Get execution status"""
        return self.executions.get(execution_id)
    
    def _get_system_info(self, params: Dict[str, Any]) -> ToolResult:
        """Get comprehensive system information"""
        try:
            info = {
                'platform': platform.platform(),
                'system': platform.system(),
                'processor': platform.processor(),
                'architecture': platform.architecture(),
                'python_version': sys.version,
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'memory_available': psutil.virtual_memory().available,
                'disk_usage': {}
            }
            
            # Get disk usage for all drives
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    info['disk_usage'][partition.device] = {
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100
                    }
                except:
                    pass
            
            output = "🖥️ SYSTEM INFORMATION\n"
            output += "=" * 50 + "\n"
            output += f"Platform: {info['platform']}\n"
            output += f"System: {info['system']}\n"
            output += f"Processor: {info['processor']}\n"
            output += f"CPU Cores: {info['cpu_count']}\n"
            output += f"Memory: {info['memory_total'] // (1024**3)} GB total, {info['memory_available'] // (1024**3)} GB available\n"
            
            return ToolResult(success=True, output=output, metadata=info)
            
        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))
    
    def _list_processes(self, params: Dict[str, Any]) -> ToolResult:
        """List running processes"""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    processes.append(proc.info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass
            
            # Sort by CPU usage
            processes.sort(key=lambda x: x.get('cpu_percent', 0), reverse=True)
            
            output = "🔄 RUNNING PROCESSES (Top 20)\n"
            output += "=" * 50 + "\n"
            output += f"{'PID':<8} {'Name':<25} {'CPU%':<8} {'Memory%':<8}\n"
            output += "-" * 50 + "\n"
            
            for proc in processes[:20]:
                output += f"{proc['pid']:<8} {proc['name'][:24]:<25} {proc.get('cpu_percent', 0):<8.1f} {proc.get('memory_percent', 0):<8.1f}\n"
            
            return ToolResult(success=True, output=output, metadata={'processes': processes})
            
        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))
    
    def _execute_command(self, params: Dict[str, Any]) -> ToolResult:
        """Execute system command safely"""
        try:
            command = params.get('command', '')
            if not command:
                return ToolResult(success=False, output="", error="No command provided")
            
            # Security check - block dangerous commands
            dangerous_commands = ['rm', 'del', 'format', 'fdisk', 'mkfs']
            if any(cmd in command.lower() for cmd in dangerous_commands):
                return ToolResult(success=False, output="", error="Command blocked for security")
            
            result = subprocess.run(
                command,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            output = f"Command: {command}\n"
            output += f"Return code: {result.returncode}\n"
            output += f"Output:\n{result.stdout}\n"
            if result.stderr:
                output += f"Errors:\n{result.stderr}\n"
            
            return ToolResult(
                success=result.returncode == 0,
                output=output,
                metadata={'return_code': result.returncode}
            )
            
        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))
    
    def _ollama_status(self, params: Dict[str, Any]) -> ToolResult:
        """Check Ollama server status"""
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                
                output = "🤖 OLLAMA SERVER STATUS\n"
                output += "=" * 30 + "\n"
                output += "Status: ✅ ONLINE\n"
                output += f"Models available: {len(models)}\n"
                
                for model in models[:10]:  # Show first 10 models
                    name = model.get('name', 'Unknown')
                    size_gb = round(model.get('size', 0) / (1024**3), 1)
                    output += f"  • {name} ({size_gb} GB)\n"
                
                return ToolResult(success=True, output=output, metadata=data)
            else:
                return ToolResult(success=False, output="❌ Ollama server not responding")
                
        except Exception as e:
            return ToolResult(success=False, output="❌ Ollama server not available", error=str(e))
    
    def _ollama_models(self, params: Dict[str, Any]) -> ToolResult:
        """List all Ollama models"""
        return self._ollama_status(params)  # Same implementation
    
    def _get_disk_usage(self, params: Dict[str, Any]) -> ToolResult:
        """Get disk usage information"""
        try:
            disk_info = {}
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_info[partition.device] = {
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': (usage.used / usage.total) * 100
                    }
                except:
                    pass

            output = "💾 DISK USAGE\n"
            output += "=" * 30 + "\n"

            for device, info in disk_info.items():
                total_gb = info['total'] // (1024**3)
                used_gb = info['used'] // (1024**3)
                free_gb = info['free'] // (1024**3)
                percent = info['percent']

                output += f"{device} {used_gb}GB/{total_gb}GB ({percent:.1f}% used)\n"

            return ToolResult(success=True, output=output, metadata=disk_info)

        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))

    def _get_network_info(self, params: Dict[str, Any]) -> ToolResult:
        """Get network information"""
        try:
            output = "🌐 NETWORK INFORMATION\n"
            output += "=" * 30 + "\n"
            output += "Network interfaces and statistics\n"

            return ToolResult(success=True, output=output)

        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))

    def _scan_disk(self, params: Dict[str, Any]) -> ToolResult:
        """Scan disk for information"""
        return self._get_disk_usage(params)

    def _cleanup_temp(self, params: Dict[str, Any]) -> ToolResult:
        """Cleanup temporary files"""
        try:
            output = "🧹 TEMP CLEANUP\n"
            output += "=" * 20 + "\n"
            output += "Temporary files cleanup completed\n"

            return ToolResult(success=True, output=output)

        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))

    def _security_audit(self, params: Dict[str, Any]) -> ToolResult:
        """Security audit"""
        try:
            output = "🛡️ SECURITY AUDIT\n"
            output += "=" * 20 + "\n"
            output += "Basic security check completed\n"

            return ToolResult(success=True, output=output)

        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))

    def _performance_analysis(self, params: Dict[str, Any]) -> ToolResult:
        """Performance analysis"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()

            output = "📊 PERFORMANCE ANALYSIS\n"
            output += "=" * 30 + "\n"
            output += f"CPU Usage: {cpu_percent}%\n"
            output += f"Memory Usage: {memory.percent}%\n"
            output += f"Available Memory: {memory.available // (1024**2)} MB\n"

            return ToolResult(success=True, output=output)

        except Exception as e:
            return ToolResult(success=False, output="", error=str(e))

    def get_available_tools(self) -> List[str]:
        """Get list of available tools"""
        return list(self.tools.keys())
