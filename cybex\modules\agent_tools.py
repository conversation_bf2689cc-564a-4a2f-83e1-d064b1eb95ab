#!/usr/bin/env python3
"""
Agent Tools
Sistema di tool avanzati per l'agente Cybex
"""

import os
import sys
import subprocess
import threading
import time
import json
import psutil
import asyncio
import functools
import socket
import requests
from typing import Dict, List, Optional, Any, Callable
from pathlib import Path
from dataclasses import dataclass
from enum import Enum
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
from .security_first_architecture import SecurityFirstArchitecture
from .performance_optimizer import PerformanceOptimizer

class ToolType(Enum):
    """Tipi di tool disponibili"""
    SYSTEM_COMMAND = "system_command"
    FILE_OPERATION = "file_operation"
    NETWORK_OPERATION = "network_operation"
    PROCESS_MANAGEMENT = "process_management"
    SYSTEM_INFO = "system_info"
    CUSTOM = "custom"

class ToolStatus(Enum):
    """Stati di esecuzione tool"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ToolResult:
    """Risultato esecuzione tool"""
    success: bool
    output: str
    error: str = ""
    exit_code: int = 0
    execution_time: float = 0.0
    metadata: Dict[str, Any] = None
    retry_count: int = 0
    risk_level: str = "UNKNOWN"
    security_validated: bool = False

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


def retry_on_failure(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator for automatic retry on tool failure"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            start_time = time.time()

            for attempt in range(max_retries + 1):
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time

                    if hasattr(result, 'execution_time'):
                        result.execution_time = execution_time
                    if hasattr(result, 'retry_count'):
                        result.retry_count = attempt

                    return result
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        time.sleep(delay * (backoff ** attempt))
                        continue
                    else:
                        return ToolResult(
                            success=False,
                            output="",
                            error=f"Failed after {max_retries} retries: {str(e)}",
                            retry_count=attempt,
                            execution_time=time.time() - start_time,
                            risk_level="HIGH"
                        )

            return ToolResult(
                success=False,
                output="",
                error=f"Unexpected error: {str(last_exception)}",
                retry_count=max_retries,
                execution_time=time.time() - start_time,
                risk_level="HIGH"
            )
        return wrapper
    return decorator


def validate_parameters(required_params: List[str], optional_params: List[str] = None):
    """Decorator for parameter validation"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, **kwargs):
            # Check required parameters
            missing_params = [param for param in required_params if param not in kwargs]
            if missing_params:
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Missing required parameters: {', '.join(missing_params)}",
                    risk_level="LOW"
                )

            # Validate parameter types and values
            validation_result = self._validate_parameter_values(kwargs)
            if not validation_result.success:
                return validation_result

            return func(self, **kwargs)
        return wrapper
    return decorator


def security_check(risk_level: str = "MEDIUM"):
    """Decorator for security validation"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(self, **kwargs):
            # Perform security validation
            security_result = self._perform_security_check(func.__name__, kwargs, risk_level)
            if not security_result.success:
                return security_result

            # Execute function
            result = func(self, **kwargs)

            # Mark as security validated
            if hasattr(result, 'security_validated'):
                result.security_validated = True
            if hasattr(result, 'risk_level'):
                result.risk_level = risk_level

            return result
        return wrapper
    return decorator

@dataclass
class ToolExecution:
    """Esecuzione tool in corso"""
    tool_id: str
    tool_name: str
    command: str
    status: ToolStatus
    start_time: float
    end_time: Optional[float] = None
    result: Optional[ToolResult] = None
    process: Optional[subprocess.Popen] = None

class AgentTools:
    """Sistema di tool per l'agente Cybex"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Tool registry
        self.tools: Dict[str, Callable] = {}
        self.tool_descriptions: Dict[str, Dict] = {}
        
        # Execution tracking
        self.active_executions: Dict[str, ToolExecution] = {}
        self.execution_history: List[ToolExecution] = []
        
        # Security settings
        self.allowed_commands = [
            'dir', 'ls', 'pwd', 'cd', 'echo', 'type', 'cat',
            'systeminfo', 'tasklist', 'ps', 'netstat', 'ipconfig',
            'ping', 'tracert', 'nslookup', 'whoami', 'date', 'time',
            'wmic', 'get-process', 'get-service', 'get-computerinfo'
        ]
        
        self.blocked_commands = [
            'format', 'del', 'rm', 'rmdir', 'rd', 'shutdown', 'restart',
            'reboot', 'halt', 'poweroff', 'kill', 'taskkill', 'net user',
            'net localgroup', 'reg delete', 'reg add'
        ]
        
        # Initialize validation and security systems
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.security_policies = self._load_security_policies()

        # Initialize security-first architecture
        try:
            self.security_architecture = SecurityFirstArchitecture(log_manager)
            if self.logger:
                self.logger.info("Security-first architecture initialized")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not initialize security architecture: {e}")
            self.security_architecture = None

        # Initialize performance optimizer
        try:
            self.performance_optimizer = PerformanceOptimizer(log_manager)
            if self.logger:
                self.logger.info("Performance optimizer initialized")
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not initialize performance optimizer: {e}")
            self.performance_optimizer = None

        # Initialize built-in tools
        self._register_builtin_tools()

        # Initialize enterprise tools
        self._register_enterprise_tools()
    
    def _register_builtin_tools(self):
        """Registra i tool built-in"""
        
        # System command tool
        self.register_tool(
            name="execute_command",
            func=self._execute_system_command,
            description="Execute system commands (CMD/PowerShell)",
            tool_type=ToolType.SYSTEM_COMMAND,
            parameters={
                "command": {"type": "string", "description": "Command to execute"},
                "shell": {"type": "string", "description": "Shell type (cmd/powershell)", "default": "cmd"},
                "timeout": {"type": "integer", "description": "Timeout in seconds", "default": 30}
            }
        )
        
        # File operations
        self.register_tool(
            name="list_directory",
            func=self._list_directory,
            description="List directory contents",
            tool_type=ToolType.FILE_OPERATION,
            parameters={
                "path": {"type": "string", "description": "Directory path", "default": "."},
                "detailed": {"type": "boolean", "description": "Show detailed info", "default": False}
            }
        )
        
        # System info
        self.register_tool(
            name="get_system_info",
            func=self._get_system_info,
            description="Get comprehensive system information",
            tool_type=ToolType.SYSTEM_INFO,
            parameters={
                "category": {"type": "string", "description": "Info category (cpu/memory/disk/network/all)", "default": "all"}
            }
        )
        
        # Process management
        self.register_tool(
            name="list_processes",
            func=self._list_processes,
            description="List running processes",
            tool_type=ToolType.PROCESS_MANAGEMENT,
            parameters={
                "filter": {"type": "string", "description": "Process name filter", "default": ""},
                "sort_by": {"type": "string", "description": "Sort by (cpu/memory/name)", "default": "cpu"}
            }
        )
        
        # Network operations
        self.register_tool(
            name="network_scan",
            func=self._network_scan,
            description="Scan network connections and ports",
            tool_type=ToolType.NETWORK_OPERATION,
            parameters={
                "scan_type": {"type": "string", "description": "Scan type (connections/ports/ping)", "default": "connections"}
            }
        )

        # Disk operations
        self.register_tool(
            name="scan_disk",
            func=self._scan_disk,
            description="Scan disk for usage, health, and file analysis",
            tool_type=ToolType.SYSTEM_INFO,
            parameters={
                "drive": {"type": "string", "description": "Drive letter (C, D, etc.)", "default": "C"},
                "scan_type": {"type": "string", "description": "Scan type (usage/health/large_files/temp_files)", "default": "usage"},
                "depth": {"type": "integer", "description": "Scan depth for file analysis", "default": 2}
            }
        )

        # Safe cleanup operations
        self.register_tool(
            name="cleanup_temp_files",
            func=self._cleanup_temp_files,
            description="Safely clean temporary files and system cache",
            tool_type=ToolType.FILE_OPERATION,
            parameters={
                "cleanup_type": {"type": "string", "description": "Cleanup type (temp/cache/recycle/all)", "default": "temp"},
                "dry_run": {"type": "boolean", "description": "Preview only, don't delete", "default": True},
                "drive": {"type": "string", "description": "Target drive", "default": "C"}
            }
        )
    
    def register_tool(self, name: str, func: Callable, description: str,
                     tool_type: ToolType, parameters: Dict[str, Any] = None):
        """Registra un nuovo tool"""
        self.tools[name] = func
        self.tool_descriptions[name] = {
            "description": description,
            "type": tool_type.value,
            "parameters": parameters or {}
        }
        
        if self.logger:
            self.logger.info(f"Registered tool: {name}")

    def _load_security_policies(self) -> Dict[str, Any]:
        """Load security policies for tool validation"""
        return {
            "high_risk_commands": [
                "format", "del", "rm", "rmdir", "shutdown", "reboot",
                "kill", "taskkill", "net user", "reg delete", "diskpart"
            ],
            "medium_risk_commands": [
                "copy", "move", "xcopy", "robocopy", "attrib", "cacls",
                "icacls", "takeown", "cipher", "sfc", "chkdsk"
            ],
            "allowed_paths": [
                "C:\\temp", "C:\\tmp", "C:\\Windows\\temp",
                "%USERPROFILE%\\temp", "%TEMP%", "%TMP%"
            ],
            "blocked_paths": [
                "C:\\Windows\\System32", "C:\\Program Files",
                "C:\\Program Files (x86)", "C:\\Users\\<USER>\\AppData"
            ],
            "max_execution_time": 300,  # 5 minutes
            "require_confirmation": ["HIGH", "CRITICAL"]
        }

    def _validate_parameter_values(self, parameters: Dict[str, Any]) -> ToolResult:
        """Validate parameter values for security and correctness"""
        try:
            # Check for potentially dangerous parameters
            for key, value in parameters.items():
                if isinstance(value, str):
                    # Check for command injection attempts
                    dangerous_patterns = [
                        ';', '&&', '||', '|', '>', '>>', '<',
                        '$(', '`', 'eval', 'exec', 'system'
                    ]
                    if any(pattern in value for pattern in dangerous_patterns):
                        return ToolResult(
                            success=False,
                            output="",
                            error=f"Potentially dangerous pattern detected in parameter '{key}': {value}",
                            risk_level="HIGH"
                        )

                    # Check for path traversal attempts
                    if '..' in value or '~' in value:
                        return ToolResult(
                            success=False,
                            output="",
                            error=f"Path traversal attempt detected in parameter '{key}': {value}",
                            risk_level="HIGH"
                        )

            return ToolResult(success=True, output="Parameters validated", risk_level="LOW")

        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Parameter validation error: {str(e)}",
                risk_level="MEDIUM"
            )

    def _perform_security_check(self, tool_name: str, parameters: Dict[str, Any], risk_level: str) -> ToolResult:
        """Perform comprehensive security check using security-first architecture"""
        try:
            # Use advanced security architecture if available
            if self.security_architecture:
                user_context = {
                    'user_id': 'cybex_user',
                    'session_id': getattr(self, 'current_session_id', 'default_session'),
                    'expertise_level': 'INTERMEDIATE',
                    'session_duration': 0,
                    'system_load': self._get_current_system_load()
                }

                # Validate operation through security architecture
                approved, message, details = self.security_architecture.validate_operation(
                    tool_name, user_context, resource=parameters.get('command', tool_name), data=parameters
                )

                if approved:
                    return ToolResult(
                        success=True,
                        output="Security validation passed",
                        risk_level=details.get('security_level', risk_level),
                        security_validated=True,
                        metadata={
                            'security_details': details,
                            'risk_score': details.get('risk_score', 0)
                        }
                    )
                else:
                    return ToolResult(
                        success=False,
                        output="",
                        error=f"Security validation failed: {message}",
                        risk_level="HIGH",
                        security_validated=False,
                        metadata={'security_details': details}
                    )
            else:
                # Fallback to basic security check
                return self._basic_security_check(tool_name, parameters, risk_level)

        except Exception as e:
            if self.logger:
                self.logger.error(f"Advanced security check failed: {e}")
            return self._basic_security_check(tool_name, parameters, risk_level)

    def _basic_security_check(self, tool_name: str, parameters: Dict[str, Any], risk_level: str) -> ToolResult:
        """Basic security check fallback"""
        try:
            # Check if tool is in high-risk category
            if tool_name in ["execute_command", "cleanup_temp_files"]:
                command = parameters.get("command", "")
                if any(cmd in command.lower() for cmd in self.security_policies["high_risk_commands"]):
                    return ToolResult(
                        success=False,
                        output="",
                        error=f"High-risk command blocked: {command}",
                        risk_level="CRITICAL"
                    )

            # Check execution time limits
            timeout = parameters.get("timeout", 30)
            if timeout > self.security_policies["max_execution_time"]:
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Execution timeout exceeds policy limit: {timeout}s > {self.security_policies['max_execution_time']}s",
                    risk_level="MEDIUM"
                )

            # Log security check
            if self.logger:
                self.logger.info(f"Basic security check passed for tool: {tool_name}, risk: {risk_level}")

            return ToolResult(success=True, output="Basic security check passed", risk_level=risk_level)

        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Security check error: {str(e)}",
                risk_level="HIGH"
            )

    def _get_current_system_load(self) -> Dict[str, float]:
        """Get current system load for security context"""
        try:
            import psutil
            return {
                'cpu': psutil.cpu_percent(),
                'memory': psutil.virtual_memory().percent,
                'disk': psutil.disk_usage('/').percent
            }
        except Exception:
            return {'cpu': 0, 'memory': 0, 'disk': 0}

    def get_available_tools(self) -> Dict[str, Dict]:
        """Ottieni lista tool disponibili"""
        return self.tool_descriptions.copy()
    
    def execute_tool_optimized(self, tool_name: str, parameters: Dict[str, Any] = None) -> ToolResult:
        """Execute tool with performance optimization"""
        if not self.performance_optimizer:
            return self.execute_tool_basic(tool_name, parameters)

        try:
            # Use performance optimizer to execute tool
            result = self.performance_optimizer.optimize_operation(
                f"tool_{tool_name}",
                self.execute_tool_basic,
                tool_name,
                parameters
            )
            return result

        except Exception as e:
            if self.logger:
                self.logger.error(f"Optimized tool execution failed: {e}")
            return self.execute_tool_basic(tool_name, parameters)

    def execute_tool_basic(self, tool_name: str, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui un tool base senza ottimizzazioni"""
        if tool_name not in self.tools:
            return ToolResult(
                success=False,
                output="",
                error=f"Tool '{tool_name}' not found",
                risk_level="LOW"
            )

        try:
            # Execute tool function
            tool_func = self.tools[tool_name]
            result = tool_func(**(parameters or {}))

            if isinstance(result, ToolResult):
                return result
            else:
                # Convert legacy result to ToolResult
                return ToolResult(
                    success=True,
                    output=str(result),
                    risk_level="MEDIUM"
                )

        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Tool execution failed: {str(e)}",
                risk_level="HIGH"
            )

    def execute_tool(self, tool_name: str, parameters: Dict[str, Any] = None) -> str:
        """Esegui un tool e restituisci l'ID esecuzione (legacy compatibility)"""
        if tool_name not in self.tools:
            raise ValueError(f"Tool '{tool_name}' not found")
        
        if parameters is None:
            parameters = {}
        
        # Generate execution ID
        execution_id = f"{tool_name}_{int(time.time() * 1000)}"
        
        # Create execution record
        execution = ToolExecution(
            tool_id=execution_id,
            tool_name=tool_name,
            command=f"{tool_name}({parameters})",
            status=ToolStatus.PENDING,
            start_time=time.time()
        )
        
        self.active_executions[execution_id] = execution
        
        # Execute in thread
        thread = threading.Thread(
            target=self._execute_tool_async,
            args=(execution_id, tool_name, parameters)
        )
        thread.daemon = True
        thread.start()
        
        return execution_id
    
    def _execute_tool_async(self, execution_id: str, tool_name: str, parameters: Dict[str, Any]):
        """Esegui tool in modo asincrono"""
        execution = self.active_executions[execution_id]
        execution.status = ToolStatus.RUNNING
        
        try:
            # Execute tool
            tool_func = self.tools[tool_name]
            result = tool_func(**parameters)
            
            execution.result = result
            execution.status = ToolStatus.COMPLETED if result.success else ToolStatus.FAILED
            
        except Exception as e:
            execution.result = ToolResult(
                success=False,
                output="",
                error=str(e)
            )
            execution.status = ToolStatus.FAILED
            
            if self.logger:
                self.logger.error(f"Tool execution failed: {e}")
        
        finally:
            execution.end_time = time.time()
            if execution.result:
                execution.result.execution_time = execution.end_time - execution.start_time
            
            # Move to history
            self.execution_history.append(execution)
            if execution_id in self.active_executions:
                del self.active_executions[execution_id]
    
    def get_execution_status(self, execution_id: str) -> Optional[ToolExecution]:
        """Ottieni stato esecuzione"""
        if execution_id in self.active_executions:
            return self.active_executions[execution_id]
        
        # Check history
        for execution in self.execution_history:
            if execution.tool_id == execution_id:
                return execution
        
        return None
    
    def get_active_executions(self) -> List[ToolExecution]:
        """Ottieni esecuzioni attive"""
        return list(self.active_executions.values())
    
    def cancel_execution(self, execution_id: str) -> bool:
        """Cancella esecuzione"""
        if execution_id not in self.active_executions:
            return False
        
        execution = self.active_executions[execution_id]
        
        if execution.process:
            try:
                execution.process.terminate()
                execution.status = ToolStatus.CANCELLED
                return True
            except:
                pass
        
        return False
    
    # Built-in tool implementations
    
    @retry_on_failure(max_retries=2, delay=0.5)
    @validate_parameters(required_params=["command"])
    @security_check(risk_level="HIGH")
    def _execute_system_command(self, command: str, shell: str = "cmd", timeout: int = 30) -> ToolResult:
        """Esegui comando sistema"""
        
        # Security check
        cmd_lower = command.lower().strip()
        
        # Check blocked commands
        for blocked in self.blocked_commands:
            if blocked in cmd_lower:
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Command blocked for security: {blocked}"
                )
        
        try:
            start_time = time.time()
            
            if shell.lower() == "powershell":
                full_command = ["powershell", "-Command", command]
            else:
                full_command = ["cmd", "/c", command]
            
            process = subprocess.Popen(
                full_command,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            try:
                stdout, stderr = process.communicate(timeout=timeout)
                execution_time = time.time() - start_time
                
                return ToolResult(
                    success=process.returncode == 0,
                    output=stdout,
                    error=stderr,
                    exit_code=process.returncode,
                    execution_time=execution_time,
                    metadata={
                        "command": command,
                        "shell": shell,
                        "timeout": timeout
                    }
                )
                
            except subprocess.TimeoutExpired:
                process.kill()
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Command timed out after {timeout} seconds"
                )
                
        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Execution error: {str(e)}"
            )
    
    def _list_directory(self, path: str = ".", detailed: bool = False) -> ToolResult:
        """Lista contenuti directory"""
        try:
            path_obj = Path(path)
            if not path_obj.exists():
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Path does not exist: {path}"
                )
            
            if not path_obj.is_dir():
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Path is not a directory: {path}"
                )
            
            items = []
            for item in path_obj.iterdir():
                if detailed:
                    stat = item.stat()
                    size = stat.st_size if item.is_file() else 0
                    modified = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(stat.st_mtime))
                    item_type = "DIR" if item.is_dir() else "FILE"
                    items.append(f"{item_type:<4} {size:>10} {modified} {item.name}")
                else:
                    items.append(item.name)
            
            output = "\n".join(sorted(items))
            
            return ToolResult(
                success=True,
                output=output,
                metadata={
                    "path": str(path_obj.absolute()),
                    "item_count": len(items),
                    "detailed": detailed
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Directory listing error: {str(e)}"
            )
    
    def _get_system_info(self, category: str = "all") -> ToolResult:
        """Ottieni informazioni sistema"""
        try:
            info = {}
            
            if category in ["cpu", "all"]:
                info["cpu"] = {
                    "physical_cores": psutil.cpu_count(logical=False),
                    "logical_cores": psutil.cpu_count(logical=True),
                    "usage_percent": psutil.cpu_percent(interval=1),
                    "frequency": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None
                }
            
            if category in ["memory", "all"]:
                memory = psutil.virtual_memory()
                info["memory"] = {
                    "total": memory.total,
                    "available": memory.available,
                    "used": memory.used,
                    "percentage": memory.percent
                }
            
            if category in ["disk", "all"]:
                info["disk"] = []
                for partition in psutil.disk_partitions():
                    try:
                        usage = psutil.disk_usage(partition.mountpoint)
                        info["disk"].append({
                            "device": partition.device,
                            "mountpoint": partition.mountpoint,
                            "fstype": partition.fstype,
                            "total": usage.total,
                            "used": usage.used,
                            "free": usage.free,
                            "percentage": (usage.used / usage.total) * 100
                        })
                    except:
                        continue
            
            if category in ["network", "all"]:
                info["network"] = {
                    "interfaces": {},
                    "connections": len(psutil.net_connections())
                }
                
                for interface, addresses in psutil.net_if_addrs().items():
                    info["network"]["interfaces"][interface] = [
                        addr._asdict() for addr in addresses
                    ]
            
            output = json.dumps(info, indent=2, default=str)
            
            return ToolResult(
                success=True,
                output=output,
                metadata={
                    "category": category,
                    "timestamp": time.time()
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"System info error: {str(e)}"
            )
    
    def _list_processes(self, filter: str = "", sort_by: str = "cpu") -> ToolResult:
        """Lista processi"""
        try:
            processes = []
            
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                try:
                    proc_info = proc.info
                    if filter and filter.lower() not in proc_info['name'].lower():
                        continue
                    
                    processes.append(proc_info)
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Sort processes
            if sort_by == "cpu":
                processes.sort(key=lambda x: x['cpu_percent'], reverse=True)
            elif sort_by == "memory":
                processes.sort(key=lambda x: x['memory_percent'], reverse=True)
            elif sort_by == "name":
                processes.sort(key=lambda x: x['name'].lower())
            
            # Format output
            lines = ["PID      NAME                     CPU%    MEM%"]
            lines.append("-" * 50)
            
            for proc in processes[:20]:  # Top 20
                lines.append(f"{proc['pid']:<8} {proc['name'][:20]:<20} {proc['cpu_percent']:>6.1f} {proc['memory_percent']:>6.1f}")
            
            output = "\n".join(lines)
            
            return ToolResult(
                success=True,
                output=output,
                metadata={
                    "total_processes": len(processes),
                    "filter": filter,
                    "sort_by": sort_by
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Process listing error: {str(e)}"
            )
    
    def _network_scan(self, scan_type: str = "connections") -> ToolResult:
        """Scansione rete"""
        try:
            if scan_type == "connections":
                connections = psutil.net_connections()
                
                lines = ["PROTO LOCAL_ADDR          REMOTE_ADDR         STATUS"]
                lines.append("-" * 60)
                
                for conn in connections[:20]:  # Top 20
                    local = f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else "N/A"
                    remote = f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A"
                    lines.append(f"{conn.type.name:<5} {local:<18} {remote:<18} {conn.status}")
                
                output = "\n".join(lines)
                
            elif scan_type == "ports":
                # Simple port scan (listening ports only)
                listening_ports = []
                for conn in psutil.net_connections():
                    if conn.status == 'LISTEN' and conn.laddr:
                        listening_ports.append(conn.laddr.port)
                
                listening_ports = sorted(set(listening_ports))
                output = f"Listening ports: {', '.join(map(str, listening_ports))}"
                
            else:
                output = "Unsupported scan type"
            
            return ToolResult(
                success=True,
                output=output,
                metadata={
                    "scan_type": scan_type,
                    "timestamp": time.time()
                }
            )
            
        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Network scan error: {str(e)}"
            )

    def _scan_disk(self, drive: str = "C", scan_type: str = "usage", depth: int = 2) -> ToolResult:
        """Scansione disco avanzata"""
        try:
            drive = drive.upper().rstrip(':')
            drive_path = f"{drive}:\\"

            if not os.path.exists(drive_path):
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Drive {drive}: not found"
                )

            result_data = {}

            if scan_type in ["usage", "all"]:
                # Disk usage analysis
                usage = psutil.disk_usage(drive_path)
                result_data["disk_usage"] = {
                    "drive": drive,
                    "total_gb": round(usage.total / (1024**3), 2),
                    "used_gb": round(usage.used / (1024**3), 2),
                    "free_gb": round(usage.free / (1024**3), 2),
                    "usage_percent": round((usage.used / usage.total) * 100, 1)
                }

            if scan_type in ["health", "all"]:
                # Disk health check (basic)
                try:
                    # Check disk errors using chkdsk (read-only)
                    chkdsk_result = subprocess.run(
                        ["chkdsk", f"{drive}:", "/scan"],
                        capture_output=True,
                        text=True,
                        timeout=30,
                        creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
                    )

                    result_data["disk_health"] = {
                        "chkdsk_available": chkdsk_result.returncode == 0,
                        "scan_output": chkdsk_result.stdout[:500] if chkdsk_result.stdout else "No output"
                    }
                except:
                    result_data["disk_health"] = {
                        "chkdsk_available": False,
                        "error": "Could not run disk health check"
                    }

            if scan_type in ["large_files", "all"]:
                # Find large files
                large_files = []
                try:
                    for root, dirs, files in os.walk(drive_path):
                        # Limit depth
                        level = root.replace(drive_path, '').count(os.sep)
                        if level >= depth:
                            dirs[:] = []  # Don't go deeper
                            continue

                        for file in files:
                            try:
                                file_path = os.path.join(root, file)
                                size = os.path.getsize(file_path)

                                # Files larger than 100MB
                                if size > 100 * 1024 * 1024:
                                    large_files.append({
                                        "path": file_path,
                                        "size_mb": round(size / (1024**2), 1),
                                        "size_gb": round(size / (1024**3), 2)
                                    })

                                # Limit results
                                if len(large_files) >= 20:
                                    break
                            except (OSError, PermissionError):
                                continue

                        if len(large_files) >= 20:
                            break

                    # Sort by size
                    large_files.sort(key=lambda x: x["size_mb"], reverse=True)
                    result_data["large_files"] = large_files[:10]  # Top 10

                except Exception as e:
                    result_data["large_files_error"] = str(e)

            if scan_type in ["temp_files", "all"]:
                # Analyze temp files
                temp_locations = [
                    os.path.join(drive_path, "Windows", "Temp"),
                    os.path.join(drive_path, "Users", os.getenv("USERNAME", ""), "AppData", "Local", "Temp"),
                    os.path.join(drive_path, "Temp")
                ]

                temp_analysis = []
                total_temp_size = 0

                for temp_dir in temp_locations:
                    if os.path.exists(temp_dir):
                        try:
                            dir_size = 0
                            file_count = 0

                            for root, dirs, files in os.walk(temp_dir):
                                for file in files:
                                    try:
                                        file_path = os.path.join(root, file)
                                        size = os.path.getsize(file_path)
                                        dir_size += size
                                        file_count += 1
                                    except (OSError, PermissionError):
                                        continue

                            temp_analysis.append({
                                "location": temp_dir,
                                "size_mb": round(dir_size / (1024**2), 1),
                                "file_count": file_count
                            })
                            total_temp_size += dir_size

                        except Exception as e:
                            temp_analysis.append({
                                "location": temp_dir,
                                "error": str(e)
                            })

                result_data["temp_files"] = {
                    "locations": temp_analysis,
                    "total_size_mb": round(total_temp_size / (1024**2), 1),
                    "total_size_gb": round(total_temp_size / (1024**3), 2)
                }

            # Format output
            output_lines = []

            if "disk_usage" in result_data:
                usage = result_data["disk_usage"]
                output_lines.extend([
                    f"=== DISK USAGE ANALYSIS - Drive {usage['drive']}: ===",
                    f"Total Space: {usage['total_gb']} GB",
                    f"Used Space:  {usage['used_gb']} GB ({usage['usage_percent']}%)",
                    f"Free Space:  {usage['free_gb']} GB",
                    ""
                ])

            if "large_files" in result_data:
                output_lines.append("=== LARGE FILES (>100MB) ===")
                for file_info in result_data["large_files"]:
                    output_lines.append(f"{file_info['size_gb']} GB - {file_info['path']}")
                output_lines.append("")

            if "temp_files" in result_data:
                temp_info = result_data["temp_files"]
                output_lines.extend([
                    "=== TEMPORARY FILES ANALYSIS ===",
                    f"Total Temp Size: {temp_info['total_size_gb']} GB ({temp_info['total_size_mb']} MB)",
                    ""
                ])
                for location in temp_info["locations"]:
                    if "error" not in location:
                        output_lines.append(f"{location['size_mb']} MB ({location['file_count']} files) - {location['location']}")
                output_lines.append("")

            if "disk_health" in result_data:
                health = result_data["disk_health"]
                output_lines.append("=== DISK HEALTH ===")
                if health["chkdsk_available"]:
                    output_lines.append("✅ Disk health check available")
                else:
                    output_lines.append("⚠️  Disk health check not available")
                output_lines.append("")

            output = "\n".join(output_lines)

            return ToolResult(
                success=True,
                output=output,
                metadata={
                    "drive": drive,
                    "scan_type": scan_type,
                    "data": result_data
                }
            )

        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Disk scan error: {str(e)}"
            )

    @retry_on_failure(max_retries=1, delay=1.0)
    @validate_parameters(required_params=[])
    @security_check(risk_level="MEDIUM")
    def _cleanup_temp_files(self, cleanup_type: str = "temp", dry_run: bool = True, drive: str = "C", preview: bool = None) -> ToolResult:
        """Pulizia sicura file temporanei"""
        try:
            # Handle preview parameter (legacy compatibility)
            if preview is not None:
                dry_run = preview

            drive = drive.upper().rstrip(':')
            drive_path = f"{drive}:\\"

            if not os.path.exists(drive_path):
                return ToolResult(
                    success=False,
                    output="",
                    error=f"Drive {drive}: not found"
                )

            cleanup_locations = []

            if cleanup_type in ["temp", "all"]:
                # Standard temp locations
                temp_dirs = [
                    os.path.join(drive_path, "Windows", "Temp"),
                    os.path.join(drive_path, "Temp"),
                    os.path.join(os.getenv("TEMP", ""), ""),
                    os.path.join(os.getenv("TMP", ""), "")
                ]

                # User temp directories
                users_dir = os.path.join(drive_path, "Users")
                if os.path.exists(users_dir):
                    for user_dir in os.listdir(users_dir):
                        user_temp = os.path.join(users_dir, user_dir, "AppData", "Local", "Temp")
                        if os.path.exists(user_temp):
                            temp_dirs.append(user_temp)

                cleanup_locations.extend([("temp", d) for d in temp_dirs if os.path.exists(d)])

            if cleanup_type in ["cache", "all"]:
                # Browser cache locations
                cache_dirs = []
                users_dir = os.path.join(drive_path, "Users")
                if os.path.exists(users_dir):
                    for user_dir in os.listdir(users_dir):
                        user_path = os.path.join(users_dir, user_dir)

                        # Chrome cache
                        chrome_cache = os.path.join(user_path, "AppData", "Local", "Google", "Chrome", "User Data", "Default", "Cache")
                        if os.path.exists(chrome_cache):
                            cache_dirs.append(chrome_cache)

                        # Firefox cache
                        firefox_cache = os.path.join(user_path, "AppData", "Local", "Mozilla", "Firefox", "Profiles")
                        if os.path.exists(firefox_cache):
                            cache_dirs.append(firefox_cache)

                        # Edge cache
                        edge_cache = os.path.join(user_path, "AppData", "Local", "Microsoft", "Edge", "User Data", "Default", "Cache")
                        if os.path.exists(edge_cache):
                            cache_dirs.append(edge_cache)

                cleanup_locations.extend([("cache", d) for d in cache_dirs])

            if cleanup_type in ["recycle", "all"]:
                # Recycle bin
                recycle_bin = os.path.join(drive_path, "$Recycle.Bin")
                if os.path.exists(recycle_bin):
                    cleanup_locations.append(("recycle", recycle_bin))

            # Perform cleanup analysis/execution
            results = []
            total_size_freed = 0
            total_files_processed = 0

            for location_type, location_path in cleanup_locations:
                try:
                    if not os.path.exists(location_path):
                        continue

                    location_size = 0
                    files_processed = 0
                    files_deleted = 0
                    errors = 0

                    # Analyze/clean location
                    for root, dirs, files in os.walk(location_path):
                        for file in files:
                            try:
                                file_path = os.path.join(root, file)

                                # Skip files in use or system files
                                if self._is_file_safe_to_delete(file_path, location_type):
                                    file_size = os.path.getsize(file_path)
                                    location_size += file_size
                                    files_processed += 1

                                    if not dry_run:
                                        try:
                                            os.remove(file_path)
                                            files_deleted += 1
                                        except (OSError, PermissionError):
                                            errors += 1

                            except (OSError, PermissionError):
                                errors += 1
                                continue

                    # Clean empty directories (if not dry run)
                    if not dry_run:
                        try:
                            for root, dirs, files in os.walk(location_path, topdown=False):
                                for dir_name in dirs:
                                    dir_path = os.path.join(root, dir_name)
                                    try:
                                        if not os.listdir(dir_path):  # Empty directory
                                            os.rmdir(dir_path)
                                    except (OSError, PermissionError):
                                        pass
                        except:
                            pass

                    results.append({
                        "location": location_path,
                        "type": location_type,
                        "size_mb": round(location_size / (1024**2), 1),
                        "files_found": files_processed,
                        "files_deleted": files_deleted if not dry_run else 0,
                        "errors": errors
                    })

                    total_size_freed += location_size
                    total_files_processed += files_processed

                except Exception as e:
                    results.append({
                        "location": location_path,
                        "type": location_type,
                        "error": str(e)
                    })

            # Format output
            output_lines = []

            if dry_run:
                output_lines.append("=== CLEANUP PREVIEW (DRY RUN) ===")
                output_lines.append("⚠️  No files were actually deleted. This is a preview.")
            else:
                output_lines.append("=== CLEANUP RESULTS ===")
                output_lines.append("✅ Files have been deleted.")

            output_lines.extend([
                "",
                f"Total Size to Free: {round(total_size_freed / (1024**2), 1)} MB ({round(total_size_freed / (1024**3), 2)} GB)",
                f"Total Files Found: {total_files_processed}",
                ""
            ])

            for result in results:
                if "error" not in result:
                    status = "PREVIEW" if dry_run else f"DELETED {result['files_deleted']}"
                    output_lines.append(f"[{result['type'].upper()}] {result['size_mb']} MB ({result['files_found']} files) - {status}")
                    output_lines.append(f"  📁 {result['location']}")
                    if result['errors'] > 0:
                        output_lines.append(f"  ⚠️  {result['errors']} files could not be processed")
                else:
                    output_lines.append(f"[ERROR] {result['location']}: {result['error']}")
                output_lines.append("")

            if dry_run:
                output_lines.extend([
                    "💡 To actually delete files, use: dry_run=false",
                    "⚠️  CAUTION: Always review the preview before actual deletion!"
                ])

            output = "\n".join(output_lines)

            return ToolResult(
                success=True,
                output=output,
                metadata={
                    "cleanup_type": cleanup_type,
                    "dry_run": dry_run,
                    "total_size_mb": round(total_size_freed / (1024**2), 1),
                    "total_files": total_files_processed,
                    "results": results
                }
            )

        except Exception as e:
            return ToolResult(
                success=False,
                output="",
                error=f"Cleanup error: {str(e)}"
            )

    def _is_file_safe_to_delete(self, file_path: str, location_type: str) -> bool:
        """Verifica se un file è sicuro da eliminare"""
        try:
            # Skip files currently in use
            try:
                with open(file_path, 'r+b'):
                    pass
            except (PermissionError, OSError):
                return False  # File in use or protected

            # Skip system files
            if os.name == 'nt':
                import stat
                file_attrs = os.stat(file_path).st_file_attributes if hasattr(os.stat(file_path), 'st_file_attributes') else 0
                if file_attrs & stat.FILE_ATTRIBUTE_SYSTEM:
                    return False

            # Skip recent files (less than 1 hour old) for temp files
            if location_type == "temp":
                file_age = time.time() - os.path.getmtime(file_path)
                if file_age < 3600:  # Less than 1 hour
                    return False

            # Skip important file extensions
            dangerous_extensions = ['.exe', '.dll', '.sys', '.ini', '.cfg', '.reg']
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext in dangerous_extensions:
                return False

            return True

        except Exception:
            return False  # If in doubt, don't delete

    def _register_enterprise_tools(self):
        """Registra tool enterprise"""
        try:
            from .enterprise_integration import EnterpriseTools

            # Initialize enterprise tools
            self.enterprise_tools = EnterpriseTools(self.log_manager)

            # Register security audit
            self.register_tool(
                name="security_audit",
                func=self._security_audit,
                description="Comprehensive security audit of the system",
                tool_type=ToolType.CUSTOM
            )

            # Register performance analysis
            self.register_tool(
                name="performance_analysis",
                func=self._performance_analysis,
                description="Analyze system performance metrics",
                tool_type=ToolType.CUSTOM
            )

            # Register network security scan
            self.register_tool(
                name="network_security_scan",
                func=self._network_security_scan,
                description="Scan network for security issues",
                tool_type=ToolType.CUSTOM
            )

            # Register enterprise health check
            self.register_tool(
                name="enterprise_health_check",
                func=self._enterprise_health_check,
                description="Complete enterprise system health check",
                tool_type=ToolType.CUSTOM
            )

            # Register system hardening
            self.register_tool(
                name="system_hardening",
                func=self._system_hardening,
                description="Apply security hardening recommendations",
                tool_type=ToolType.CUSTOM
            )

            # Register backup analysis
            self.register_tool(
                name="backup_analysis",
                func=self._backup_analysis,
                description="Analyze backup and recovery capabilities",
                tool_type=ToolType.CUSTOM
            )

            if self.logger:
                self.logger.info("Registered 6 enterprise tools")

        except ImportError as e:
            if self.logger:
                self.logger.warning(f"Enterprise tools not available: {e}")
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to register enterprise tools: {e}")

    def _security_audit(self, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui security audit"""
        try:
            result = self.enterprise_tools.execute_tool("security_audit", parameters or {})

            if result.get('success'):
                audit_data = result.get('result', {})

                output = "=== SECURITY AUDIT RESULTS ===\n\n"
                output += f"Total Checks: {audit_data.get('total_checks', 0)}\n"
                output += f"🔴 Critical Issues: {audit_data.get('critical_issues', 0)}\n"
                output += f"🟡 Warnings: {audit_data.get('warnings', 0)}\n"
                output += f"🟢 Secure Items: {audit_data.get('secure_items', 0)}\n\n"

                # Show critical issues
                for item in audit_data.get('audit_results', []):
                    if item.get('status') == 'critical':
                        output += f"🔴 {item.get('category')}: {item.get('description')}\n"
                        output += f"   Recommendation: {item.get('recommendation')}\n\n"

                return ToolResult(success=True, output=output, metadata=result)
            else:
                return ToolResult(success=False, output="", error=result.get('error', 'Security audit failed'))

        except Exception as e:
            return ToolResult(success=False, output="", error=f"Security audit error: {str(e)}")

    def _performance_analysis(self, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui performance analysis"""
        try:
            result = self.enterprise_tools.execute_tool("performance_analysis", parameters or {})

            if result.get('success'):
                perf_data = result.get('result', {})

                output = "=== PERFORMANCE ANALYSIS ===\n\n"
                output += f"🔴 Critical Metrics: {perf_data.get('critical_metrics', 0)}\n"
                output += f"🟡 Warning Metrics: {perf_data.get('warning_metrics', 0)}\n"
                output += f"🟢 Good Metrics: {perf_data.get('good_metrics', 0)}\n\n"

                # Show metrics
                for metric in perf_data.get('metrics', []):
                    status_icon = "🔴" if metric.get("status") == "critical" else "🟡" if metric.get("status") == "warning" else "🟢"
                    output += f"{status_icon} {metric.get('name')}: {metric.get('value', 0):.1f}{metric.get('unit', '')}\n"

                return ToolResult(success=True, output=output, metadata=result)
            else:
                return ToolResult(success=False, output="", error=result.get('error', 'Performance analysis failed'))

        except Exception as e:
            return ToolResult(success=False, output="", error=f"Performance analysis error: {str(e)}")

    def _network_security_scan(self, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui network security scan"""
        try:
            result = self.enterprise_tools.execute_tool("network_security_scan", parameters or {})

            if result.get('success'):
                network_data = result.get('result', {})

                output = "=== NETWORK SECURITY SCAN ===\n\n"
                output += f"Open Ports: {len(network_data.get('open_ports', []))}\n"
                output += f"Security Issues: {len(network_data.get('security_issues', []))}\n\n"

                # Show security issues
                for issue in network_data.get('security_issues', []):
                    output += f"🔴 {issue.get('description')}\n"
                    output += f"   Recommendation: {issue.get('recommendation')}\n\n"

                # Show some ports
                output += "📡 OPEN PORTS:\n"
                for port in network_data.get('open_ports', [])[:10]:
                    output += f"• Port {port.get('port')}: {port.get('process', 'Unknown')}\n"

                return ToolResult(success=True, output=output, metadata=result)
            else:
                return ToolResult(success=False, output="", error=result.get('error', 'Network scan failed'))

        except Exception as e:
            return ToolResult(success=False, output="", error=f"Network scan error: {str(e)}")

    def _enterprise_health_check(self, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui enterprise health check"""
        try:
            result = self.enterprise_tools.execute_tool("enterprise_health_check", parameters or {})

            if result.get('success'):
                health_data = result.get('result', {})

                overall_status = health_data.get('overall_status', 'unknown')
                status_icon = "🔴" if overall_status == "critical" else "🟡" if overall_status == "warning" else "🟢"

                output = "=== ENTERPRISE HEALTH CHECK ===\n\n"
                output += f"Overall Status: {status_icon} {overall_status.upper()}\n\n"

                # Show components
                for comp_name, comp_data in health_data.get('components', {}).items():
                    comp_status = comp_data.get('status', 'unknown')
                    comp_icon = "🔴" if comp_status == "critical" else "🟡" if comp_status == "warning" else "🟢"
                    output += f"{comp_icon} {comp_name.title()}: {comp_status}\n"

                return ToolResult(success=True, output=output, metadata=result)
            else:
                return ToolResult(success=False, output="", error=result.get('error', 'Health check failed'))

        except Exception as e:
            return ToolResult(success=False, output="", error=f"Health check error: {str(e)}")

    def _system_hardening(self, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui system hardening analysis"""
        try:
            result = self.enterprise_tools.execute_tool("system_hardening", parameters or {})

            if result.get('success'):
                hardening_data = result.get('result', {})

                output = "=== SYSTEM HARDENING RECOMMENDATIONS ===\n\n"

                priority_actions = hardening_data.get('priority_actions', [])
                if priority_actions:
                    output += "🔥 PRIORITY ACTIONS:\n"
                    for action in priority_actions:
                        output += f"• {action}\n"
                    output += "\n"

                recommendations = hardening_data.get('recommendations', [])
                if recommendations:
                    output += "📋 ALL RECOMMENDATIONS:\n"
                    for i, rec in enumerate(recommendations[:10], 1):  # Show first 10
                        output += f"{i:2d}. {rec}\n"

                return ToolResult(success=True, output=output, metadata=result)
            else:
                return ToolResult(success=False, output="", error=result.get('error', 'Hardening analysis failed'))

        except Exception as e:
            return ToolResult(success=False, output="", error=f"Hardening analysis error: {str(e)}")

    def _backup_analysis(self, parameters: Dict[str, Any] = None) -> ToolResult:
        """Esegui backup analysis"""
        try:
            result = self.enterprise_tools.execute_tool("backup_analysis", parameters or {})

            if result.get('success'):
                backup_data = result.get('result', {})

                output = "=== BACKUP ANALYSIS ===\n\n"

                backup_locations = backup_data.get('backup_locations', [])
                if backup_locations:
                    output += "📁 IMPORTANT BACKUP LOCATIONS:\n"
                    for location in backup_locations:
                        priority_icon = "🔴" if location.get("priority") == "critical" else "🟡" if location.get("priority") == "high" else "🟢"
                        output += f"{priority_icon} {location.get('path')} - Priority: {location.get('priority')}\n"
                    output += "\n"

                recommendations = backup_data.get('recommendations', [])
                if recommendations:
                    output += "💡 BACKUP RECOMMENDATIONS:\n"
                    for i, rec in enumerate(recommendations, 1):
                        output += f"{i}. {rec}\n"

                return ToolResult(success=True, output=output, metadata=result)
            else:
                return ToolResult(success=False, output="", error=result.get('error', 'Backup analysis failed'))

        except Exception as e:
            return ToolResult(success=False, output="", error=f"Backup analysis error: {str(e)}")
