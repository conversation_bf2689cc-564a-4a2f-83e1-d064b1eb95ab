#!/usr/bin/env python3
"""
Interactive SVP Test
Test delle funzionalità SVP di Cybex in modo interattivo
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

from cybex.core.cybex_core import Cybex<PERSON>ore
from cybex.modules.command_executor import CommandExecutor
from cybex.modules.ollama_interface import OllamaInterface


def test_system_status():
    """Test system status completo"""
    print("🔍 Testing System Status...")
    
    try:
        # Initialize components
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, ollama_interface)
        
        # Start session
        core.start_session("test_user")
        
        # Get comprehensive status
        status = core.get_system_status()
        
        print(f"\n📊 System Status Report:")
        print(f"  Platform: {status.get('platform', 'Unknown')}")
        print(f"  Python: {status.get('python_version', 'Unknown')}")
        print(f"  Mode: {status.get('mode', 'Unknown')}")
        print(f"  Session Active: {status.get('session_active', False)}")
        
        # SVP Features Status
        if 'monitoring' in status:
            mon = status['monitoring']
            print(f"\n📈 System Monitoring:")
            print(f"  Enabled: {mon['enabled']}")
            if mon['current_state']:
                cpu = mon['current_state'].get('cpu', {}).get('percent', 0)
                memory = mon['current_state'].get('memory', {}).get('percent', 0)
                disk = mon['current_state'].get('disk', {}).get('percent', 0)
                print(f"  CPU: {cpu:.1f}%")
                print(f"  Memory: {memory:.1f}%")
                print(f"  Disk: {disk:.1f}%")
            print(f"  Active Alerts: {mon['alerts']}")
        
        if 'disk_health' in status:
            disk = status['disk_health']
            print(f"\n💾 Disk Health:")
            print(f"  Total Disks: {disk['total_disks']}")
            print(f"  Healthy: {disk['healthy_disks']}")
            print(f"  Warning: {disk['warning_disks']}")
            print(f"  Critical: {disk['critical_disks']}")
            print(f"  Total Space: {disk['total_space_gb']:.1f} GB")
            print(f"  Average Usage: {disk['average_usage_percent']:.1f}%")
        
        if 'database' in status:
            db = status['database']
            print(f"\n🗄️  Database:")
            print(f"  File Size: {db.get('file_size_mb', 0):.2f} MB")
            print(f"  User Profiles: {db.get('user_profiles_count', 0)}")
            print(f"  Sessions: {db.get('session_history_count', 0)}")
            print(f"  Commands: {db.get('command_history_count', 0)}")
        
        if 'agent' in status:
            agent = status['agent']
            metrics = agent['metrics']
            print(f"\n🤖 Agent Status:")
            print(f"  Total Executions: {metrics['total_executions']}")
            print(f"  Successful: {metrics['successful_executions']}")
            print(f"  Failed: {metrics['failed_executions']}")
            if metrics['total_executions'] > 0:
                success_rate = (metrics['successful_executions'] / metrics['total_executions']) * 100
                print(f"  Success Rate: {success_rate:.1f}%")
            print(f"  Active Executions: {agent['active_executions']}")
        
        # End session
        core.end_session()
        
        return True
        
    except Exception as e:
        print(f"❌ System status test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_system_monitoring():
    """Test system monitoring features"""
    print("\n📊 Testing System Monitoring...")
    
    try:
        core = CybexCore()
        
        if not core.system_monitor:
            print("⚠️  System monitoring not available")
            return False
        
        # Get current state
        current_state = core.system_monitor.get_current_state()
        print(f"✅ Current state retrieved")
        
        if current_state:
            print(f"  Timestamp: {current_state.get('timestamp', 'Unknown')}")
            
            cpu = current_state.get('cpu', {})
            if cpu:
                print(f"  CPU: {cpu.get('percent', 0):.1f}% ({cpu.get('count', 0)} cores)")
            
            memory = current_state.get('memory', {})
            if memory:
                used_gb = memory.get('used', 0) / (1024**3)
                total_gb = memory.get('total', 0) / (1024**3)
                print(f"  Memory: {memory.get('percent', 0):.1f}% ({used_gb:.1f}/{total_gb:.1f} GB)")
            
            processes = current_state.get('processes', 0)
            print(f"  Processes: {processes}")
        
        # Get performance summary
        summary = core.system_monitor.get_performance_summary()
        if summary:
            print(f"✅ Performance summary:")
            print(f"  Uptime: {summary.get('uptime', 'Unknown')}")
            print(f"  Active Alerts: {summary.get('alerts', 0)}")
        
        # Get alerts
        alerts = core.system_monitor.get_alerts()
        print(f"✅ Alerts: {len(alerts)} active")
        
        for alert in alerts[:3]:  # Show first 3 alerts
            print(f"  • [{alert.level.value.upper()}] {alert.message}")
        
        return True
        
    except Exception as e:
        print(f"❌ System monitoring test failed: {e}")
        return False


def test_disk_management():
    """Test disk management features"""
    print("\n💾 Testing Disk Management...")
    
    try:
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        
        # Initialize disk manager
        core._init_agent_modules(command_executor, None)
        
        if not core.disk_manager:
            print("⚠️  Disk management not available")
            return False
        
        # Get disk info
        disk_info = core.disk_manager.get_disk_info()
        print(f"✅ Disk info: {len(disk_info)} disks found")
        
        for device, info in list(disk_info.items())[:3]:  # Show first 3 disks
            print(f"  {device}: {info.get('percent', 0):.1f}% used, "
                  f"{info.get('free_gb', 0):.1f} GB free")
        
        # Get health summary
        health_summary = core.disk_manager.get_disk_health_summary()
        print(f"✅ Health summary:")
        print(f"  Total: {health_summary['total_disks']} disks")
        print(f"  Healthy: {health_summary['healthy_disks']}")
        print(f"  Warning: {health_summary['warning_disks']}")
        print(f"  Critical: {health_summary['critical_disks']}")
        print(f"  Total Space: {health_summary['total_space_gb']:.1f} GB")
        
        # Test cleanup scan (dry run)
        from cybex.modules.disk_manager import CleanupCategory
        cleanup_data = core.disk_manager.scan_for_cleanup([
            CleanupCategory.TEMP_FILES,
            CleanupCategory.LOG_FILES
        ])
        
        total_files = sum(data['total_count'] for data in cleanup_data.values())
        total_size = sum(data['estimated_space_gb'] for data in cleanup_data.values())
        
        print(f"✅ Cleanup scan:")
        print(f"  Files found: {total_files}")
        print(f"  Space to free: {total_size:.2f} GB")
        
        return True
        
    except Exception as e:
        print(f"❌ Disk management test failed: {e}")
        return False


def test_database_operations():
    """Test database operations"""
    print("\n🗄️  Testing Database Operations...")
    
    try:
        core = CybexCore()
        
        if not core.database_manager:
            print("⚠️  Database not available")
            return False
        
        # Test user profile operations
        username = f"test_user_{int(time.time())}"
        success = core.database_manager.create_user_profile(
            username,
            "Test User",
            {"theme": "dark", "notifications": True}
        )
        print(f"✅ User profile created: {success}")
        
        # Get user profile
        profile = core.database_manager.get_user_profile(username)
        if profile:
            print(f"✅ User profile retrieved: {profile['display_name']}")
            print(f"  Preferences: {len(profile.get('preferences', {}))}")
        
        # Test session operations
        session_id = f"test_session_{int(time.time())}"
        success = core.database_manager.create_session(session_id, username, "test")
        print(f"✅ Session created: {success}")
        
        # End session
        success = core.database_manager.end_session(session_id, 5, {"test": True})
        print(f"✅ Session ended: {success}")
        
        # Get database stats
        stats = core.database_manager.get_database_stats()
        print(f"✅ Database stats:")
        print(f"  File size: {stats.get('file_size_mb', 0):.2f} MB")
        print(f"  User profiles: {stats.get('user_profiles_count', 0)}")
        print(f"  Sessions: {stats.get('session_history_count', 0)}")
        print(f"  Commands: {stats.get('command_history_count', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Database operations test failed: {e}")
        return False


def test_agent_planning():
    """Test agent planning capabilities"""
    print("\n🤖 Testing Agent Planning...")
    
    try:
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, ollama_interface)
        
        if not core.agent_planner:
            print("⚠️  Agent planner not available")
            return False
        
        # Create a test plan
        goal = "Check system status and show basic information"
        context = {
            'system_info': 'Windows 11',
            'username': 'test_user'
        }
        
        print(f"Creating plan for goal: {goal}")
        plan_id = core.create_agent_plan(goal, context)
        
        if plan_id:
            print(f"✅ Plan created: {plan_id[:8]}...")
            
            # Get plan details
            plan = core.agent_planner.get_plan(plan_id)
            if plan:
                print(f"✅ Plan details:")
                print(f"  Name: {plan.name}")
                print(f"  Tasks: {len(plan.tasks)}")
                print(f"  Risk Level: {plan.risk_level}")
                print(f"  Estimated Duration: {plan.estimated_duration // 60} minutes")
                
                # Show first few tasks
                for i, task in enumerate(plan.tasks[:3]):
                    print(f"  Task {i+1}: {task.name}")
                    if task.command and not task.command.startswith('#'):
                        print(f"    Command: {task.command}")
            
            return True
        else:
            print("❌ Failed to create plan")
            return False
        
    except Exception as e:
        print(f"❌ Agent planning test failed: {e}")
        return False


def test_system_optimization():
    """Test system optimization"""
    print("\n⚡ Testing System Optimization...")
    
    try:
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        
        # Initialize modules
        core._init_agent_modules(command_executor, None)
        
        # Get recommendations
        recommendations = core.get_system_recommendations()
        print(f"✅ Recommendations: {len(recommendations)} items")
        
        for rec in recommendations[:3]:  # Show first 3
            print(f"  • [{rec.get('priority', 'medium').upper()}] {rec.get('message', 'No message')}")
        
        # Perform optimization (safe operations only)
        print("Performing system optimization...")
        result = core.perform_system_optimization()
        
        print(f"✅ Optimization result: {'Success' if result['success'] else 'Failed'}")
        
        if result['optimizations']:
            for opt in result['optimizations']:
                print(f"  • {opt['type']}: completed")
        else:
            print("  • No optimizations needed")
        
        if result['errors']:
            print(f"  Errors: {len(result['errors'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ System optimization test failed: {e}")
        return False


def main():
    """Run interactive SVP tests"""
    print("🎯 Cybex SVP Interactive Testing")
    print("=" * 40)
    
    tests = [
        ("System Status", test_system_status),
        ("System Monitoring", test_system_monitoring),
        ("Disk Management", test_disk_management),
        ("Database Operations", test_database_operations),
        ("Agent Planning", test_agent_planning),
        ("System Optimization", test_system_optimization),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All SVP features are working correctly!")
        print("Cybex SVP is ready for production use.")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
