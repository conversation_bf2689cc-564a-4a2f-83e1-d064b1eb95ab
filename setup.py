"""
Setup script for Cybex - Cybernetic Expert Agent
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README file
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# Read requirements
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="cybex",
    version="1.0.0",
    author="Cybex Development Team",
    author_email="<EMAIL>",
    description="Cybernetic Expert Agent - Local AI System Administrator",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/cybex-ai/cybex",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: System Administrators",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: System :: Systems Administration",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    python_requires=">=3.11",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.2",
            "pytest-cov>=4.1.0",
            "black>=23.7.0",
            "flake8>=6.0.0",
            "mypy>=1.5.0",
        ],
        "enterprise": [
            "python-virustotal-api>=1.1.11",
            "paramiko>=3.3.1",
            "customtkinter>=5.2.0",
        ]
    },
    entry_points={
        "console_scripts": [
            "cybex=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "cybex": [
            "config/*.yaml",
            "docs/*.md",
        ],
    },
    zip_safe=False,
    keywords="ai, system-administration, automation, cybersecurity, ollama, gemma",
    project_urls={
        "Bug Reports": "https://github.com/cybex-ai/cybex/issues",
        "Source": "https://github.com/cybex-ai/cybex",
        "Documentation": "https://cybex-ai.github.io/cybex",
    },
)
