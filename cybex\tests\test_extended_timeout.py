#!/usr/bin/env python3
"""
Test Extended Timeout
Test dei timeout estesi per modelli grandi
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_timeout_calculation():
    """Test timeout calculation for different models"""
    print("⏱️  Testing Timeout Calculation")
    print("=" * 40)
    
    try:
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.core.cybex_core import CybexCore
        
        # Initialize components
        core = CybexCore()
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Test models with their expected timeouts
        test_models = [
            ("devstral:24b", "Very Large Model"),
            ("gemma3:27b", "Very Large Model"),
            ("deepseek-r1:8b", "Large Model"),
            ("gemma3:4b", "Medium Model"),
            ("deepseek-r1:1.5b", "Small Model"),
            ("AGtech:latest", "Unknown Size"),
            ("SocialAI:latest", "Unknown Size")
        ]
        
        print("Model timeout calculations:")
        print()
        
        for model, description in test_models:
            # Temporarily set model to test timeout calculation
            original_model = ollama_interface.model
            ollama_interface.model = model
            
            # Recalculate timeout
            base_timeout = 180
            calculated_timeout = ollama_interface._calculate_model_timeout(base_timeout)
            
            # Restore original model
            ollama_interface.model = original_model
            
            minutes = calculated_timeout // 60
            seconds = calculated_timeout % 60
            
            print(f"  {model:<20} ({description})")
            print(f"    Timeout: {calculated_timeout}s ({minutes}m {seconds}s)")
            print()
        
        print("✅ Timeout calculation working")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_timeout():
    """Test configuration timeout settings"""
    print(f"\n⚙️  Testing Configuration Timeout")
    print("=" * 40)
    
    try:
        from cybex.core.cybex_core import CybexCore
        
        core = CybexCore()
        
        # Check base timeout from config
        base_timeout = core.config_manager.get('ollama.timeout', 30)
        max_tokens = core.config_manager.get('ollama.max_tokens', 3000)
        current_model = core.config_manager.get('ollama.model', 'gemma3:4b')
        
        print(f"Configuration settings:")
        print(f"  Base timeout: {base_timeout}s ({base_timeout//60}m {base_timeout%60}s)")
        print(f"  Max tokens: {max_tokens}")
        print(f"  Current model: {current_model}")
        
        if base_timeout >= 180:
            print("✅ Extended timeout configured")
        else:
            print("⚠️  Short timeout - may cause issues with large models")
        
        if max_tokens >= 4000:
            print("✅ Extended token limit configured")
        else:
            print("⚠️  Low token limit - may truncate responses")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_enterprise_ui_timeout_display():
    """Test Enterprise UI timeout display"""
    print(f"\n🎯 Testing Enterprise UI Timeout Display")
    print("=" * 50)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        
        # Test timeout calculation for different models
        test_models = [
            "devstral:24b",
            "gemma3:27b", 
            "deepseek-r1:8b",
            "gemma3:4b",
            "deepseek-r1:1.5b"
        ]
        
        print("Enterprise UI timeout display:")
        print()
        
        for model in test_models:
            timeout = ui._get_model_timeout(model)
            minutes = timeout // 60
            
            print(f"  {model:<20} → {timeout}s ({minutes} minutes)")
            
            if timeout > 300:
                print(f"    Would show: 'Large model - may take up to {minutes} minutes'")
            elif timeout > 120:
                print(f"    Would show: 'Processing may take up to {minutes} minutes'")
            else:
                print(f"    Would show: No special timeout message")
            print()
        
        print("✅ Enterprise UI timeout display working")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_actual_model_response():
    """Test actual model response with extended timeout"""
    print(f"\n🤖 Testing Actual Model Response")
    print("=" * 40)
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.ollama_interface import OllamaInterface
        
        # Initialize components
        core = CybexCore()
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Get current model
        current_model = core.config_manager.get('ollama.model', 'gemma3:4b')
        timeout = ollama_interface._calculate_model_timeout(180)
        
        print(f"Testing with model: {current_model}")
        print(f"Calculated timeout: {timeout}s ({timeout//60}m {timeout%60}s)")
        
        # Simple test message
        test_message = "Hello! Please respond with just 'Hi there!' and nothing else."
        
        print(f"Sending test message: '{test_message}'")
        print("⏳ Waiting for response...")
        
        start_time = time.time()
        
        # Prepare context
        context = {
            'mode': 'chat',
            'system_type': 'Windows'
        }
        
        response = ollama_interface.generate_response(test_message, context)
        
        elapsed_time = time.time() - start_time
        
        if response.success:
            print(f"✅ Response received in {elapsed_time:.2f}s")
            print(f"📝 Response: '{response.content.strip()}'")
            
            if elapsed_time < timeout:
                print(f"✅ Response within timeout limit")
            else:
                print(f"⚠️  Response took longer than expected")
            
            return True
        else:
            print(f"❌ Failed: {response.error}")
            
            if "timed out" in response.error.lower():
                print(f"💡 Timeout occurred - consider:")
                print(f"   • Using a smaller model")
                print(f"   • Increasing timeout further")
                print(f"   • Checking Ollama server performance")
            
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def show_timeout_recommendations():
    """Show timeout recommendations for different models"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    recommendations = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                      {SunriseColors.ACCENT}⏱️  TIMEOUT RECOMMENDATIONS{SunriseColors.SUCCESS}                               ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🤖 MODEL TIMEOUT SETTINGS:{SunriseColors.SUCCESS}                                                     ║
║                                                                               ║
║ {SunriseColors.WARNING}Very Large Models (24B-27B parameters):{SunriseColors.SUCCESS}                                       ║
║   • devstral:24b, gemma3:27b                                                 ║
║   • Timeout: 9 minutes (540s)                                                ║
║   • Best for: Complex analysis, long responses                               ║
║                                                                               ║
║ {SunriseColors.INFO}Large Models (7B-8B parameters):{SunriseColors.SUCCESS}                                                ║
║   • deepseek-r1:8b, gemma2:7b                                                ║
║   • Timeout: 6 minutes (360s)                                                ║
║   • Best for: General chat, coding help                                      ║
║                                                                               ║
║ {SunriseColors.ACCENT}Medium Models (4B parameters):{SunriseColors.SUCCESS}                                                 ║
║   • gemma3:4b                                                                ║
║   • Timeout: 4.5 minutes (270s)                                              ║
║   • Best for: Quick responses, system commands                               ║
║                                                                               ║
║ {SunriseColors.SUCCESS}Small Models (1.5B parameters):{SunriseColors.SUCCESS}                                               ║
║   • deepseek-r1:1.5b                                                         ║
║   • Timeout: 4.5 minutes (270s)                                              ║
║   • Best for: Fast responses, simple tasks                                   ║
║                                                                               ║
║ {SunriseColors.ACCENT}💡 OPTIMIZATION TIPS:{SunriseColors.SUCCESS}                                                          ║
║   • Use smaller models for quick system commands                             ║
║   • Use larger models for complex analysis                                   ║
║   • Keep prompts concise for faster responses                                ║
║   • Monitor system resources during large model usage                        ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(recommendations)


def main():
    """Run extended timeout tests"""
    print("🎯 Cybex Enterprise - Extended Timeout Test")
    print("=" * 60)
    
    tests = [
        ("Timeout Calculation", test_timeout_calculation),
        ("Configuration Timeout", test_config_timeout),
        ("Enterprise UI Timeout Display", test_enterprise_ui_timeout_display),
        ("Actual Model Response", test_actual_model_response)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= 3:  # At least 3 out of 4 tests should pass
        show_timeout_recommendations()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Extended timeout system is working!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}All your models should now work without timeout issues{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Ready to use:{SunriseColors.RESET}")
        print(f"  • devstral:24b (9 min timeout)")
        print(f"  • gemma3:27b (9 min timeout)")
        print(f"  • All other models with appropriate timeouts")
    else:
        print(f"⚠️  Some tests failed. Check configuration and Ollama connection.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
