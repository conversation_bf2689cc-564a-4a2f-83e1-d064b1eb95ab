#!/usr/bin/env python3
"""
Test Suite for Web Browsing Capabilities
Tests web search, page fetching, and content analysis
"""

import unittest
import time
from pathlib import Path
import sys

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from cybex.modules.web_browsing_agent import WebBrowsingAgent
    from cybex.modules.agent_tools import AgentTools
    from cybex.modules.enhanced_ai_integration import EnhancedAIIntegration
    from cybex.modules.log_manager import LogManager
    WEB_DEPENDENCIES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Web dependencies not available: {e}")
    print("💡 Run: scripts/install_web_dependencies.bat")
    WEB_DEPENDENCIES_AVAILABLE = False


@unittest.skipUnless(WEB_DEPENDENCIES_AVAILABLE, "Web dependencies not installed")
class TestWebBrowsingAgent(unittest.TestCase):
    """Test web browsing agent functionality"""
    
    def setUp(self):
        self.agent = WebBrowsingAgent()
    
    def tearDown(self):
        if hasattr(self, 'agent'):
            self.agent.close()
    
    def test_web_search_basic(self):
        """Test basic web search functionality"""
        results = self.agent.search_web("python programming", max_results=3)
        
        self.assertIsInstance(results, list)
        if results:  # Only test if we got results (network dependent)
            self.assertGreater(len(results), 0)
            self.assertLessEqual(len(results), 3)
            
            # Check result structure
            first_result = results[0]
            self.assertTrue(hasattr(first_result, 'title'))
            self.assertTrue(hasattr(first_result, 'url'))
            self.assertTrue(hasattr(first_result, 'snippet'))
            self.assertTrue(hasattr(first_result, 'domain'))
    
    def test_fetch_webpage_basic(self):
        """Test basic webpage fetching"""
        # Use a reliable test URL
        test_url = "https://httpbin.org/html"
        content = self.agent.fetch_page_content(test_url)
        
        if content:  # Only test if fetch succeeded (network dependent)
            self.assertIsNotNone(content.title)
            self.assertIsNotNone(content.content)
            self.assertEqual(content.status_code, 200)
            self.assertGreater(len(content.content), 0)
    
    def test_content_analysis(self):
        """Test content analysis functionality"""
        # Create mock content for testing
        from cybex.modules.web_browsing_agent import WebPageContent
        
        mock_content = WebPageContent(
            url="https://example.com",
            title="Test Page Title",
            content="This is a test page with some content for analysis.",
            markdown_content="# Test Page\nThis is content.",
            links=["https://example.com/link1", "https://example.com/link2"],
            images=["https://example.com/image1.jpg"],
            metadata={"description": "Test page description"},
            load_time=1.5,
            status_code=200
        )
        
        analysis = self.agent.analyze_page_content(mock_content, 'general')
        
        self.assertIsInstance(analysis, dict)
        self.assertIn('word_count', analysis)
        self.assertIn('character_count', analysis)
        self.assertIn('links_count', analysis)
        self.assertIn('images_count', analysis)
        self.assertEqual(analysis['links_count'], 2)
        self.assertEqual(analysis['images_count'], 1)
    
    def test_browsing_history(self):
        """Test browsing history functionality"""
        # Perform a search to create history
        self.agent.search_web("test query", max_results=1)
        
        history = self.agent.get_browsing_history()
        
        self.assertIsInstance(history, dict)
        self.assertIn('search_history', history)
        self.assertIn('total_searches', history)
        self.assertGreaterEqual(history['total_searches'], 1)


@unittest.skipUnless(WEB_DEPENDENCIES_AVAILABLE, "Web dependencies not installed")
class TestWebToolsIntegration(unittest.TestCase):
    """Test web tools integration with AgentTools"""
    
    def setUp(self):
        self.log_manager = LogManager()
        self.agent_tools = AgentTools(self.log_manager)
    
    def test_web_tools_registration(self):
        """Test that web tools are properly registered"""
        available_tools = self.agent_tools.get_available_tools()
        
        web_tools = ['web_search', 'fetch_webpage', 'analyze_webpage', 'browsing_history']
        for tool in web_tools:
            self.assertIn(tool, available_tools, f"Web tool {tool} not registered")
    
    def test_web_search_tool_execution(self):
        """Test web search tool execution"""
        if not hasattr(self.agent_tools, 'web_browsing_agent') or not self.agent_tools.web_browsing_agent:
            self.skipTest("Web browsing agent not available")
        
        result = self.agent_tools.execute_tool_optimized(
            'web_search',
            {'query': 'python', 'max_results': 2}
        )
        
        self.assertIsNotNone(result)
        self.assertTrue(hasattr(result, 'success'))
        # Note: Success depends on network connectivity
    
    def test_fetch_webpage_tool_execution(self):
        """Test webpage fetch tool execution"""
        if not hasattr(self.agent_tools, 'web_browsing_agent') or not self.agent_tools.web_browsing_agent:
            self.skipTest("Web browsing agent not available")
        
        result = self.agent_tools.execute_tool_optimized(
            'fetch_webpage',
            {'url': 'https://httpbin.org/html'}
        )
        
        self.assertIsNotNone(result)
        self.assertTrue(hasattr(result, 'success'))
        # Note: Success depends on network connectivity


@unittest.skipUnless(WEB_DEPENDENCIES_AVAILABLE, "Web dependencies not installed")
class TestWebNaturalLanguageProcessing(unittest.TestCase):
    """Test natural language processing for web commands"""
    
    def setUp(self):
        self.ai_integration = EnhancedAIIntegration()
    
    def test_web_search_command_parsing(self):
        """Test parsing of web search commands"""
        test_commands = [
            "Cerca informazioni su intelligenza artificiale",
            "Search for python tutorials",
            "Trova notizie su tecnologia",
            "Ricerca machine learning"
        ]
        
        for command in test_commands:
            with self.subTest(command=command):
                result = self.ai_integration.parse_natural_language_command(command)
                
                self.assertEqual(result['tool'], 'web_search')
                self.assertEqual(result['intent'], 'web_search')
                self.assertIn('parameters', result)
                self.assertIn('query', result['parameters'])
    
    def test_webpage_fetch_command_parsing(self):
        """Test parsing of webpage fetch commands"""
        test_commands = [
            "Apri sito google.com",
            "Open website https://example.com",
            "Vai su facebook.com",
            "Visita github.com"
        ]
        
        for command in test_commands:
            with self.subTest(command=command):
                result = self.ai_integration.parse_natural_language_command(command)
                
                self.assertEqual(result['tool'], 'fetch_webpage')
                self.assertEqual(result['intent'], 'web_fetch')
                self.assertIn('parameters', result)
                self.assertIn('url', result['parameters'])
    
    def test_webpage_analysis_command_parsing(self):
        """Test parsing of webpage analysis commands"""
        test_commands = [
            "Analizza sito web google.com per SEO",
            "Analyze website example.com for content quality",
            "Esamina pagina github.com aspetti tecnici"
        ]
        
        for command in test_commands:
            with self.subTest(command=command):
                result = self.ai_integration.parse_natural_language_command(command)
                
                self.assertEqual(result['tool'], 'analyze_webpage')
                self.assertEqual(result['intent'], 'web_analysis')
                self.assertIn('parameters', result)
                self.assertIn('url', result['parameters'])
                self.assertIn('analysis_type', result['parameters'])


def run_web_browsing_tests():
    """Run web browsing tests with detailed reporting"""
    print("🌐 CYBEX ENTERPRISE - WEB BROWSING TEST SUITE")
    print("=" * 60)
    
    if not WEB_DEPENDENCIES_AVAILABLE:
        print("❌ Web dependencies not available!")
        print("💡 Run: scripts/install_web_dependencies.bat")
        print("   Then try again.")
        return False
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test classes
    test_classes = [
        TestWebBrowsingAgent,
        TestWebToolsIntegration,
        TestWebNaturalLanguageProcessing
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Generate summary
    print("\n" + "=" * 60)
    print("🌐 WEB BROWSING TEST SUMMARY")
    print("=" * 60)
    print(f"Tests Run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success Rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ FAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  • {test}")
    
    if result.errors:
        print(f"\n🔥 ERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  • {test}")
    
    # Overall assessment
    if len(result.failures) == 0 and len(result.errors) == 0:
        print(f"\n🎉 ALL WEB BROWSING TESTS PASSED!")
        print("✅ CYBEX Enterprise web browsing capabilities are operational!")
        return True
    else:
        print(f"\n⚠️  Some web tests failed (may be due to network connectivity)")
        return False


if __name__ == "__main__":
    success = run_web_browsing_tests()
    sys.exit(0 if success else 1)
