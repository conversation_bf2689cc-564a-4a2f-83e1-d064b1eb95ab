@echo off
setlocal enabledelayedexpansion
REM ============================================================================
REM Cybex Enterprise - Advanced Launcher
REM Professional startup script with multiple options
REM by AGTECHdesigne
REM ============================================================================

title Cybex Enterprise - Advanced Launcher

REM Set colors for output
color 0B

:MAIN_MENU
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║   ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗                                  ║
echo ║  ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝                                  ║
echo ║  ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝                                   ║
echo ║  ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗                                   ║
echo ║  ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗                                  ║
echo ║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝                                  ║
echo ║                                                                              ║
echo ║                      Enterprise Edition                                      ║
echo ║                        by AGTECHdesigne                                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════════╗
echo ║                            🚀 CYBEX LAUNCHER                                  ║
echo ╠═══════════════════════════════════════════════════════════════════════════════╣
echo ║                                                                               ║
echo ║  [1] 🖥️  Start Cybex Enterprise (Full Interface)                             ║
echo ║  [2] 🧪 Run System Tests                                                     ║
echo ║  [3] 🔧 Check System Requirements                                            ║
echo ║  [4] 📋 View Available Ollama Models                                         ║
echo ║  [5] 🎨 Demo Enterprise Features                                             ║
echo ║  [6] 📖 View Documentation                                                   ║
echo ║  [7] ⚙️  Install/Update Dependencies                                         ║
echo ║  [8] 🔄 Update Cybex                                                         ║
echo ║  [Q] ❌ Quit                                                                 ║
echo ║                                                                               ║
echo ╚═══════════════════════════════════════════════════════════════════════════════╝
echo.

set /p choice="Select option (1-8, Q): "

if /i "%choice%"=="1" goto START_CYBEX
if /i "%choice%"=="2" goto RUN_TESTS
if /i "%choice%"=="3" goto CHECK_REQUIREMENTS
if /i "%choice%"=="4" goto VIEW_MODELS
if /i "%choice%"=="5" goto DEMO_FEATURES
if /i "%choice%"=="6" goto VIEW_DOCS
if /i "%choice%"=="7" goto INSTALL_DEPS
if /i "%choice%"=="8" goto UPDATE_CYBEX
if /i "%choice%"=="Q" goto QUIT
if /i "%choice%"=="QUIT" goto QUIT

echo.
echo ❌ Invalid option. Please try again.
timeout /t 2 >nul
goto MAIN_MENU

:START_CYBEX
cls
echo.
echo 🚀 Starting Cybex Enterprise...
echo.

REM Check Python
call :CHECK_PYTHON
if errorlevel 1 goto MAIN_MENU

REM Check files
if not exist "main_enterprise.py" (
    echo ❌ ERROR: main_enterprise.py not found
    echo Please ensure you're in the correct Cybex directory
    pause
    goto MAIN_MENU
)

REM Launch Cybex
echo ✅ Launching Cybex Enterprise...
echo.
python main_enterprise.py

echo.
echo 🎯 Cybex Enterprise session ended
pause
goto MAIN_MENU

:RUN_TESTS
cls
echo.
echo 🧪 Running Cybex System Tests...
echo.

call :CHECK_PYTHON
if errorlevel 1 goto MAIN_MENU

echo Running comprehensive tests...
echo.

if exist "test_dynamic_models.py" (
    echo 🤖 Testing dynamic model detection...
    python test_dynamic_models.py
    echo.
)

if exist "test_enterprise_ui.py" (
    echo 🎨 Testing Enterprise UI...
    python test_enterprise_ui.py
    echo.
)

if exist "test_natural_language.py" (
    echo 🗣️  Testing natural language processing...
    python test_natural_language.py
    echo.
)

echo ✅ Tests completed
pause
goto MAIN_MENU

:CHECK_REQUIREMENTS
cls
echo.
echo 🔍 Checking System Requirements...
echo.

call :CHECK_PYTHON
if errorlevel 1 goto MAIN_MENU

echo ✅ Python is installed and working
echo.

echo 📦 Checking Python packages...
python -c "import yaml, requests, psutil, colorama; print('✅ All required packages are installed')" 2>nul
if errorlevel 1 (
    echo ❌ Some required packages are missing
    echo Run option [7] to install dependencies
) else (
    echo ✅ All Python dependencies are available
)
echo.

echo 🔌 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not running or not installed
    echo Install from: https://ollama.ai
) else (
    echo ✅ Ollama server is running
)
echo.

echo 💾 Checking disk space...
for /f "tokens=3" %%a in ('dir /-c ^| find "bytes free"') do set free_bytes=%%a
set /a free_gb=!free_bytes!/1073741824
echo ✅ Available disk space: !free_gb! GB
echo.

pause
goto MAIN_MENU

:VIEW_MODELS
cls
echo.
echo 📋 Available Ollama Models...
echo.

curl -s http://localhost:11434/api/tags 2>nul | python -m json.tool 2>nul
if errorlevel 1 (
    echo ❌ Could not connect to Ollama server
    echo.
    echo Make sure Ollama is installed and running:
    echo   1. Install from https://ollama.ai
    echo   2. Run: ollama pull gemma2:7b
    echo   3. Start Ollama service
) else (
    echo.
    echo ✅ Models retrieved from Ollama API
    echo These models will be available in Cybex Enterprise
)

echo.
pause
goto MAIN_MENU

:DEMO_FEATURES
cls
echo.
echo 🎨 Running Enterprise Features Demo...
echo.

call :CHECK_PYTHON
if errorlevel 1 goto MAIN_MENU

if exist "demo_enterprise.py" (
    python demo_enterprise.py
) else (
    echo ❌ Demo file not found
    echo Please ensure demo_enterprise.py is in the current directory
)

echo.
pause
goto MAIN_MENU

:VIEW_DOCS
cls
echo.
echo 📖 Cybex Enterprise Documentation
echo.
echo ╔═══════════════════════════════════════════════════════════════════════════════╗
echo ║                          📚 QUICK START GUIDE                                ║
echo ╠═══════════════════════════════════════════════════════════════════════════════╣
echo ║                                                                               ║
echo ║ 🚀 LAUNCHING CYBEX:                                                          ║
echo ║   • Use this launcher (option 1) or run: python main_enterprise.py          ║
echo ║                                                                               ║
echo ║ 🗣️  NATURAL LANGUAGE COMMANDS:                                               ║
echo ║   • "dammi la situazione memoria del computer"                               ║
echo ║   • "come sta il disco?"                                                     ║
echo ║   • "stato del sistema"                                                      ║
echo ║   • "ottimizza il sistema"                                                   ║
echo ║                                                                               ║
echo ║ 🚀 QUICK ACTIONS:                                                            ║
echo ║   • Press [1-9] for instant system analysis                                  ║
echo ║   • Press [C] for configuration                                              ║
echo ║   • Press [M] to switch Chat/Agent mode                                      ║
echo ║                                                                               ║
echo ║ ⚙️  CONFIGURATION:                                                           ║
echo ║   • All your Ollama models are automatically detected                        ║
echo ║   • Use [R] to refresh model list                                            ║
echo ║   • Use [A] to see detailed model information                                ║
echo ║                                                                               ║
echo ║ 🎨 FEATURES:                                                                 ║
echo ║   • Sunrise color palette (75bde0, f8d49b, f8bc9b, fb9b9b)                  ║
echo ║   • Real-time system monitoring                                              ║
echo ║   • Italian natural language processing                                      ║
echo ║   • Enterprise-grade interface                                               ║
echo ║                                                                               ║
echo ╚═══════════════════════════════════════════════════════════════════════════════╝
echo.
pause
goto MAIN_MENU

:INSTALL_DEPS
cls
echo.
echo 📦 Installing/Updating Dependencies...
echo.

call :CHECK_PYTHON
if errorlevel 1 goto MAIN_MENU

if exist "requirements.txt" (
    echo Installing from requirements.txt...
    pip install -r requirements.txt
) else (
    echo Installing core dependencies...
    pip install pyyaml requests psutil colorama
)

echo.
echo ✅ Dependencies installation completed
pause
goto MAIN_MENU

:UPDATE_CYBEX
cls
echo.
echo 🔄 Update Cybex (Feature coming soon)...
echo.
echo This feature will allow automatic updates from the repository.
echo For now, please manually download the latest version.
echo.
pause
goto MAIN_MENU

:CHECK_PYTHON
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)
exit /b 0

:QUIT
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║                    Thank you for using Cybex Enterprise                      ║
echo ║                           by AGTECHdesigne                                   ║
echo ║                                                                              ║
echo ║                     🚀 Advanced System Administration                        ║
echo ║                     🤖 Natural Language Processing                           ║
echo ║                     🎨 Professional Interface                                ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
timeout /t 3 >nul
exit /b 0
