"""
CLI Interface Module
Command-line interface for Cybex AI agent
"""

import os
import sys
import time
import platform
from typing import Dict, List, Optional, Tuple
from pathlib import Path

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from colorama import init, Fore, Back, Style
    init(autoreset=True)
    COLORS_AVAILABLE = True
except ImportError:
    COLORS_AVAILABLE = False
    # Fallback color definitions
    class Fore:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Back:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Style:
        BRIGHT = DIM = NORMAL = RESET_ALL = ""

from cybex.core.cybex_core import CybexCore
from cybex.modules.command_executor import CommandExecutor
from cybex.modules.ollama_interface import OllamaInterface


class CybexCLI:
    """
    Command-line interface for Cybex AI agent
    """
    
    def __init__(self):
        """Initialize CLI interface"""
        self.running = False
        self.core = None
        self.command_executor = None
        self.ollama_interface = None
        self.logger = None

        # CLI state
        self.current_directory = os.getcwd()
        self.prompt_prefix = "cybex"
        
        # Initialize components
        self._initialize_components()
    
    def _initialize_components(self) -> bool:
        """Initialize Cybex components"""
        try:
            # Initialize core
            self.core = CybexCore()
            self.logger = self.core.log_manager.get_logger(__name__)
            
            # Initialize command executor
            self.command_executor = CommandExecutor(
                self.core.config_manager,
                self.core.security_manager,
                self.core.log_manager
            )
            
            # Initialize Ollama interface
            self.ollama_interface = OllamaInterface(
                self.core.config_manager,
                self.core.log_manager
            )

            # Initialize agent modules now that command_executor is available
            self.core._init_agent_modules(self.command_executor, self.ollama_interface)

            # Initialize natural language processor
            self.nl_processor = None
            self._init_nl_processor()

            return True

        except Exception as e:
            self.logger.error(f"Failed to initialize components: {e}")
            return False

    def _init_nl_processor(self) -> None:
        """Initialize natural language processor"""
        try:
            from cybex.modules.natural_language_processor import NaturalLanguageProcessor

            self.nl_processor = NaturalLanguageProcessor(
                self.core,
                self.core.system_monitor,
                self.core.disk_manager
            )
            self.logger.info("Natural language processor initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize NL processor: {e}")
            self.nl_processor = None

            return True
            
        except Exception as e:
            self._print_error(f"Failed to initialize Cybex: {e}")
            return False
    
    def start(self) -> None:
        """Start the CLI interface"""
        if not self.core:
            self._print_error("Cybex not properly initialized")
            return
        
        self._print_banner()
        
        # Start Cybex session
        if not self.core.start_session():
            self._print_error("Failed to start Cybex session")
            return
        
        self._print_info("Cybex session started successfully")
        self._print_help()
        
        self.running = True
        
        try:
            self._main_loop()
        except KeyboardInterrupt:
            self._print_info("\nShutting down Cybex...")
        except Exception as e:
            self._print_error(f"Unexpected error: {e}")
        finally:
            self._shutdown()
    
    def _main_loop(self) -> None:
        """Main CLI loop"""
        while self.running:
            try:
                # Get user input
                prompt = self._build_prompt()
                user_input = input(prompt).strip()
                
                if not user_input:
                    continue
                
                # Process input
                self._process_input(user_input)
                
            except KeyboardInterrupt:
                self._print_info("\nUse 'exit' or 'quit' to shutdown Cybex")
            except EOFError:
                break
    
    def _process_input(self, user_input: str) -> None:
        """Process user input"""
        # Check for built-in commands
        if self._handle_builtin_commands(user_input):
            return
        
        # Check if it's a direct system command
        if user_input.startswith('!'):
            self._execute_system_command(user_input[1:])
            return
        
        # Send to AI for processing
        self._process_ai_request(user_input)
    
    def _handle_builtin_commands(self, command: str) -> bool:
        """Handle built-in CLI commands"""
        parts = command.lower().split()
        cmd = parts[0] if parts else ""

        if cmd in ['exit', 'quit', 'q']:
            self.running = False
            return True

        elif cmd == 'help':
            self._print_help()
            return True

        elif cmd == 'status':
            self._show_status()
            return True

        elif cmd == 'history':
            self._show_history()
            return True

        elif cmd == 'clear':
            self._clear_screen()
            return True

        elif cmd == 'mode':
            if len(parts) > 1:
                self._switch_mode(parts[1])
            else:
                self._print_info(f"Current mode: {self.core.mode.value}")
            return True

        elif cmd == 'config':
            self._show_config()
            return True

        elif cmd == 'logs':
            limit = int(parts[1]) if len(parts) > 1 and parts[1].isdigit() else 10
            self._show_logs(limit)
            return True

        # SVP Commands
        elif cmd == 'monitor':
            self._show_system_monitor()
            return True

        elif cmd == 'disk':
            if len(parts) > 1 and parts[1] == 'cleanup':
                self._disk_cleanup()
            else:
                self._show_disk_info()
            return True

        elif cmd == 'optimize':
            self._optimize_system()
            return True

        elif cmd == 'plan':
            if len(parts) > 1:
                goal = ' '.join(parts[1:])
                self._create_agent_plan(goal)
            else:
                self._print_info("Usage: plan <goal description>")
            return True

        elif cmd == 'snapshot':
            if len(parts) > 1:
                if parts[1] == 'create':
                    name = ' '.join(parts[2:]) if len(parts) > 2 else f"manual_{int(time.time())}"
                    self._create_snapshot(name)
                elif parts[1] == 'list':
                    self._list_snapshots()
                elif parts[1] == 'restore' and len(parts) > 2:
                    self._restore_snapshot(parts[2])
                else:
                    self._print_info("Usage: snapshot create|list|restore [name]")
            else:
                self._list_snapshots()
            return True

        return False
    
    def _execute_system_command(self, command: str) -> None:
        """Execute system command directly"""
        self._print_info(f"Executing: {command}")
        
        # Check if confirmation is required
        if self.core.requires_confirmation(command):
            if not self._confirm_action(f"Execute command: {command}"):
                self._print_warning("Command cancelled")
                return
        
        # Execute command
        success, output, metadata = self.command_executor.execute_command(
            command, confirm_required=False
        )
        
        # Display results
        if success:
            self._print_success("Command executed successfully")
            if output:
                print(output)
        else:
            self._print_error(f"Command failed: {output}")
    
    def _process_ai_request(self, user_input: str) -> None:
        """Process request through AI with natural language processing"""
        self._print_info("Elaborando richiesta...")

        # Prepare context with NL processor
        context = {
            'system_info': f"{platform.system()} {platform.release()}",
            'current_directory': self.current_directory,
            'mode': self.core.mode.value,
            'nl_processor': self.nl_processor  # Add NL processor to context
        }

        # Get AI response
        response = self.ollama_interface.generate_response(user_input, context)

        if response.success:
            # Check if this was processed locally by NL processor
            if response.metadata and response.metadata.get('processed_locally'):
                # This was handled by natural language processor
                self._print_nl_response(response.content, response.metadata)

                # Show recommendations if available
                recommendations = response.metadata.get('recommendations', [])
                if recommendations:
                    self._print_recommendations(recommendations)
            else:
                # This was handled by AI model
                print(f"\n{Fore.CYAN}Cybex:{Style.RESET_ALL} {response.content}\n")

                # Check if response contains commands to execute
                self._check_for_commands_in_response(response.content)
        else:
            self._print_error(f"Errore AI: {response.error}")
    
    def _check_for_commands_in_response(self, response: str) -> None:
        """Check if AI response contains commands to execute"""
        # This is a simplified implementation
        # In a full version, this would parse the response for command suggestions
        if "EXECUTE:" in response.upper():
            lines = response.split('\n')
            for line in lines:
                if line.strip().upper().startswith('EXECUTE:'):
                    command = line.split(':', 1)[1].strip()
                    if self._confirm_action(f"Execute suggested command: {command}"):
                        self._execute_system_command(command)
    
    def _build_prompt(self) -> str:
        """Build the CLI prompt"""
        mode_indicator = "🤖" if self.core.mode.value == "agent" else "💬"
        return f"{Fore.GREEN}{self.prompt_prefix}{Style.RESET_ALL} {mode_indicator} > "
    
    def _print_banner(self) -> None:
        """Print Cybex banner"""
        banner = f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║   {Fore.YELLOW}CYBEX - Cybernetic Expert Agent{Fore.CYAN}                        ║
║   {Fore.WHITE}Local AI System Administrator{Fore.CYAN}                           ║
║                                                              ║
║   {Fore.GREEN}Version: 1.0.0 (MVP Phase){Fore.CYAN}                             ║
║   {Fore.MAGENTA}System: {platform.system()} {platform.release():<20}{Fore.CYAN}                    ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
"""
        print(banner)
    
    def _print_help(self) -> None:
        """Print help information"""
        help_text = f"""
{Fore.YELLOW}Available Commands:{Style.RESET_ALL}
  {Fore.GREEN}help{Style.RESET_ALL}           - Show this help message
  {Fore.GREEN}status{Style.RESET_ALL}         - Show system and Cybex status
  {Fore.GREEN}mode [chat|agent]{Style.RESET_ALL} - Switch or show current mode
  {Fore.GREEN}history{Style.RESET_ALL}        - Show command history
  {Fore.GREEN}logs [n]{Style.RESET_ALL}       - Show last n log entries (default: 10)
  {Fore.GREEN}config{Style.RESET_ALL}         - Show current configuration
  {Fore.GREEN}clear{Style.RESET_ALL}          - Clear screen
  {Fore.GREEN}exit/quit{Style.RESET_ALL}      - Exit Cybex

{Fore.YELLOW}SVP Commands:{Style.RESET_ALL}
  {Fore.GREEN}monitor{Style.RESET_ALL}        - Show system monitoring dashboard
  {Fore.GREEN}disk [cleanup]{Style.RESET_ALL} - Show disk info or perform cleanup
  {Fore.GREEN}optimize{Style.RESET_ALL}       - Perform system optimization
  {Fore.GREEN}plan <goal>{Style.RESET_ALL}    - Create agent execution plan
  {Fore.GREEN}snapshot{Style.RESET_ALL}       - Manage system snapshots

{Fore.YELLOW}Command Execution:{Style.RESET_ALL}
  {Fore.GREEN}!command{Style.RESET_ALL}       - Execute system command directly
  {Fore.GREEN}text input{Style.RESET_ALL}     - Send request to AI for processing

{Fore.YELLOW}Current Mode:{Style.RESET_ALL} {Fore.CYAN}{self.core.mode.value}{Style.RESET_ALL}
"""
        print(help_text)
    
    def _show_status(self) -> None:
        """Show system and Cybex status"""
        status = self.core.get_system_status()

        status_text = f"""
{Fore.YELLOW}System Status:{Style.RESET_ALL}
  Platform: {status['platform']}
  Python: {status['python_version']}
  Mode: {status['mode']}
  Confirmation Level: {status['confirm_level']}
  Session Active: {status['session_active']}

{Fore.YELLOW}Ollama Status:{Style.RESET_ALL}
  Server Available: {self.ollama_interface.is_server_available()}
  Current Model: {self.ollama_interface.model}
"""

        # Add SVP status if available
        if 'monitoring' in status:
            mon = status['monitoring']
            status_text += f"""
{Fore.YELLOW}System Monitoring:{Style.RESET_ALL}
  Enabled: {mon['enabled']}
  CPU: {mon['current_state'].get('cpu', {}).get('percent', 0):.1f}%
  Memory: {mon['current_state'].get('memory', {}).get('percent', 0):.1f}%
  Disk: {mon['current_state'].get('disk', {}).get('percent', 0):.1f}%
  Active Alerts: {mon['alerts']}
"""

        if 'disk_health' in status:
            disk = status['disk_health']
            status_text += f"""
{Fore.YELLOW}Disk Health:{Style.RESET_ALL}
  Total Disks: {disk['total_disks']}
  Healthy: {disk['healthy_disks']} | Warning: {disk['warning_disks']} | Critical: {disk['critical_disks']}
  Total Space: {disk['total_space_gb']:.1f} GB
  Average Usage: {disk['average_usage_percent']:.1f}%
"""

        if 'agent' in status:
            agent = status['agent']
            status_text += f"""
{Fore.YELLOW}Agent Status:{Style.RESET_ALL}
  Total Executions: {agent['metrics']['total_executions']}
  Success Rate: {(agent['metrics']['successful_executions'] / max(agent['metrics']['total_executions'], 1) * 100):.1f}%
  Active Executions: {agent['active_executions']}
"""

        print(status_text)
    
    def _show_history(self) -> None:
        """Show command history"""
        history = self.command_executor.get_command_history()
        
        if not history:
            self._print_info("No command history available")
            return
        
        print(f"\n{Fore.YELLOW}Recent Commands:{Style.RESET_ALL}")
        for i, entry in enumerate(history, 1):
            status = "✓" if entry['success'] else "✗"
            timestamp = time.strftime("%H:%M:%S", time.localtime(entry['timestamp']))
            print(f"  {i:2d}. [{timestamp}] {status} {entry['command']}")
    
    def _show_config(self) -> None:
        """Show current configuration"""
        config_info = f"""
{Fore.YELLOW}Configuration:{Style.RESET_ALL}
  Mode: {self.core.mode.value}
  Confirmation Level: {self.core.confirm_level.value}
  System Type: {self.core.system_type}
  Ollama Model: {self.ollama_interface.model}
  Ollama Host: {self.ollama_interface.host}:{self.ollama_interface.port}
"""
        print(config_info)
    
    def _show_logs(self, limit: int) -> None:
        """Show recent log entries"""
        logs = self.core.log_manager.get_recent_logs(limit)
        
        if not logs:
            self._print_info("No log entries available")
            return
        
        print(f"\n{Fore.YELLOW}Recent Log Entries:{Style.RESET_ALL}")
        for log in logs:
            print(f"  {log}")
    
    def _switch_mode(self, mode: str) -> None:
        """Switch operating mode"""
        if self.core.switch_mode(mode):
            self._print_success(f"Switched to {mode} mode")
        else:
            self._print_error(f"Invalid mode: {mode}")
    
    def _confirm_action(self, message: str) -> bool:
        """Ask for user confirmation"""
        response = input(f"{Fore.YELLOW}Confirm:{Style.RESET_ALL} {message} (y/N): ").strip().lower()
        return response in ['y', 'yes']
    
    def _clear_screen(self) -> None:
        """Clear the screen"""
        os.system('cls' if os.name == 'nt' else 'clear')
        self._print_banner()
    
    def _print_info(self, message: str) -> None:
        """Print info message"""
        print(f"{Fore.BLUE}ℹ{Style.RESET_ALL} {message}")
    
    def _print_success(self, message: str) -> None:
        """Print success message"""
        print(f"{Fore.GREEN}✓{Style.RESET_ALL} {message}")
    
    def _print_warning(self, message: str) -> None:
        """Print warning message"""
        print(f"{Fore.YELLOW}⚠{Style.RESET_ALL} {message}")
    
    def _print_error(self, message: str) -> None:
        """Print error message"""
        print(f"{Fore.RED}✗{Style.RESET_ALL} {message}")
    
    def _shutdown(self) -> None:
        """Shutdown Cybex"""
        if self.core and self.core.session_active:
            self.core.end_session()

        self._print_info("Cybex shutdown complete")

    def _print_nl_response(self, content: str, metadata: Dict) -> None:
        """Print natural language processor response"""
        print(f"\n{Fore.GREEN}🤖 Cybex (Analisi Locale):{Style.RESET_ALL}")
        print(content)

        # Show execution time if available
        if metadata.get('execution_time'):
            print(f"\n{Fore.BLUE}⏱️  Tempo elaborazione: {metadata['execution_time']:.2f}s{Style.RESET_ALL}")

    def _print_recommendations(self, recommendations: List[str]) -> None:
        """Print recommendations"""
        if recommendations:
            print(f"\n{Fore.YELLOW}💡 Raccomandazioni:{Style.RESET_ALL}")
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
            print()

    # SVP Command Methods
    def _show_system_monitor(self) -> None:
        """Show system monitoring dashboard"""
        if not self.core.system_monitor:
            self._print_warning("System monitoring not available")
            return

        try:
            current_state = self.core.system_monitor.get_current_state()
            performance_summary = self.core.system_monitor.get_performance_summary()
            alerts = self.core.system_monitor.get_alerts()

            print(f"\n{Fore.YELLOW}System Monitoring Dashboard:{Style.RESET_ALL}")
            print(f"  Timestamp: {current_state.get('timestamp', 'Unknown')}")

            # CPU Info
            cpu = current_state.get('cpu', {})
            print(f"\n{Fore.CYAN}CPU:{Style.RESET_ALL}")
            print(f"  Usage: {cpu.get('percent', 0):.1f}%")
            print(f"  Cores: {cpu.get('count', 0)}")
            print(f"  Frequency: {cpu.get('frequency', 0):.0f} MHz")

            # Memory Info
            memory = current_state.get('memory', {})
            print(f"\n{Fore.CYAN}Memory:{Style.RESET_ALL}")
            print(f"  Usage: {memory.get('percent', 0):.1f}%")
            print(f"  Used: {memory.get('used', 0) / (1024**3):.1f} GB")
            print(f"  Available: {memory.get('available', 0) / (1024**3):.1f} GB")

            # Disk Info
            disk = current_state.get('disk', {})
            print(f"\n{Fore.CYAN}Disk:{Style.RESET_ALL}")
            print(f"  Average Usage: {disk.get('percent', 0):.1f}%")

            # Network Info
            network = current_state.get('network', {})
            print(f"\n{Fore.CYAN}Network:{Style.RESET_ALL}")
            print(f"  Sent: {network.get('sent_bytes', 0) / 1024:.1f} KB/s")
            print(f"  Received: {network.get('recv_bytes', 0) / 1024:.1f} KB/s")

            # Alerts
            if alerts:
                print(f"\n{Fore.RED}Active Alerts:{Style.RESET_ALL}")
                for alert in alerts[:5]:  # Show first 5 alerts
                    level_color = Fore.RED if alert.level.value == 'critical' else Fore.YELLOW
                    print(f"  {level_color}[{alert.level.value.upper()}]{Style.RESET_ALL} {alert.message}")
            else:
                print(f"\n{Fore.GREEN}No active alerts{Style.RESET_ALL}")

        except Exception as e:
            self._print_error(f"Failed to show monitoring dashboard: {e}")

    def _show_disk_info(self) -> None:
        """Show disk information"""
        if not self.core.disk_manager:
            self._print_warning("Disk management not available")
            return

        try:
            disk_info = self.core.disk_manager.get_disk_info()
            health_summary = self.core.disk_manager.get_disk_health_summary()

            print(f"\n{Fore.YELLOW}Disk Information:{Style.RESET_ALL}")
            print(f"  Total Disks: {health_summary['total_disks']}")
            print(f"  Total Space: {health_summary['total_space_gb']:.1f} GB")
            print(f"  Used Space: {health_summary['used_space_gb']:.1f} GB")
            print(f"  Free Space: {health_summary['free_space_gb']:.1f} GB")
            print(f"  Average Usage: {health_summary['average_usage_percent']:.1f}%")

            print(f"\n{Fore.CYAN}Health Status:{Style.RESET_ALL}")
            print(f"  Healthy: {health_summary['healthy_disks']}")
            print(f"  Warning: {health_summary['warning_disks']}")
            print(f"  Critical: {health_summary['critical_disks']}")

            print(f"\n{Fore.CYAN}Disk Details:{Style.RESET_ALL}")
            for disk in health_summary['disks']:
                health_color = Fore.GREEN if disk['health'] == 'good' else (
                    Fore.YELLOW if disk['health'] == 'warning' else Fore.RED
                )
                print(f"  {disk['device']}: {disk['usage_percent']:.1f}% used, "
                      f"{disk['free_gb']:.1f} GB free, "
                      f"{health_color}{disk['health']}{Style.RESET_ALL}")

        except Exception as e:
            self._print_error(f"Failed to show disk info: {e}")

    def _disk_cleanup(self) -> None:
        """Perform disk cleanup"""
        if not self.core.disk_manager:
            self._print_warning("Disk management not available")
            return

        try:
            from cybex.modules.disk_manager import CleanupCategory

            # Show cleanup preview first
            self._print_info("Scanning for cleanup candidates...")
            cleanup_data = self.core.disk_manager.scan_for_cleanup([
                CleanupCategory.TEMP_FILES,
                CleanupCategory.LOG_FILES,
                CleanupCategory.BROWSER_CACHE
            ])

            total_files = sum(data['total_count'] for data in cleanup_data.values())
            total_size = sum(data['estimated_space_gb'] for data in cleanup_data.values())

            print(f"\n{Fore.YELLOW}Cleanup Preview:{Style.RESET_ALL}")
            print(f"  Files to clean: {total_files}")
            print(f"  Space to free: {total_size:.2f} GB")

            if total_files == 0:
                self._print_info("No files found for cleanup")
                return

            # Ask for confirmation
            if self._confirm_action(f"Proceed with cleanup of {total_files} files?"):
                self._print_info("Performing cleanup...")
                result = self.core.disk_manager.cleanup_files(
                    list(cleanup_data.keys()),
                    dry_run=False
                )

                if result['files_removed'] > 0:
                    self._print_success(f"Cleanup completed: {result['files_removed']} files removed, "
                                      f"{result['space_freed_gb']:.2f} GB freed")
                else:
                    self._print_info("No files were removed")

                if result['errors']:
                    self._print_warning(f"{len(result['errors'])} errors occurred during cleanup")
            else:
                self._print_info("Cleanup cancelled")

        except Exception as e:
            self._print_error(f"Failed to perform disk cleanup: {e}")

    def _optimize_system(self) -> None:
        """Perform system optimization"""
        self._print_info("Starting system optimization...")

        try:
            result = self.core.perform_system_optimization()

            if result['success']:
                self._print_success("System optimization completed")

                for opt in result['optimizations']:
                    opt_type = opt['type']
                    if opt_type == 'disk_cleanup':
                        cleanup = opt['result']
                        print(f"  • Disk cleanup: {cleanup['files_removed']} files, "
                              f"{cleanup['space_freed_gb']:.2f} GB freed")
                    elif opt_type == 'database_cleanup':
                        db_cleanup = opt['result']
                        print(f"  • Database cleanup: {db_cleanup.get('commands_deleted', 0)} old commands removed")

                if not result['optimizations']:
                    self._print_info("System is already optimized")
            else:
                self._print_error("System optimization failed")
                for error in result['errors']:
                    print(f"  • {error}")

        except Exception as e:
            self._print_error(f"Failed to optimize system: {e}")

    def _create_agent_plan(self, goal: str) -> None:
        """Create agent execution plan"""
        if not self.core.agent_planner:
            self._print_warning("Agent planning not available")
            return

        try:
            self._print_info(f"Creating plan for goal: {goal}")

            context = {
                'system_info': f"{self.core.system_type}",
                'current_directory': self.current_directory,
                'mode': self.core.mode.value
            }

            plan_id = self.core.create_agent_plan(goal, context)

            if plan_id:
                plan = self.core.agent_planner.get_plan(plan_id)
                self._print_success(f"Plan created: {plan.name}")
                print(f"  Plan ID: {plan_id}")
                print(f"  Tasks: {len(plan.tasks)}")
                print(f"  Estimated Duration: {plan.estimated_duration // 60} minutes")
                print(f"  Risk Level: {plan.risk_level}")

                if self._confirm_action("Execute this plan now?"):
                    self._execute_agent_plan(plan_id)
            else:
                self._print_error("Failed to create plan")

        except Exception as e:
            self._print_error(f"Failed to create agent plan: {e}")

    def _execute_agent_plan(self, plan_id: str) -> None:
        """Execute agent plan"""
        try:
            def progress_callback(progress: float, task, result):
                print(f"  Progress: {progress:.1f}% - {task.name}: {'✓' if result.success else '✗'}")

            self._print_info("Executing agent plan...")
            success = self.core.execute_agent_plan(plan_id, progress_callback)

            if success:
                self._print_success("Agent plan executed successfully")
            else:
                self._print_error("Agent plan execution failed")

        except Exception as e:
            self._print_error(f"Failed to execute agent plan: {e}")

    def _create_snapshot(self, name: str) -> None:
        """Create system snapshot"""
        if not self.core.system_snapshot:
            self._print_warning("System snapshots not available")
            return

        try:
            self._print_info(f"Creating snapshot: {name}")
            snapshot_id = self.core.system_snapshot.create_snapshot(
                name,
                f"Manual snapshot created from CLI"
            )

            if snapshot_id:
                self._print_success(f"Snapshot created: {snapshot_id}")
            else:
                self._print_error("Failed to create snapshot")

        except Exception as e:
            self._print_error(f"Failed to create snapshot: {e}")

    def _list_snapshots(self) -> None:
        """List available snapshots"""
        if not self.core.system_snapshot:
            self._print_warning("System snapshots not available")
            return

        try:
            snapshots = self.core.system_snapshot.get_snapshots()

            if not snapshots:
                self._print_info("No snapshots available")
                return

            print(f"\n{Fore.YELLOW}Available Snapshots:{Style.RESET_ALL}")
            for snapshot in snapshots:
                print(f"  {snapshot['id']}: {snapshot['name']}")
                print(f"    Created: {snapshot['created_at']}")
                print(f"    Components: {', '.join(snapshot['components'])}")
                if snapshot['description']:
                    print(f"    Description: {snapshot['description']}")
                print()

        except Exception as e:
            self._print_error(f"Failed to list snapshots: {e}")

    def _restore_snapshot(self, snapshot_id: str) -> None:
        """Restore from snapshot"""
        if not self.core.system_snapshot:
            self._print_warning("System snapshots not available")
            return

        try:
            if self._confirm_action(f"Restore from snapshot {snapshot_id}? This may overwrite current system state."):
                self._print_info(f"Restoring from snapshot: {snapshot_id}")
                success = self.core.system_snapshot.restore_snapshot(snapshot_id)

                if success:
                    self._print_success("Snapshot restored successfully")
                else:
                    self._print_error("Failed to restore snapshot")
            else:
                self._print_info("Restore cancelled")

        except Exception as e:
            self._print_error(f"Failed to restore snapshot: {e}")


def main():
    """Main entry point for CLI"""
    cli = CybexCLI()
    cli.start()


if __name__ == "__main__":
    main()
