"""
Security Manager Mo<PERSON>le
Handles security validation, command filtering, and safety checks for Cybex
"""

import re
import os
import logging
from typing import List, Tu<PERSON>, Dict, Set
from pathlib import Path


class SecurityManager:
    """
    Manages security policies and command validation for Cybex
    """
    
    def __init__(self, config_manager):
        """Initialize security manager with configuration"""
        self.config_manager = config_manager
        self.logger = logging.getLogger(__name__)
        
        # Load security configuration
        self.security_config = config_manager.get_section('security')
        self.critical_ops = config_manager.get('critical_ops', [])
        
        # Initialize security patterns
        self._init_security_patterns()
    
    def _init_security_patterns(self) -> None:
        """Initialize security patterns for command validation"""
        # Destructive command patterns
        self.destructive_patterns = [
            r'\brm\s+.*-rf?\s*/',  # rm -rf commands
            r'\brm\s+-rf?\s+/',  # rm -rf commands
            r'\bdel\s+.*\/[fs]\s+.*\/[sq]',  # del /f /s /q commands
            r'\bformat\s+[a-z]:\s*$',  # format drive commands
            r'\bfdisk\s+.*',  # fdisk commands
            r'\bdiskpart\s*$',  # diskpart commands
            r'\bmkfs\.',  # filesystem creation
            r'\bdd\s+.*of=\/dev\/',  # dd to device
        ]
        
        # System-altering patterns
        self.system_altering_patterns = [
            r'\bsudo\s+',  # sudo commands
            r'\bchmod\s+',  # permission changes
            r'\bchown\s+',  # ownership changes
            r'\bsystemctl\s+(stop|disable|mask)',  # service management
            r'\bnet\s+(stop|start)',  # Windows service management
            r'\breg\s+(add|delete)',  # registry modifications
            r'\bapt\s+(remove|purge)',  # package removal
            r'\byum\s+(remove|erase)',  # package removal
            r'\bpip\s+uninstall',  # Python package removal
        ]
        
        # Critical operation patterns
        self.critical_patterns = [
            r'\bshutdown\s+',  # system shutdown
            r'\breboot\s*$',  # system reboot
            r'\binit\s+[06]',  # system halt/reboot
            r'\bkillall\s+',  # kill all processes
            r'\bpkill\s+',  # kill processes by name
            r'\btaskkill\s+.*\/f',  # force kill processes
        ]
        
        # Blocked command patterns (always forbidden)
        self.blocked_patterns = [
            r'sudo\s+rm\s+-rf?\s+/',  # sudo rm -rf /
            r'rm\s+-rf?\s+/home',  # rm -rf /home
            r'format\s+c:\s*$',  # format c:
            r'del\s+\/f\s+\/s\s+\/q\s+c:\\',  # del /f /s /q c:\
            r':\(\)\{\s*:\s*\|\s*:\s*&\s*\}\s*;:\s*$',  # fork bomb
        ]
    
    def validate_command(self, command: str) -> Tuple[bool, str]:
        """
        Validate if a command is safe to execute
        Returns (is_safe, reason)
        """
        if not command or not command.strip():
            return False, "Empty command"
        
        command = command.strip().lower()
        
        # Check command length
        max_length = self.security_config.get('max_command_length', 1000)
        if len(command) > max_length:
            return False, f"Command too long (max {max_length} characters)"
        
        # Check blocked commands
        blocked_commands = self.security_config.get('blocked_commands', [])
        for blocked in blocked_commands:
            if blocked.lower() in command:
                return False, f"Blocked command detected: {blocked}"
        
        # Check blocked patterns
        for pattern in self.blocked_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return False, f"Blocked command pattern detected"
        
        # Check if command tries to access forbidden paths
        if not self._validate_path_access(command):
            return False, "Access to forbidden path detected"
        
        return True, "Command is safe"
    
    def is_destructive_operation(self, command: str) -> bool:
        """Check if command is destructive (deletes/formats data)"""
        command = command.strip().lower()
        
        # Check critical operations list
        for op in self.critical_ops:
            if command.startswith(op.lower()):
                return True
        
        # Check destructive patterns
        for pattern in self.destructive_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def is_system_altering(self, command: str) -> bool:
        """Check if command alters system state"""
        command = command.strip().lower()
        
        # Destructive operations are also system-altering
        if self.is_destructive_operation(command):
            return True
        
        # Check system-altering patterns
        for pattern in self.system_altering_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        # Check for file/directory creation/modification
        creation_patterns = [
            r'\bmkdir\s+',
            r'\btouch\s+',
            r'\becho\s+.*>\s*',
            r'\bcp\s+.*',
            r'\bmv\s+.*',
            r'\bcopy\s+.*',
            r'\bmove\s+.*',
        ]
        
        for pattern in creation_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def is_critical_operation(self, command: str) -> bool:
        """Check if command is critical (affects system availability)"""
        command = command.strip().lower()
        
        # Check critical patterns
        for pattern in self.critical_patterns:
            if re.search(pattern, command, re.IGNORECASE):
                return True
        
        return False
    
    def _validate_path_access(self, command: str) -> bool:
        """Validate that command only accesses allowed directories"""
        if not self.security_config.get('enable_sandbox', True):
            return True
        
        allowed_dirs = self.security_config.get('allowed_directories', [])
        if not allowed_dirs:
            return True
        
        # Extract potential paths from command
        path_patterns = [
            r'["\']?([/\\][^"\'\s]+)["\']?',  # Absolute paths
            r'["\']?([a-zA-Z]:[/\\][^"\'\s]+)["\']?',  # Windows absolute paths
        ]
        
        found_paths = []
        for pattern in path_patterns:
            matches = re.findall(pattern, command)
            found_paths.extend(matches)
        
        # Check if any found path is outside allowed directories
        for path in found_paths:
            path = os.path.normpath(path)
            allowed = False
            
            for allowed_dir in allowed_dirs:
                allowed_dir = os.path.normpath(allowed_dir)
                if path.startswith(allowed_dir):
                    allowed = True
                    break
            
            if not allowed:
                self.logger.warning(f"Path access denied: {path}")
                return False
        
        return True
    
    def get_security_level(self, command: str) -> str:
        """
        Get security level for a command
        Returns: 'safe', 'moderate', 'critical', 'blocked'
        """
        is_safe, reason = self.validate_command(command)
        
        if not is_safe:
            return 'blocked'
        elif self.is_critical_operation(command):
            return 'critical'
        elif self.is_destructive_operation(command):
            return 'critical'
        elif self.is_system_altering(command):
            return 'moderate'
        else:
            return 'safe'
    
    def sanitize_output(self, output: str) -> str:
        """Sanitize command output to remove sensitive information"""
        if not output:
            return output
        
        # Remove potential passwords or keys
        sensitive_patterns = [
            r'password[=:]\s*\S+',
            r'key[=:]\s*\S+',
            r'token[=:]\s*\S+',
            r'secret[=:]\s*\S+',
        ]
        
        sanitized = output
        for pattern in sensitive_patterns:
            sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)
        
        return sanitized
    
    def log_security_event(self, event_type: str, command: str, details: str = "") -> None:
        """Log security-related events"""
        self.logger.warning(f"Security Event [{event_type}]: {command} - {details}")
