# 🚀 CYBEX ENTERPRISE LAUNCHERS

Guida completa ai launcher per CYBEX Enterprise con Ollama Monitor integrato.

## 📋 **LAUNCHER DISPONIBILI**

### **1. START_CYBEX.bat** ⚡
**Avvio rapido per uso quotidiano**

```bash
START_CYBEX.bat
```

**Caratteristiche:**
- ✅ Avvio immediato senza controlli estesi
- ✅ Interfaccia pulita e veloce
- ✅ Fallback automatico al launcher completo se necessario
- ✅ Ideale per uso quotidiano

---

### **2. CYBEX_ENTERPRISE_MONITOR.bat** 🏢
**Launcher completo con diagnostica**

```bash
CYBEX_ENTERPRISE_MONITOR.bat
```

**Caratteristiche:**
- ✅ Controlli completi del sistema
- ✅ Verifica Python e moduli
- ✅ Test connessione Ollama
- ✅ Conteggio modelli disponibili
- ✅ Installazione automatica dipendenze mancanti
- ✅ Diagnostica completa errori

**Output esempio:**
```
🐍 Checking Python installation...
✅ Python 3.12.0 found
🔧 Checking required modules...
✅ All modules available
🤖 Checking Ollama server...
✅ Ollama server is running
🤖 10 models available
```

---

### **3. CYBEX_DEBUG.bat** 🔧
**Modalità debug per sviluppatori**

```bash
CYBEX_DEBUG.bat
```

**Caratteristiche:**
- ✅ Logging dettagliato attivato
- ✅ Diagnostica sistema completa
- ✅ Verifica struttura CYBEX
- ✅ Test moduli Python dettagliato
- ✅ Analisi Ollama approfondita
- ✅ Traceback completo errori
- ✅ Variabili ambiente debug

**Ideale per:**
- 🔧 Risoluzione problemi
- 🔧 Sviluppo e testing
- 🔧 Analisi performance
- 🔧 Debugging errori

---

## 🎯 **QUALE LAUNCHER USARE?**

### **Per uso normale:** 
👉 **START_CYBEX.bat** - Veloce e diretto

### **Prima installazione o problemi:**
👉 **CYBEX_ENTERPRISE_MONITOR.bat** - Diagnostica completa

### **Sviluppo o debugging:**
👉 **CYBEX_DEBUG.bat** - Informazioni dettagliate

---

## 🛠️ **FUNZIONALITÀ ENTERPRISE INCLUSE**

Tutti i launcher avviano CYBEX Enterprise con:

### **🤖 Ollama Monitor Integrato**
- **LED Status Indicator** - Stato visivo in tempo reale
- **Progress Bar Animata** - Durante le operazioni AI
- **Response Time Tracking** - Monitoraggio prestazioni
- **Activity Display** - Descrizione operazioni correnti
- **Model Switch Detection** - Tracking cambio modelli

### **🛠️ 52 Enterprise Tools**
- **Core Tools (7)** - Sistema base
- **Enterprise Tools (6)** - Security, health check
- **Web Tools (4)** - Browsing, search, analysis
- **File Tools (8)** - Management avanzato
- **Database Tools (5)** - Multi-DB support
- **Development Tools (6)** - Git, Docker, testing
- **Excel Tools (3)** - Automation e analysis
- **Ollama Tools (4)** - AI integration
- **System Tools (5)** - Administration
- **Security Tools (4)** - Vulnerability, compliance

### **🎨 GUI Futuristica**
- **Design Cyberpunk** - Tema neon professionale
- **Terminale AI Integrato** - Comandi naturali
- **Model Selector** - Dropdown dinamico
- **Quick Actions** - Pulsanti rapidi
- **Status Monitoring** - Real-time system info

---

## 🔧 **RISOLUZIONE PROBLEMI**

### **Errore: "Python not found"**
```bash
# Installa Python 3.8+ e aggiungi al PATH
# Oppure usa Anaconda/Miniconda
```

### **Errore: "cybex directory not found"**
```bash
# Assicurati di essere nella cartella root di CYBEX
cd G:\Cybex
START_CYBEX.bat
```

### **Errore: "Ollama server not responding"**
```bash
# Avvia Ollama server
ollama serve

# Oppure usa CYBEX senza AI
# (funzionalità base disponibili)
```

### **Errore: "Missing required modules"**
```bash
# Il launcher installa automaticamente
# Oppure manualmente:
pip install requests psutil tkinter
```

---

## 📊 **MONITORAGGIO OLLAMA**

### **Stati LED:**
- 🔴 **Rosso** - Ollama offline
- 🟢 **Verde** - Online e idle  
- 🟠 **Arancione** - Processing (pulsante)
- 🔵 **Blu** - Idle con modelli pronti

### **Informazioni Mostrate:**
- **Modello corrente** in uso
- **Tempo di risposta** in tempo reale
- **Descrizione attività** corrente
- **Numero modelli** disponibili

### **Quick Actions:**
- **🔄 Refresh** - Aggiorna stato
- **📊 Models** - Lista modelli completa
- **🧪 Test** - Test connessione

---

## 🎮 **COMANDI DA PROVARE**

Una volta avviato CYBEX, prova questi comandi nel terminale:

### **Comandi AI:**
```
ciao
analizza sistema
stato ollama
web search "cybersecurity 2025"
```

### **Tool Enterprise:**
```
security audit
performance analysis
create excel analysis
vulnerability scan
```

### **Comandi Sistema:**
```
systeminfo
tasklist
dir
help
```

---

## 📝 **LOG E DEBUG**

### **File di Log:**
- `logs/cybex.log` - Log principale
- `logs/ollama.log` - Log Ollama specifico
- `logs/debug.log` - Debug dettagliato (modalità debug)

### **Variabili Debug:**
```bash
set CYBEX_DEBUG=1          # Abilita debug
set CYBEX_LOG_LEVEL=DEBUG  # Livello log dettagliato
```

---

## 🎯 **PROSSIMI PASSI**

1. **Scegli il launcher** appropriato
2. **Avvia CYBEX Enterprise**
3. **Testa il monitor Ollama** con alcuni comandi
4. **Esplora i 52 tool** enterprise disponibili
5. **Personalizza** secondo le tue esigenze

**CYBEX Enterprise è ora pronto per operazioni di livello enterprise con monitoraggio completo!** 🚀
