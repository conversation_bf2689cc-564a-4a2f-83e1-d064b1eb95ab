@echo off
title CYBEX Enterprise - Debug Mode
color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🔧 CYBEX ENTERPRISE - DEBUG MODE                        ║
echo ║                      Advanced Diagnostics & Monitoring                      ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Enable detailed logging
set PYTHONPATH=%CD%
set CYBEX_DEBUG=1
set CYBEX_LOG_LEVEL=DEBUG

echo 🔍 Debug mode enabled
echo 📊 Detailed logging active
echo 📁 Working directory: %CD%
echo 🐍 Python path: %PYTHONPATH%
echo.

REM System diagnostics
echo 🖥️ SYSTEM DIAGNOSTICS:
echo ═══════════════════════
python --version
echo Platform: %OS% %PROCESSOR_ARCHITECTURE%
echo User: %USERNAME%
echo Time: %DATE% %TIME%
echo.

REM Check CYBEX structure
echo 📁 CYBEX STRUCTURE CHECK:
echo ═══════════════════════════
if exist "cybex" (
    echo ✅ cybex/ directory found
) else (
    echo ❌ cybex/ directory missing
)

if exist "cybex\modules" (
    echo ✅ cybex/modules/ found
) else (
    echo ❌ cybex/modules/ missing
)

if exist "cybex\interfaces" (
    echo ✅ cybex/interfaces/ found
) else (
    echo ❌ cybex/interfaces/ missing
)

if exist "cybex\modules\ollama_monitor.py" (
    echo ✅ Ollama Monitor found
) else (
    echo ❌ Ollama Monitor missing
)

echo.

REM Check Python modules
echo 🐍 PYTHON MODULES CHECK:
echo ═══════════════════════════
python -c "
import sys
modules = ['tkinter', 'requests', 'psutil', 'threading', 'queue', 'json', 'time']
for module in modules:
    try:
        __import__(module)
        print(f'✅ {module}')
    except ImportError:
        print(f'❌ {module} - MISSING')
"
echo.

REM Ollama detailed check
echo 🤖 OLLAMA DETAILED CHECK:
echo ═══════════════════════════
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ❌ Ollama server not responding
    echo 💡 Check if Ollama is running: ollama serve
) else (
    echo ✅ Ollama server responding
    
    REM Get detailed Ollama info
    echo 📊 Ollama Models:
    curl -s http://localhost:11434/api/tags | python -c "
import sys, json
try:
    data = json.load(sys.stdin)
    models = data.get('models', [])
    print(f'   Total models: {len(models)}')
    for model in models[:5]:
        name = model.get('name', 'Unknown')
        size_gb = round(model.get('size', 0) / (1024**3), 1)
        print(f'   • {name} ({size_gb} GB)')
    if len(models) > 5:
        print(f'   ... and {len(models) - 5} more')
except:
    print('   Error parsing model data')
"
)
echo.

REM Launch with debug
echo 🚀 LAUNCHING CYBEX ENTERPRISE (DEBUG MODE):
echo ═══════════════════════════════════════════════
echo 💡 All output will be shown for debugging
echo 💡 Press Ctrl+C to stop
echo.

python -c "
import sys
sys.path.insert(0, '.')
print('🔧 Debug: Python path configured')
print('🔧 Debug: Importing CYBEX modules...')

try:
    from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI
    print('✅ Debug: GUI module imported successfully')
    
    print('🔧 Debug: Creating GUI instance...')
    app = CybexFuturisticGUI()
    print('✅ Debug: GUI instance created')
    
    print('🚀 Debug: Starting GUI...')
    app.run()
    print('✅ Debug: GUI closed normally')
    
except ImportError as e:
    print(f'❌ Debug: Import error: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Debug: Runtime error: {e}')
    import traceback
    traceback.print_exc()
    sys.exit(1)
"

echo.
echo 🔧 DEBUG SESSION ENDED
echo ═══════════════════════
echo 💡 Check the output above for any errors
echo 📝 Log files are in: logs/
pause
