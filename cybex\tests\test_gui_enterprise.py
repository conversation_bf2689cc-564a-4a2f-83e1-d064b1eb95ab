#!/usr/bin/env python3
"""
Test GUI Enterprise Integration
Verifica che i tool enterprise siano integrati nella GUI
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_gui_enterprise_integration():
    """Test integrazione enterprise nella GUI"""
    print("🏢 Testing GUI Enterprise Integration")
    print("=" * 50)
    
    try:
        from cybex.modules.agent_tools import AgentTools

        # Initialize agent tools without log manager for testing
        agent_tools = AgentTools()
        
        # Get all available tools
        all_tools = agent_tools.get_available_tools()
        
        print(f"✅ Agent tools initialized")
        print(f"✅ Total tools available: {len(all_tools)}")
        
        # Check for enterprise tools
        enterprise_tool_names = [
            "security_audit", "performance_analysis", "network_security_scan",
            "enterprise_health_check", "system_hardening", "backup_analysis"
        ]
        
        found_enterprise_tools = []
        for tool_name in enterprise_tool_names:
            if tool_name in all_tools:
                found_enterprise_tools.append(tool_name)
                print(f"  ✅ {tool_name}")
            else:
                print(f"  ❌ {tool_name} - NOT FOUND")
        
        print(f"\n🎯 Enterprise tools found: {len(found_enterprise_tools)}/6")
        
        if len(found_enterprise_tools) >= 6:
            print("🎉 ALL ENTERPRISE TOOLS INTEGRATED!")
            
            # Test one enterprise tool
            print(f"\n🔒 Testing security_audit execution...")
            try:
                result = agent_tools.execute_tool("security_audit", {})
                if result.success:
                    print("✅ Security audit executed successfully")
                    print(f"Output preview: {result.output[:200]}...")
                else:
                    print(f"❌ Security audit failed: {result.error}")
            except Exception as e:
                print(f"❌ Error executing security audit: {e}")
            
            return True
        else:
            print("❌ Not all enterprise tools are integrated")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run test"""
    print("🎯 Cybex GUI Enterprise Integration Test")
    print("=" * 60)
    
    success = test_gui_enterprise_integration()
    
    print(f"\n{'='*60}")
    
    if success:
        print("🎉 SUCCESS: Enterprise tools are integrated in GUI!")
        print("🏢 The .bat file will launch GUI with enterprise capabilities!")
        print("\n🚀 ENTERPRISE TOOLS AVAILABLE IN GUI:")
        print("  • security_audit - Professional security analysis")
        print("  • performance_analysis - Real-time performance monitoring")
        print("  • network_security_scan - Network vulnerability assessment")
        print("  • enterprise_health_check - Complete system health check")
        print("  • system_hardening - Security hardening recommendations")
        print("  • backup_analysis - Backup and recovery planning")
        print("\n💼 ENTERPRISE LEVEL: ACHIEVED IN GUI! 🏢")
    else:
        print("❌ FAILED: Enterprise tools not properly integrated")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
