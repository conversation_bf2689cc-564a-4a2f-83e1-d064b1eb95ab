#!/usr/bin/env python3
"""
Cybex Installation Script
Automated setup for Cybernetic Expert Agent
"""

import os
import sys
import subprocess
import platform
import urllib.request
import json
from pathlib import Path


class CybexInstaller:
    """Automated installer for Cybex"""
    
    def __init__(self):
        self.system = platform.system().lower()
        self.python_version = sys.version_info
        self.project_root = Path(__file__).parent
        
    def check_python_version(self):
        """Check if Python version is compatible"""
        print("🐍 Checking Python version...")
        
        if self.python_version < (3, 11):
            print(f"❌ Python 3.11+ required, found {self.python_version.major}.{self.python_version.minor}")
            return False
        
        print(f"✅ Python {self.python_version.major}.{self.python_version.minor} is compatible")
        return True
    
    def check_ollama_installation(self):
        """Check if Ollama is installed"""
        print("🤖 Checking Ollama installation...")
        
        try:
            result = subprocess.run(['ollama', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print(f"✅ Ollama found: {result.stdout.strip()}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        print("❌ Ollama not found")
        return False
    
    def install_ollama(self):
        """Install Ollama"""
        print("📦 Installing Ollama...")
        
        if self.system == "windows":
            print("Please download and install Ollama from: https://ollama.ai/download/windows")
            input("Press Enter after installing Ollama...")
        elif self.system == "darwin":  # macOS
            print("Please download and install Ollama from: https://ollama.ai/download/mac")
            input("Press Enter after installing Ollama...")
        else:  # Linux
            try:
                # Download and run Ollama install script
                install_cmd = "curl -fsSL https://ollama.ai/install.sh | sh"
                result = subprocess.run(install_cmd, shell=True, check=True)
                print("✅ Ollama installed successfully")
                return True
            except subprocess.CalledProcessError:
                print("❌ Failed to install Ollama automatically")
                print("Please install manually from: https://ollama.ai")
                return False
        
        return self.check_ollama_installation()
    
    def check_ollama_server(self):
        """Check if Ollama server is running"""
        print("🔍 Checking Ollama server...")
        
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                print("✅ Ollama server is running")
                return True
        except:
            pass
        
        print("❌ Ollama server not running")
        return False
    
    def start_ollama_server(self):
        """Start Ollama server"""
        print("🚀 Starting Ollama server...")
        
        try:
            if self.system == "windows":
                subprocess.Popen(['ollama', 'serve'], creationflags=subprocess.CREATE_NEW_CONSOLE)
            else:
                subprocess.Popen(['ollama', 'serve'])
            
            # Wait a bit for server to start
            import time
            time.sleep(3)
            
            return self.check_ollama_server()
        except Exception as e:
            print(f"❌ Failed to start Ollama server: {e}")
            return False
    
    def install_gemma_model(self):
        """Install Gemma model"""
        print("📥 Installing Gemma 2 7B model...")
        
        try:
            result = subprocess.run(['ollama', 'pull', 'gemma2:7b'], 
                                  check=True, timeout=600)  # 10 minutes timeout
            print("✅ Gemma 2 7B model installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install Gemma model")
            return False
        except subprocess.TimeoutExpired:
            print("❌ Model installation timed out")
            return False
    
    def create_virtual_environment(self):
        """Create Python virtual environment"""
        print("🐍 Creating virtual environment...")
        
        venv_path = self.project_root / "venv"
        
        try:
            subprocess.run([sys.executable, '-m', 'venv', str(venv_path)], check=True)
            print("✅ Virtual environment created")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to create virtual environment")
            return False
    
    def install_requirements(self):
        """Install Python requirements"""
        print("📦 Installing Python dependencies...")
        
        requirements_file = self.project_root / "requirements.txt"
        if not requirements_file.exists():
            print("❌ requirements.txt not found")
            return False
        
        venv_path = self.project_root / "venv"
        if self.system == "windows":
            pip_path = venv_path / "Scripts" / "pip.exe"
        else:
            pip_path = venv_path / "bin" / "pip"
        
        try:
            subprocess.run([str(pip_path), 'install', '-r', str(requirements_file)], 
                          check=True)
            print("✅ Dependencies installed successfully")
            return True
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False
    
    def create_config_directories(self):
        """Create necessary directories"""
        print("📁 Creating directories...")
        
        directories = [
            self.project_root / "logs",
            self.project_root / "cybex" / "config",
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
        
        print("✅ Directories created")
        return True
    
    def test_installation(self):
        """Test the installation"""
        print("🧪 Testing installation...")
        
        venv_path = self.project_root / "venv"
        if self.system == "windows":
            python_path = venv_path / "Scripts" / "python.exe"
        else:
            python_path = venv_path / "bin" / "python"
        
        try:
            # Test import
            result = subprocess.run([
                str(python_path), '-c', 
                'from cybex.core.cybex_core import CybexCore; print("Import successful")'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ Installation test passed")
                return True
            else:
                print(f"❌ Installation test failed: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("❌ Installation test timed out")
            return False
    
    def print_usage_instructions(self):
        """Print usage instructions"""
        venv_path = self.project_root / "venv"
        
        if self.system == "windows":
            activate_cmd = f"{venv_path}\\Scripts\\activate"
        else:
            activate_cmd = f"source {venv_path}/bin/activate"
        
        print(f"""
🎉 Cybex installation completed successfully!

To start using Cybex:

1. Activate the virtual environment:
   {activate_cmd}

2. Start Cybex:
   python main.py

3. Or run with specific options:
   python main.py --mode agent
   python main.py --help

📚 For more information, see README.md

🛡️  Remember: Cybex is a powerful tool. Use it responsibly!
""")
    
    def install(self):
        """Run the complete installation process"""
        print("🚀 Starting Cybex installation...\n")
        
        # Check Python version
        if not self.check_python_version():
            return False
        
        # Check/install Ollama
        if not self.check_ollama_installation():
            if not self.install_ollama():
                return False
        
        # Check/start Ollama server
        if not self.check_ollama_server():
            if not self.start_ollama_server():
                print("⚠️  Please start Ollama server manually: ollama serve")
        
        # Install Gemma model
        if not self.install_gemma_model():
            print("⚠️  You can install the model later with: ollama pull gemma2:7b")
        
        # Create virtual environment
        if not self.create_virtual_environment():
            return False
        
        # Install requirements
        if not self.install_requirements():
            return False
        
        # Create directories
        if not self.create_config_directories():
            return False
        
        # Test installation
        if not self.test_installation():
            print("⚠️  Installation may have issues, but you can try running Cybex")
        
        # Print usage instructions
        self.print_usage_instructions()
        
        return True


def main():
    """Main installation function"""
    installer = CybexInstaller()
    
    try:
        success = installer.install()
        if success:
            print("✅ Installation completed successfully!")
            sys.exit(0)
        else:
            print("❌ Installation failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n❌ Installation cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error during installation: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
