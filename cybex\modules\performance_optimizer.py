#!/usr/bin/env python3
"""
Performance Optimizer for CYBEX Enterprise
Implements intelligent caching, async operations, and resource management
"""

import asyncio
import time
import threading
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from collections import defaultdict, OrderedDict
import json
import pickle
import hashlib
from concurrent.futures import Thr<PERSON>PoolExecutor, ProcessPoolExecutor, as_completed
import psutil
import weakref


@dataclass
class CacheEntry:
    """Cache entry with metadata"""
    key: str
    value: Any
    timestamp: float
    access_count: int
    last_access: float
    ttl: float
    size_bytes: int
    priority: int


@dataclass
class PerformanceMetrics:
    """Performance metrics tracking"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    cpu_usage: float
    memory_usage: float
    cache_hits: int
    cache_misses: int
    async_operations: int
    resource_efficiency: float


class IntelligentCache:
    """Intelligent caching system with LRU and TTL support"""
    
    def __init__(self, max_size: int = 1000, default_ttl: float = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheEntry] = OrderedDict()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_requests': 0
        }
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache"""
        with self._lock:
            self.stats['total_requests'] += 1
            
            if key not in self.cache:
                self.stats['misses'] += 1
                return None
            
            entry = self.cache[key]
            
            # Check TTL
            if time.time() - entry.timestamp > entry.ttl:
                del self.cache[key]
                self.stats['misses'] += 1
                return None
            
            # Update access info
            entry.access_count += 1
            entry.last_access = time.time()
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            
            self.stats['hits'] += 1
            return entry.value
    
    def set(self, key: str, value: Any, ttl: Optional[float] = None, priority: int = 1) -> bool:
        """Set value in cache"""
        with self._lock:
            ttl = ttl or self.default_ttl
            size_bytes = self._estimate_size(value)
            
            # Create cache entry
            entry = CacheEntry(
                key=key,
                value=value,
                timestamp=time.time(),
                access_count=1,
                last_access=time.time(),
                ttl=ttl,
                size_bytes=size_bytes,
                priority=priority
            )
            
            # Remove existing entry if present
            if key in self.cache:
                del self.cache[key]
            
            # Add new entry
            self.cache[key] = entry
            
            # Evict if necessary
            self._evict_if_needed()
            
            return True
    
    def _estimate_size(self, value: Any) -> int:
        """Estimate size of value in bytes"""
        try:
            return len(pickle.dumps(value))
        except Exception:
            return len(str(value).encode('utf-8'))
    
    def _evict_if_needed(self):
        """Evict entries if cache is full"""
        while len(self.cache) > self.max_size:
            # Find least valuable entry (low priority, old, rarely accessed)
            min_score = float('inf')
            evict_key = None
            
            for key, entry in self.cache.items():
                # Calculate eviction score (lower = more likely to evict)
                age_factor = time.time() - entry.last_access
                access_factor = 1.0 / max(entry.access_count, 1)
                priority_factor = 1.0 / max(entry.priority, 1)
                
                score = entry.priority * entry.access_count / (age_factor + 1)
                
                if score < min_score:
                    min_score = score
                    evict_key = key
            
            if evict_key:
                del self.cache[evict_key]
                self.stats['evictions'] += 1
    
    def clear(self):
        """Clear all cache entries"""
        with self._lock:
            self.cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self._lock:
            hit_rate = self.stats['hits'] / max(self.stats['total_requests'], 1)
            return {
                **self.stats,
                'hit_rate': hit_rate,
                'cache_size': len(self.cache),
                'max_size': self.max_size
            }


class AsyncOperationManager:
    """Manages async operations for better performance"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.thread_executor = ThreadPoolExecutor(max_workers=max_workers)
        self.process_executor = ProcessPoolExecutor(max_workers=max_workers // 2)
        self.active_operations = {}
        self.operation_stats = defaultdict(list)
        self._lock = threading.Lock()
    
    async def execute_async(self, func: Callable, *args, use_process: bool = False, **kwargs) -> Any:
        """Execute function asynchronously"""
        loop = asyncio.get_event_loop()
        
        executor = self.process_executor if use_process else self.thread_executor
        
        start_time = time.time()
        try:
            result = await loop.run_in_executor(executor, func, *args)
            duration = time.time() - start_time
            
            with self._lock:
                self.operation_stats[func.__name__].append({
                    'duration': duration,
                    'success': True,
                    'timestamp': start_time
                })
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            
            with self._lock:
                self.operation_stats[func.__name__].append({
                    'duration': duration,
                    'success': False,
                    'error': str(e),
                    'timestamp': start_time
                })
            
            raise
    
    def execute_batch_async(self, operations: List[Tuple[Callable, tuple, dict]]) -> List[Any]:
        """Execute multiple operations in parallel"""
        futures = []
        
        for func, args, kwargs in operations:
            future = self.thread_executor.submit(func, *args, **kwargs)
            futures.append(future)
        
        results = []
        for future in as_completed(futures):
            try:
                result = future.result()
                results.append(result)
            except Exception as e:
                results.append(e)
        
        return results
    
    def get_operation_stats(self) -> Dict[str, Any]:
        """Get operation statistics"""
        with self._lock:
            stats = {}
            for operation, records in self.operation_stats.items():
                if records:
                    durations = [r['duration'] for r in records if r['success']]
                    success_rate = sum(1 for r in records if r['success']) / len(records)
                    
                    stats[operation] = {
                        'total_executions': len(records),
                        'success_rate': success_rate,
                        'avg_duration': sum(durations) / len(durations) if durations else 0,
                        'min_duration': min(durations) if durations else 0,
                        'max_duration': max(durations) if durations else 0
                    }
            
            return stats


class ResourceManager:
    """Intelligent resource management and optimization"""
    
    def __init__(self):
        self.resource_limits = {
            'cpu_threshold': 80.0,
            'memory_threshold': 85.0,
            'disk_threshold': 90.0,
            'max_concurrent_operations': 10
        }
        self.resource_history = defaultdict(list)
        self.active_operations = 0
        self._lock = threading.Lock()
        
        # Start resource monitoring
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitoring_thread.start()
    
    def _monitor_resources(self):
        """Monitor system resources"""
        while self.monitoring_active:
            try:
                # Get current resource usage
                cpu_percent = psutil.cpu_percent(interval=1)
                memory_percent = psutil.virtual_memory().percent
                disk_percent = psutil.disk_usage('/').percent
                
                timestamp = time.time()
                
                with self._lock:
                    self.resource_history['cpu'].append((timestamp, cpu_percent))
                    self.resource_history['memory'].append((timestamp, memory_percent))
                    self.resource_history['disk'].append((timestamp, disk_percent))
                    
                    # Keep only last hour of data
                    cutoff = timestamp - 3600
                    for resource in self.resource_history:
                        self.resource_history[resource] = [
                            (t, v) for t, v in self.resource_history[resource] if t > cutoff
                        ]
                
                time.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                time.sleep(60)  # Wait longer on error
    
    def can_execute_operation(self, operation_type: str = "default") -> Tuple[bool, str]:
        """Check if operation can be executed based on resource availability"""
        try:
            # Check concurrent operations limit
            if self.active_operations >= self.resource_limits['max_concurrent_operations']:
                return False, "Too many concurrent operations"
            
            # Check CPU usage
            cpu_percent = psutil.cpu_percent()
            if cpu_percent > self.resource_limits['cpu_threshold']:
                return False, f"CPU usage too high: {cpu_percent:.1f}%"
            
            # Check memory usage
            memory_percent = psutil.virtual_memory().percent
            if memory_percent > self.resource_limits['memory_threshold']:
                return False, f"Memory usage too high: {memory_percent:.1f}%"
            
            # Check disk usage
            disk_percent = psutil.disk_usage('/').percent
            if disk_percent > self.resource_limits['disk_threshold']:
                return False, f"Disk usage too high: {disk_percent:.1f}%"
            
            return True, "Resources available"
            
        except Exception as e:
            return False, f"Resource check failed: {str(e)}"
    
    def acquire_resources(self, operation_type: str = "default") -> bool:
        """Acquire resources for operation"""
        can_execute, reason = self.can_execute_operation(operation_type)
        if can_execute:
            with self._lock:
                self.active_operations += 1
            return True
        return False
    
    def release_resources(self, operation_type: str = "default"):
        """Release resources after operation"""
        with self._lock:
            self.active_operations = max(0, self.active_operations - 1)
    
    def get_resource_status(self) -> Dict[str, Any]:
        """Get current resource status"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent,
                'active_operations': self.active_operations,
                'max_operations': self.resource_limits['max_concurrent_operations'],
                'resource_limits': self.resource_limits
            }
        except Exception as e:
            return {'error': str(e)}
    
    def get_resource_trends(self) -> Dict[str, Any]:
        """Get resource usage trends"""
        with self._lock:
            trends = {}
            for resource, history in self.resource_history.items():
                if len(history) >= 2:
                    recent_values = [v for t, v in history[-10:]]  # Last 10 readings
                    avg_recent = sum(recent_values) / len(recent_values)
                    
                    older_values = [v for t, v in history[-20:-10]] if len(history) >= 20 else recent_values
                    avg_older = sum(older_values) / len(older_values) if older_values else avg_recent
                    
                    trend = "increasing" if avg_recent > avg_older * 1.1 else "decreasing" if avg_recent < avg_older * 0.9 else "stable"
                    
                    trends[resource] = {
                        'current': recent_values[-1] if recent_values else 0,
                        'average': avg_recent,
                        'trend': trend,
                        'history_points': len(history)
                    }
            
            return trends


class PerformanceOptimizer:
    """Main performance optimization coordinator"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Initialize components
        self.cache = IntelligentCache(max_size=2000, default_ttl=3600)
        self.async_manager = AsyncOperationManager(max_workers=6)
        self.resource_manager = ResourceManager()
        
        # Performance tracking
        self.performance_metrics = []
        self.optimization_suggestions = []
        
        # Configuration
        self.config = {
            'cache_enabled': True,
            'async_enabled': True,
            'resource_management_enabled': True,
            'performance_monitoring_enabled': True
        }
        
        if self.logger:
            self.logger.info("Performance optimizer initialized")
    
    def optimize_operation(self, operation_name: str, func: Callable, *args, **kwargs) -> Any:
        """Optimize operation execution"""
        start_time = time.time()
        start_cpu = psutil.cpu_percent()
        start_memory = psutil.virtual_memory().percent
        
        try:
            # Check cache first
            if self.config['cache_enabled']:
                cache_key = self._generate_cache_key(operation_name, args, kwargs)
                cached_result = self.cache.get(cache_key)
                if cached_result is not None:
                    self._record_performance_metrics(
                        operation_name, start_time, time.time(), start_cpu, start_memory,
                        cache_hit=True
                    )
                    return cached_result
            
            # Check resource availability
            if self.config['resource_management_enabled']:
                can_execute, reason = self.resource_manager.can_execute_operation(operation_name)
                if not can_execute:
                    raise RuntimeError(f"Cannot execute operation: {reason}")
                
                self.resource_manager.acquire_resources(operation_name)
            
            try:
                # Execute operation
                result = func(*args, **kwargs)
                
                # Cache result if enabled
                if self.config['cache_enabled']:
                    self.cache.set(cache_key, result, priority=self._calculate_cache_priority(operation_name))
                
                # Record metrics
                self._record_performance_metrics(
                    operation_name, start_time, time.time(), start_cpu, start_memory,
                    cache_hit=False
                )
                
                return result
                
            finally:
                if self.config['resource_management_enabled']:
                    self.resource_manager.release_resources(operation_name)
        
        except Exception as e:
            self._record_performance_metrics(
                operation_name, start_time, time.time(), start_cpu, start_memory,
                cache_hit=False, error=str(e)
            )
            raise
    
    def _generate_cache_key(self, operation_name: str, args: tuple, kwargs: dict) -> str:
        """Generate cache key for operation"""
        try:
            key_data = {
                'operation': operation_name,
                'args': args,
                'kwargs': kwargs
            }
            key_str = json.dumps(key_data, sort_keys=True, default=str)
            return hashlib.md5(key_str.encode()).hexdigest()
        except Exception:
            return f"{operation_name}_{hash((args, tuple(sorted(kwargs.items()))))}"
    
    def _calculate_cache_priority(self, operation_name: str) -> int:
        """Calculate cache priority for operation"""
        # Higher priority for expensive operations
        expensive_operations = ['security_audit', 'performance_analysis', 'network_security_scan']
        if operation_name in expensive_operations:
            return 10
        elif 'scan' in operation_name or 'analysis' in operation_name:
            return 5
        else:
            return 1
    
    def _record_performance_metrics(self, operation: str, start_time: float, end_time: float,
                                   start_cpu: float, start_memory: float, cache_hit: bool = False,
                                   error: str = None):
        """Record performance metrics"""
        try:
            current_cpu = psutil.cpu_percent()
            current_memory = psutil.virtual_memory().percent
            
            metrics = PerformanceMetrics(
                operation=operation,
                start_time=start_time,
                end_time=end_time,
                duration=end_time - start_time,
                cpu_usage=current_cpu - start_cpu,
                memory_usage=current_memory - start_memory,
                cache_hits=1 if cache_hit else 0,
                cache_misses=0 if cache_hit else 1,
                async_operations=0,  # Would be set by async operations
                resource_efficiency=self._calculate_resource_efficiency(end_time - start_time, current_cpu, current_memory)
            )
            
            self.performance_metrics.append(metrics)
            
            # Keep only recent metrics
            if len(self.performance_metrics) > 1000:
                self.performance_metrics = self.performance_metrics[-500:]
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Failed to record performance metrics: {e}")
    
    def _calculate_resource_efficiency(self, duration: float, cpu: float, memory: float) -> float:
        """Calculate resource efficiency score"""
        # Simple efficiency calculation (higher is better)
        if duration == 0:
            return 100.0
        
        # Penalize high resource usage and long duration
        efficiency = 100.0 - (cpu * 0.5) - (memory * 0.3) - (duration * 10)
        return max(0.0, min(100.0, efficiency))
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        try:
            cache_stats = self.cache.get_stats()
            async_stats = self.async_manager.get_operation_stats()
            resource_status = self.resource_manager.get_resource_status()
            resource_trends = self.resource_manager.get_resource_trends()
            
            # Calculate overall metrics
            if self.performance_metrics:
                avg_duration = sum(m.duration for m in self.performance_metrics) / len(self.performance_metrics)
                avg_efficiency = sum(m.resource_efficiency for m in self.performance_metrics) / len(self.performance_metrics)
                total_cache_hits = sum(m.cache_hits for m in self.performance_metrics)
                total_cache_misses = sum(m.cache_misses for m in self.performance_metrics)
            else:
                avg_duration = 0
                avg_efficiency = 0
                total_cache_hits = 0
                total_cache_misses = 0
            
            return {
                'cache_performance': cache_stats,
                'async_performance': async_stats,
                'resource_status': resource_status,
                'resource_trends': resource_trends,
                'overall_metrics': {
                    'average_operation_duration': avg_duration,
                    'average_resource_efficiency': avg_efficiency,
                    'total_operations': len(self.performance_metrics),
                    'cache_hit_ratio': total_cache_hits / max(total_cache_hits + total_cache_misses, 1)
                },
                'optimization_suggestions': self._generate_optimization_suggestions()
            }
            
        except Exception as e:
            return {'error': f'Failed to generate performance report: {str(e)}'}
    
    def _generate_optimization_suggestions(self) -> List[str]:
        """Generate optimization suggestions based on metrics"""
        suggestions = []
        
        try:
            cache_stats = self.cache.get_stats()
            resource_status = self.resource_manager.get_resource_status()
            
            # Cache optimization suggestions
            if cache_stats['hit_rate'] < 0.5:
                suggestions.append("Consider increasing cache size or TTL for better hit rates")
            
            # Resource optimization suggestions
            if resource_status['cpu_percent'] > 80:
                suggestions.append("High CPU usage detected - consider reducing concurrent operations")
            
            if resource_status['memory_percent'] > 85:
                suggestions.append("High memory usage detected - consider clearing caches or reducing memory-intensive operations")
            
            # Performance suggestions
            if self.performance_metrics:
                slow_operations = [m for m in self.performance_metrics[-100:] if m.duration > 10]
                if len(slow_operations) > 10:
                    suggestions.append("Multiple slow operations detected - consider async execution or optimization")
            
            if not suggestions:
                suggestions.append("System performance is optimal")
            
        except Exception:
            suggestions.append("Unable to generate optimization suggestions")
        
        return suggestions


def create_performance_optimizer(log_manager=None) -> PerformanceOptimizer:
    """Factory function to create performance optimizer"""
    return PerformanceOptimizer(log_manager)


# Example usage
if __name__ == "__main__":
    optimizer = create_performance_optimizer()
    
    # Test optimization
    def sample_operation(x, y):
        time.sleep(0.1)  # Simulate work
        return x + y
    
    # Optimize operation
    result = optimizer.optimize_operation("sample_add", sample_operation, 5, 10)
    print(f"Result: {result}")
    
    # Get performance report
    report = optimizer.get_performance_report()
    print(f"Performance report: {json.dumps(report, indent=2, default=str)}")
