#!/usr/bin/env python3
"""
Quick Natural Language Test
Test rapido della comunicazione in linguaggio naturale
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_memory_request():
    """Test memory status request"""
    print("🧠 Testing Memory Status Request")
    
    try:
        from cybex.core.cybex_core import Cybex<PERSON>ore
        from cybex.modules.command_executor import CommandExecutor
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.modules.natural_language_processor import NaturalLanguageProcessor
        
        # Initialize components
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, None)
        
        # Initialize NL processor
        nl_processor = NaturalLanguageProcessor(
            core,
            core.system_monitor,
            core.disk_manager
        )
        
        # Test request
        request = "dammi la situazione memoria del computer"
        print(f"📝 Richiesta: '{request}'")
        
        # Get NL patterns
        ollama_interface = OllamaInterface(core.config_manager, core.log_manager)
        nl_patterns = ollama_interface.nl_patterns
        
        # Process request
        result = nl_processor.process_request(request, nl_patterns)
        
        if result.get('success'):
            print("✅ Richiesta elaborata con successo!")
            print(f"⚡ Azione: {result.get('action')}")
            print(f"📊 Dati disponibili: {'Sì' if result.get('data') else 'No'}")
            
            # Show message preview
            message = result.get('message', '')
            if message:
                lines = message.split('\n')
                print(f"📝 Risposta (prime righe):")
                for line in lines[:5]:  # Show first 5 lines
                    print(f"   {line}")
                if len(lines) > 5:
                    print(f"   ... (altre {len(lines)-5} righe)")
            
            # Show data if available
            data = result.get('data')
            if data:
                print(f"📊 Dati strutturati:")
                for key, value in list(data.items())[:3]:
                    print(f"   • {key}: {value}")
            
            # Show recommendations
            recommendations = result.get('recommendations', [])
            if recommendations:
                print(f"💡 Raccomandazioni ({len(recommendations)}):")
                for rec in recommendations[:2]:
                    print(f"   • {rec}")
            
            return True
        else:
            print(f"❌ Errore: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Test fallito: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_disk_request():
    """Test disk status request"""
    print("\n💾 Testing Disk Status Request")
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.command_executor import CommandExecutor
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.modules.natural_language_processor import NaturalLanguageProcessor
        
        # Initialize components
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, None)
        
        # Initialize NL processor
        nl_processor = NaturalLanguageProcessor(
            core,
            core.system_monitor,
            core.disk_manager
        )
        
        # Test request
        request = "come sta il disco?"
        print(f"📝 Richiesta: '{request}'")
        
        # Get NL patterns
        ollama_interface = OllamaInterface(core.config_manager, core.log_manager)
        nl_patterns = ollama_interface.nl_patterns
        
        # Process request
        result = nl_processor.process_request(request, nl_patterns)
        
        if result.get('success'):
            print("✅ Richiesta elaborata con successo!")
            print(f"⚡ Azione: {result.get('action')}")
            
            # Show message preview
            message = result.get('message', '')
            if message:
                lines = message.split('\n')
                print(f"📝 Risposta (prime righe):")
                for line in lines[:4]:  # Show first 4 lines
                    print(f"   {line}")
            
            return True
        else:
            print(f"❌ Errore: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Test fallito: {e}")
        return False


def test_system_overview():
    """Test system overview request"""
    print("\n🖥️  Testing System Overview Request")
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.command_executor import CommandExecutor
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.modules.natural_language_processor import NaturalLanguageProcessor
        
        # Initialize components
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, None)
        
        # Initialize NL processor
        nl_processor = NaturalLanguageProcessor(
            core,
            core.system_monitor,
            core.disk_manager
        )
        
        # Test request
        request = "stato del sistema"
        print(f"📝 Richiesta: '{request}'")
        
        # Get NL patterns
        ollama_interface = OllamaInterface(core.config_manager, core.log_manager)
        nl_patterns = ollama_interface.nl_patterns
        
        # Process request
        result = nl_processor.process_request(request, nl_patterns)
        
        if result.get('success'):
            print("✅ Richiesta elaborata con successo!")
            print(f"⚡ Azione: {result.get('action')}")
            
            # Show message preview
            message = result.get('message', '')
            if message:
                lines = message.split('\n')
                print(f"📝 Risposta (prime righe):")
                for line in lines[:6]:  # Show first 6 lines
                    print(f"   {line}")
            
            return True
        else:
            print(f"❌ Errore: {result.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Test fallito: {e}")
        return False


def main():
    """Run quick natural language tests"""
    print("🎯 Cybex - Test Rapido Linguaggio Naturale")
    print("=" * 50)
    
    tests = [
        test_memory_request,
        test_disk_request,
        test_system_overview
    ]
    
    passed = 0
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test error: {e}")
    
    print(f"\n{'='*50}")
    print(f"🎯 Risultati: {passed}/{len(tests)} test passati")
    
    if passed == len(tests):
        print("🎉 Tutti i test sono passati!")
        print("✅ La comunicazione in linguaggio naturale funziona perfettamente!")
        print("\n💬 Esempi di richieste che puoi fare:")
        print("   • 'dammi la situazione memoria del computer'")
        print("   • 'come sta il disco?'")
        print("   • 'stato del sistema'")
        print("   • 'pulisci i file temporanei'")
        print("   • 'ottimizza il sistema'")
    else:
        print(f"⚠️  {len(tests) - passed} test falliti")
    
    print("=" * 50)


if __name__ == "__main__":
    main()
