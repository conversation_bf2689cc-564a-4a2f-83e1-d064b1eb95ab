#!/usr/bin/env python3
"""
Excel Tools for CYBEX Enterprise
Professional Excel file creation and analysis tools
"""

import pandas as pd
import numpy as np
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, PatternFill, Border, Side, Alignment
from openpyxl.chart import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Reference
from openpyxl.utils.dataframe import dataframe_to_rows
from openpyxl.formatting.rule import ColorScaleRule, DataBarRule
import json
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path


@dataclass
class ExcelStyle:
    """Excel styling configuration"""
    header_font: Font
    header_fill: PatternFill
    data_font: Font
    border: Border
    alignment: Alignment


class ExcelTools:
    """Professional Excel creation and analysis tools"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Professional styles
        self.styles = self._create_professional_styles()
    
    def _create_professional_styles(self) -> Dict[str, ExcelStyle]:
        """Create professional Excel styles"""
        styles = {}
        
        # Corporate Blue Theme
        styles['corporate_blue'] = ExcelStyle(
            header_font=Font(name='Calibri', size=12, bold=True, color='FFFFFF'),
            header_fill=PatternFill(start_color='1F4E79', end_color='1F4E79', fill_type='solid'),
            data_font=Font(name='Calibri', size=11),
            border=Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            ),
            alignment=Alignment(horizontal='center', vertical='center')
        )
        
        # Modern Green Theme
        styles['modern_green'] = ExcelStyle(
            header_font=Font(name='Calibri', size=12, bold=True, color='FFFFFF'),
            header_fill=PatternFill(start_color='70AD47', end_color='70AD47', fill_type='solid'),
            data_font=Font(name='Calibri', size=11),
            border=Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            ),
            alignment=Alignment(horizontal='left', vertical='center')
        )
        
        # Executive Theme
        styles['executive'] = ExcelStyle(
            header_font=Font(name='Arial', size=12, bold=True, color='FFFFFF'),
            header_fill=PatternFill(start_color='2F4F4F', end_color='2F4F4F', fill_type='solid'),
            data_font=Font(name='Arial', size=10),
            border=Border(
                left=Side(style='medium'),
                right=Side(style='medium'),
                top=Side(style='medium'),
                bottom=Side(style='medium')
            ),
            alignment=Alignment(horizontal='center', vertical='center')
        )
        
        return styles
    
    def create_professional_excel(self, data: Union[Dict, List[Dict]], 
                                 file_path: str, analysis_type: str = 'general',
                                 style_theme: str = 'corporate_blue') -> Dict[str, Any]:
        """Create professional Excel file with analysis"""
        try:
            # Convert data to DataFrame
            if isinstance(data, dict):
                df = pd.DataFrame([data])
            elif isinstance(data, list):
                df = pd.DataFrame(data)
            else:
                return {'success': False, 'error': 'Invalid data format'}
            
            if df.empty:
                return {'success': False, 'error': 'No data provided'}
            
            # Create workbook
            wb = Workbook()
            
            # Remove default sheet
            wb.remove(wb.active)
            
            # Create sheets based on analysis type
            if analysis_type == 'financial':
                return self._create_financial_analysis(wb, df, file_path, style_theme)
            elif analysis_type == 'sales':
                return self._create_sales_analysis(wb, df, file_path, style_theme)
            elif analysis_type == 'performance':
                return self._create_performance_analysis(wb, df, file_path, style_theme)
            elif analysis_type == 'system':
                return self._create_system_analysis(wb, df, file_path, style_theme)
            else:
                return self._create_general_analysis(wb, df, file_path, style_theme)
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_general_analysis(self, wb: Workbook, df: pd.DataFrame, 
                               file_path: str, style_theme: str) -> Dict[str, Any]:
        """Create general data analysis Excel"""
        try:
            # Data Sheet
            ws_data = wb.create_sheet("Data Analysis")
            
            # Add title
            ws_data['A1'] = "CYBEX ENTERPRISE - DATA ANALYSIS REPORT"
            ws_data['A1'].font = Font(size=16, bold=True)
            ws_data['A2'] = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # Add data starting from row 4
            for r in dataframe_to_rows(df, index=False, header=True):
                ws_data.append(r)
            
            # Apply styling
            self._apply_table_styling(ws_data, df.shape[0] + 1, df.shape[1], 4, style_theme)
            
            # Summary Sheet
            ws_summary = wb.create_sheet("Summary")
            self._create_summary_sheet(ws_summary, df, style_theme)
            
            # Statistics Sheet
            ws_stats = wb.create_sheet("Statistics")
            self._create_statistics_sheet(ws_stats, df, style_theme)
            
            # Charts Sheet
            if len(df.columns) > 1:
                ws_charts = wb.create_sheet("Charts")
                self._create_charts_sheet(ws_charts, df, style_theme)
            
            # Set active sheet
            wb.active = ws_summary
            
            # Save file
            wb.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'analysis_type': 'general',
                'sheets_created': [sheet.title for sheet in wb.worksheets],
                'rows_processed': len(df),
                'columns_processed': len(df.columns),
                'message': f'Professional Excel analysis created with {len(wb.worksheets)} sheets'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_financial_analysis(self, wb: Workbook, df: pd.DataFrame,
                                 file_path: str, style_theme: str) -> Dict[str, Any]:
        """Create financial analysis Excel"""
        try:
            # Executive Summary
            ws_exec = wb.create_sheet("Executive Summary")
            ws_exec['A1'] = "FINANCIAL ANALYSIS REPORT"
            ws_exec['A1'].font = Font(size=18, bold=True)
            
            # Key metrics
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                ws_exec['A3'] = "KEY FINANCIAL METRICS"
                ws_exec['A3'].font = Font(size=14, bold=True)
                
                row = 4
                for col in numeric_cols:
                    ws_exec[f'A{row}'] = f"Total {col}:"
                    ws_exec[f'B{row}'] = df[col].sum()
                    ws_exec[f'A{row+1}'] = f"Average {col}:"
                    ws_exec[f'B{row+1}'] = df[col].mean()
                    row += 3
            
            # Detailed Data
            ws_data = wb.create_sheet("Financial Data")
            for r in dataframe_to_rows(df, index=False, header=True):
                ws_data.append(r)
            
            self._apply_table_styling(ws_data, df.shape[0] + 1, df.shape[1], 1, style_theme)
            
            # Trends Analysis
            if len(numeric_cols) > 0:
                ws_trends = wb.create_sheet("Trends")
                self._create_financial_trends(ws_trends, df, numeric_cols, style_theme)
            
            wb.active = ws_exec
            wb.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'analysis_type': 'financial',
                'sheets_created': [sheet.title for sheet in wb.worksheets],
                'message': 'Financial analysis Excel created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_sales_analysis(self, wb: Workbook, df: pd.DataFrame,
                             file_path: str, style_theme: str) -> Dict[str, Any]:
        """Create sales analysis Excel"""
        try:
            # Sales Dashboard
            ws_dashboard = wb.create_sheet("Sales Dashboard")
            ws_dashboard['A1'] = "SALES PERFORMANCE DASHBOARD"
            ws_dashboard['A1'].font = Font(size=18, bold=True)
            
            # Sales metrics
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            if len(numeric_cols) > 0:
                total_sales = df[numeric_cols[0]].sum() if len(numeric_cols) > 0 else 0
                avg_sales = df[numeric_cols[0]].mean() if len(numeric_cols) > 0 else 0
                
                ws_dashboard['A3'] = "SALES SUMMARY"
                ws_dashboard['A3'].font = Font(size=14, bold=True)
                ws_dashboard['A4'] = "Total Sales:"
                ws_dashboard['B4'] = total_sales
                ws_dashboard['A5'] = "Average Sales:"
                ws_dashboard['B5'] = avg_sales
                ws_dashboard['A6'] = "Number of Records:"
                ws_dashboard['B6'] = len(df)
            
            # Raw data
            ws_data = wb.create_sheet("Sales Data")
            for r in dataframe_to_rows(df, index=False, header=True):
                ws_data.append(r)
            
            self._apply_table_styling(ws_data, df.shape[0] + 1, df.shape[1], 1, style_theme)
            
            wb.active = ws_dashboard
            wb.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'analysis_type': 'sales',
                'sheets_created': [sheet.title for sheet in wb.worksheets],
                'message': 'Sales analysis Excel created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _create_system_analysis(self, wb: Workbook, df: pd.DataFrame,
                              file_path: str, style_theme: str) -> Dict[str, Any]:
        """Create system analysis Excel"""
        try:
            # System Overview
            ws_overview = wb.create_sheet("System Overview")
            ws_overview['A1'] = "SYSTEM ANALYSIS REPORT"
            ws_overview['A1'].font = Font(size=18, bold=True)
            ws_overview['A2'] = f"Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # System metrics
            ws_overview['A4'] = "SYSTEM METRICS SUMMARY"
            ws_overview['A4'].font = Font(size=14, bold=True)
            
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            row = 5
            for col in numeric_cols:
                ws_overview[f'A{row}'] = f"{col} (Max):"
                ws_overview[f'B{row}'] = df[col].max()
                ws_overview[f'A{row+1}'] = f"{col} (Avg):"
                ws_overview[f'B{row+1}'] = df[col].mean()
                row += 2
            
            # Raw data
            ws_data = wb.create_sheet("System Data")
            for r in dataframe_to_rows(df, index=False, header=True):
                ws_data.append(r)
            
            self._apply_table_styling(ws_data, df.shape[0] + 1, df.shape[1], 1, style_theme)
            
            wb.active = ws_overview
            wb.save(file_path)
            
            return {
                'success': True,
                'file_path': file_path,
                'analysis_type': 'system',
                'sheets_created': [sheet.title for sheet in wb.worksheets],
                'message': 'System analysis Excel created successfully'
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _apply_table_styling(self, ws, rows: int, cols: int, start_row: int, style_theme: str):
        """Apply professional styling to table"""
        try:
            style = self.styles.get(style_theme, self.styles['corporate_blue'])
            
            # Header row styling
            for col in range(1, cols + 1):
                cell = ws.cell(row=start_row, column=col)
                cell.font = style.header_font
                cell.fill = style.header_fill
                cell.border = style.border
                cell.alignment = style.alignment
            
            # Data rows styling
            for row in range(start_row + 1, start_row + rows):
                for col in range(1, cols + 1):
                    cell = ws.cell(row=row, column=col)
                    cell.font = style.data_font
                    cell.border = style.border
                    cell.alignment = Alignment(horizontal='left', vertical='center')
            
            # Auto-adjust column widths
            for col in range(1, cols + 1):
                column_letter = ws.cell(row=1, column=col).column_letter
                ws.column_dimensions[column_letter].width = 15
                
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Styling error: {e}")
    
    def _create_summary_sheet(self, ws, df: pd.DataFrame, style_theme: str):
        """Create summary sheet"""
        try:
            ws['A1'] = "DATA SUMMARY"
            ws['A1'].font = Font(size=16, bold=True)
            
            # Basic info
            ws['A3'] = "Dataset Information"
            ws['A3'].font = Font(size=12, bold=True)
            ws['A4'] = "Total Rows:"
            ws['B4'] = len(df)
            ws['A5'] = "Total Columns:"
            ws['B5'] = len(df.columns)
            
            # Column info
            ws['A7'] = "Column Information"
            ws['A7'].font = Font(size=12, bold=True)
            
            row = 8
            for col in df.columns:
                ws[f'A{row}'] = col
                ws[f'B{row}'] = str(df[col].dtype)
                ws[f'C{row}'] = df[col].count()  # Non-null count
                row += 1
                
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Summary sheet error: {e}")
    
    def _create_statistics_sheet(self, ws, df: pd.DataFrame, style_theme: str):
        """Create statistics sheet"""
        try:
            ws['A1'] = "STATISTICAL ANALYSIS"
            ws['A1'].font = Font(size=16, bold=True)
            
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 0:
                # Statistical summary
                stats_df = df[numeric_cols].describe()
                
                row = 3
                for r in dataframe_to_rows(stats_df, index=True, header=True):
                    ws.append(r)
                    
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Statistics sheet error: {e}")
    
    def _create_charts_sheet(self, ws, df: pd.DataFrame, style_theme: str):
        """Create charts sheet"""
        try:
            ws['A1'] = "DATA VISUALIZATION"
            ws['A1'].font = Font(size=16, bold=True)
            
            # This is a simplified implementation
            # In a full implementation, you would create actual charts
            ws['A3'] = "Charts would be generated here based on data types"
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Charts sheet error: {e}")
    
    def _create_financial_trends(self, ws, df: pd.DataFrame, numeric_cols, style_theme: str):
        """Create financial trends analysis"""
        try:
            ws['A1'] = "FINANCIAL TRENDS ANALYSIS"
            ws['A1'].font = Font(size=16, bold=True)
            
            # Growth rates, correlations, etc.
            ws['A3'] = "Trend analysis would be implemented here"
            
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Financial trends error: {e}")
    
    def analyze_existing_excel(self, file_path: str) -> Dict[str, Any]:
        """Analyze existing Excel file"""
        try:
            # Load workbook
            wb = load_workbook(file_path)
            
            analysis = {
                'file_path': file_path,
                'sheets': [],
                'total_sheets': len(wb.worksheets),
                'file_size': Path(file_path).stat().st_size
            }
            
            for sheet in wb.worksheets:
                sheet_info = {
                    'name': sheet.title,
                    'max_row': sheet.max_row,
                    'max_column': sheet.max_column,
                    'has_data': sheet.max_row > 1
                }
                analysis['sheets'].append(sheet_info)
            
            return {'success': True, 'analysis': analysis}
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_excel_tools(log_manager=None) -> ExcelTools:
    """Factory function to create Excel tools"""
    return ExcelTools(log_manager)
