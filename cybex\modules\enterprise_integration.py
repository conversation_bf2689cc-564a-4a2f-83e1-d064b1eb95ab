#!/usr/bin/env python3
"""
Cybex Enterprise Integration
Integrazione semplificata dei tool enterprise
"""

import json
import time
import psutil
import subprocess
import platform
from typing import Dict, Any, List
from pathlib import Path

class EnterpriseTools:
    """Tool enterprise semplificati per Cybex"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        
        # Available enterprise tools
        self.tools = {
            "security_audit": self.security_audit,
            "performance_analysis": self.performance_analysis,
            "network_security_scan": self.network_security_scan,
            "enterprise_health_check": self.enterprise_health_check,
            "system_hardening": self.system_hardening,
            "backup_analysis": self.backup_analysis
        }
    
    def get_available_tools(self) -> Dict[str, str]:
        """Ottieni lista tool disponibili"""
        return {
            "security_audit": "Comprehensive security audit of the system",
            "performance_analysis": "Analyze system performance metrics",
            "network_security_scan": "Scan network for security issues",
            "enterprise_health_check": "Complete enterprise system health check",
            "system_hardening": "Apply security hardening recommendations",
            "backup_analysis": "Analyze backup and recovery capabilities"
        }
    
    def execute_tool(self, tool_name: str, parameters: Dict[str, Any] = None) -> Dict[str, Any]:
        """Esegui tool enterprise"""
        try:
            if tool_name not in self.tools:
                return {
                    "success": False,
                    "error": f"Unknown tool: {tool_name}",
                    "available_tools": list(self.tools.keys())
                }
            
            # Execute tool
            result = self.tools[tool_name](parameters or {})
            
            return {
                "success": True,
                "tool": tool_name,
                "result": result,
                "timestamp": time.time()
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name
            }
    
    def security_audit(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Audit sicurezza sistema"""
        results = {
            "timestamp": time.time(),
            "total_checks": 0,
            "critical_issues": 0,
            "warnings": 0,
            "secure_items": 0,
            "audit_results": []
        }
        
        try:
            # Windows Security Checks
            if platform.system() == "Windows":
                # Check Windows Defender
                try:
                    result = subprocess.run(
                        ['powershell', '-Command', 'Get-MpComputerStatus'],
                        capture_output=True, text=True, timeout=30
                    )
                    
                    if "AntivirusEnabled" in result.stdout and "True" in result.stdout:
                        results["audit_results"].append({
                            "category": "Antivirus",
                            "status": "secure",
                            "description": "Windows Defender is enabled",
                            "recommendation": "Keep antivirus updated",
                            "risk_level": 2
                        })
                        results["secure_items"] += 1
                    else:
                        results["audit_results"].append({
                            "category": "Antivirus",
                            "status": "critical",
                            "description": "Windows Defender is disabled",
                            "recommendation": "Enable Windows Defender immediately",
                            "risk_level": 9
                        })
                        results["critical_issues"] += 1
                    
                    results["total_checks"] += 1
                    
                except Exception:
                    results["audit_results"].append({
                        "category": "Antivirus",
                        "status": "warning",
                        "description": "Could not check antivirus status",
                        "recommendation": "Manually verify antivirus protection",
                        "risk_level": 5
                    })
                    results["warnings"] += 1
                    results["total_checks"] += 1
                
                # Check Windows Firewall
                try:
                    result = subprocess.run(
                        ['netsh', 'advfirewall', 'show', 'allprofiles', 'state'],
                        capture_output=True, text=True, timeout=30
                    )
                    
                    if "ON" in result.stdout:
                        results["audit_results"].append({
                            "category": "Firewall",
                            "status": "secure",
                            "description": "Windows Firewall is enabled",
                            "recommendation": "Review firewall rules periodically",
                            "risk_level": 2
                        })
                        results["secure_items"] += 1
                    else:
                        results["audit_results"].append({
                            "category": "Firewall",
                            "status": "critical",
                            "description": "Windows Firewall is disabled",
                            "recommendation": "Enable Windows Firewall",
                            "risk_level": 8
                        })
                        results["critical_issues"] += 1
                    
                    results["total_checks"] += 1
                    
                except Exception:
                    results["audit_results"].append({
                        "category": "Firewall",
                        "status": "warning",
                        "description": "Could not check firewall status",
                        "recommendation": "Manually verify firewall settings",
                        "risk_level": 6
                    })
                    results["warnings"] += 1
                    results["total_checks"] += 1
            
            # Network Security Check
            try:
                connections = psutil.net_connections(kind='inet')
                listening_ports = [conn.laddr.port for conn in connections if conn.status == 'LISTEN']
                
                dangerous_ports = [21, 23, 135, 139, 445, 1433, 3389]
                open_dangerous = [port for port in listening_ports if port in dangerous_ports]
                
                if open_dangerous:
                    results["audit_results"].append({
                        "category": "Network",
                        "status": "warning",
                        "description": f"Potentially dangerous ports open: {open_dangerous}",
                        "recommendation": "Review and close unnecessary ports",
                        "risk_level": 7
                    })
                    results["warnings"] += 1
                else:
                    results["audit_results"].append({
                        "category": "Network",
                        "status": "secure",
                        "description": "No obviously dangerous ports detected",
                        "recommendation": "Continue monitoring network activity",
                        "risk_level": 3
                    })
                    results["secure_items"] += 1
                
                results["total_checks"] += 1
                
            except Exception:
                results["audit_results"].append({
                    "category": "Network",
                    "status": "warning",
                    "description": "Could not check network security",
                    "recommendation": "Manually review network configuration",
                    "risk_level": 5
                })
                results["warnings"] += 1
                results["total_checks"] += 1
            
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_error(f"Security audit error: {e}")
        
        return results
    
    def performance_analysis(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analisi performance sistema"""
        results = {
            "timestamp": time.time(),
            "metrics": [],
            "critical_metrics": 0,
            "warning_metrics": 0,
            "good_metrics": 0
        }
        
        try:
            # CPU Analysis
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_status = "good" if cpu_percent < 70 else "warning" if cpu_percent < 90 else "critical"
            
            results["metrics"].append({
                "name": "CPU Usage",
                "value": cpu_percent,
                "unit": "%",
                "status": cpu_status,
                "threshold": 70
            })
            
            if cpu_status == "critical":
                results["critical_metrics"] += 1
            elif cpu_status == "warning":
                results["warning_metrics"] += 1
            else:
                results["good_metrics"] += 1
            
            # Memory Analysis
            memory = psutil.virtual_memory()
            mem_status = "good" if memory.percent < 80 else "warning" if memory.percent < 95 else "critical"
            
            results["metrics"].append({
                "name": "Memory Usage",
                "value": memory.percent,
                "unit": "%",
                "status": mem_status,
                "threshold": 80
            })
            
            if mem_status == "critical":
                results["critical_metrics"] += 1
            elif mem_status == "warning":
                results["warning_metrics"] += 1
            else:
                results["good_metrics"] += 1
            
            # Disk Analysis
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    disk_percent = (usage.used / usage.total) * 100
                    disk_status = "good" if disk_percent < 85 else "warning" if disk_percent < 95 else "critical"
                    
                    results["metrics"].append({
                        "name": f"Disk {partition.device}",
                        "value": disk_percent,
                        "unit": "%",
                        "status": disk_status,
                        "threshold": 85
                    })
                    
                    if disk_status == "critical":
                        results["critical_metrics"] += 1
                    elif disk_status == "warning":
                        results["warning_metrics"] += 1
                    else:
                        results["good_metrics"] += 1
                        
                except PermissionError:
                    continue
            
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_error(f"Performance analysis error: {e}")
        
        return results
    
    def network_security_scan(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Scansione sicurezza rete"""
        results = {
            "timestamp": time.time(),
            "open_ports": [],
            "active_connections": [],
            "security_issues": []
        }
        
        try:
            # Scan open ports
            connections = psutil.net_connections(kind='inet')
            
            for conn in connections:
                if conn.status == 'LISTEN':
                    port_info = {
                        "port": conn.laddr.port,
                        "address": conn.laddr.ip,
                        "pid": conn.pid,
                        "process": "Unknown"
                    }
                    
                    # Get process name
                    try:
                        if conn.pid:
                            process = psutil.Process(conn.pid)
                            port_info["process"] = process.name()
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
                    
                    results["open_ports"].append(port_info)
            
            # Active connections
            for conn in connections:
                if conn.status == 'ESTABLISHED':
                    conn_info = {
                        "local": f"{conn.laddr.ip}:{conn.laddr.port}",
                        "remote": f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else "N/A",
                        "status": conn.status,
                        "pid": conn.pid
                    }
                    results["active_connections"].append(conn_info)
            
            # Security analysis
            dangerous_ports = [21, 23, 135, 139, 445, 1433, 3389, 5900]
            for port_info in results["open_ports"]:
                if port_info["port"] in dangerous_ports:
                    results["security_issues"].append({
                        "type": "dangerous_port",
                        "description": f"Potentially dangerous port {port_info['port']} is open",
                        "severity": "high",
                        "recommendation": f"Review necessity of port {port_info['port']}"
                    })
            
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_error(f"Network security scan error: {e}")
            results["error"] = str(e)
        
        return results
    
    def enterprise_health_check(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Health check completo enterprise"""
        health_report = {
            "timestamp": time.time(),
            "overall_status": "unknown",
            "components": {}
        }
        
        try:
            # Security check
            security_results = self.security_audit({})
            critical_security = security_results.get("critical_issues", 0)
            health_report["components"]["security"] = {
                "status": "critical" if critical_security > 0 else "good",
                "critical_issues": critical_security,
                "details": security_results
            }
            
            # Performance check
            performance_results = self.performance_analysis({})
            critical_performance = performance_results.get("critical_metrics", 0)
            health_report["components"]["performance"] = {
                "status": "critical" if critical_performance > 0 else "good",
                "critical_metrics": critical_performance,
                "details": performance_results
            }
            
            # Network check
            network_results = self.network_security_scan({})
            security_issues = len(network_results.get("security_issues", []))
            health_report["components"]["network"] = {
                "status": "warning" if security_issues > 0 else "good",
                "security_issues": security_issues,
                "details": network_results
            }
            
            # Determine overall status
            component_statuses = [comp.get("status", "error") for comp in health_report["components"].values()]
            
            if "critical" in component_statuses:
                health_report["overall_status"] = "critical"
            elif "warning" in component_statuses:
                health_report["overall_status"] = "warning"
            else:
                health_report["overall_status"] = "good"
            
        except Exception as e:
            if self.log_manager:
                self.log_manager.log_error(f"Enterprise health check error: {e}")
            health_report["error"] = str(e)
        
        return health_report
    
    def system_hardening(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Raccomandazioni hardening sistema"""
        return {
            "status": "analysis_complete",
            "recommendations": [
                "Enable Windows Defender or install antivirus software",
                "Enable Windows Firewall with proper rules",
                "Enable User Account Control (UAC)",
                "Install latest security updates regularly",
                "Review and disable unnecessary services",
                "Configure strong password policy",
                "Enable audit logging for security events",
                "Review user accounts and remove unused accounts",
                "Enable BitLocker disk encryption",
                "Configure automatic screen lock",
                "Disable unnecessary network protocols",
                "Regular security audit and monitoring"
            ],
            "priority_actions": [
                "Enable antivirus protection",
                "Enable firewall",
                "Install security updates",
                "Review user accounts"
            ],
            "note": "These are recommendations. Manual implementation required for safety."
        }
    
    def backup_analysis(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analisi capacità backup"""
        return {
            "status": "analysis_complete",
            "backup_locations": [
                {"path": str(Path.home() / "Documents"), "priority": "high", "size_gb": "varies"},
                {"path": str(Path.home() / "Desktop"), "priority": "medium", "size_gb": "varies"},
                {"path": str(Path.home() / "Pictures"), "priority": "high", "size_gb": "varies"},
                {"path": "C:\\Program Files", "priority": "low", "size_gb": "varies"},
                {"path": "System Registry", "priority": "critical", "size_gb": "< 1"}
            ],
            "recommendations": [
                "Implement automated daily backups",
                "Use 3-2-1 backup strategy (3 copies, 2 different media, 1 offsite)",
                "Test backup restoration regularly",
                "Encrypt backup data",
                "Monitor backup job success/failure",
                "Document recovery procedures"
            ],
            "backup_tools": [
                "Windows Backup and Restore",
                "File History",
                "Third-party solutions (Acronis, Veeam)",
                "Cloud backup services"
            ]
        }
