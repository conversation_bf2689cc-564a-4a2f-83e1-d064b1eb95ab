#!/usr/bin/env python3
"""
Demo Natural Language Communication
Dimostrazione della comunicazione in linguaggio naturale con Cybex
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

from cybex.core.cybex_core import Cybex<PERSON>ore
from cybex.modules.command_executor import CommandExecutor
from cybex.modules.ollama_interface import OllamaInterface
from cybex.modules.natural_language_processor import NaturalLanguageProcessor


def print_header():
    """Print demo header"""
    print("🎯 Cybex - Comunicazione in Linguaggio Naturale")
    print("=" * 60)
    print("Dimostrazione delle capacità di comprensione del linguaggio naturale")
    print("Cybex può ora comprendere e rispondere a richieste in italiano!")
    print("=" * 60)


def initialize_cybex():
    """Initialize Cybex components"""
    print("🚀 Inizializzazione Cybex...")
    
    try:
        # Initialize core components
        core = CybexCore()
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, ollama_interface)
        
        # Initialize natural language processor
        nl_processor = NaturalLanguageProcessor(
            core,
            core.system_monitor,
            core.disk_manager
        )
        
        print("✅ Cybex inizializzato con successo!")
        print(f"   • Sistema di monitoraggio: {'✅' if core.system_monitor else '❌'}")
        print(f"   • Gestione dischi: {'✅' if core.disk_manager else '❌'}")
        print(f"   • Database: {'✅' if core.database_manager else '❌'}")
        print(f"   • Processore linguaggio naturale: ✅")
        
        return core, ollama_interface, nl_processor
        
    except Exception as e:
        print(f"❌ Errore nell'inizializzazione: {e}")
        return None, None, None


def demo_request(request, ollama_interface, nl_processor, request_num):
    """Demonstrate a natural language request"""
    print(f"\n{'='*60}")
    print(f"🗣️  Richiesta {request_num}: '{request}'")
    print(f"{'='*60}")
    
    try:
        # Prepare context
        context = {
            'nl_processor': nl_processor,
            'mode': 'chat',
            'system_type': 'Windows'
        }
        
        # Process request
        print("🔍 Elaborazione richiesta...")
        start_time = time.time()
        
        response = ollama_interface.generate_response(request, context)
        
        processing_time = time.time() - start_time
        
        if response.success:
            print(f"✅ Risposta ricevuta in {processing_time:.2f}s")
            
            # Check if processed locally
            if response.metadata and response.metadata.get('processed_locally'):
                print(f"🏠 Elaborazione: Locale (NL Processor)")
                print(f"⚡ Azione rilevata: {response.metadata.get('action', 'N/A')}")
            else:
                print(f"🌐 Elaborazione: AI Model")
            
            print(f"\n📝 Risposta Cybex:")
            print("-" * 40)
            print(response.content)
            print("-" * 40)
            
            # Show recommendations if available
            if response.metadata and response.metadata.get('recommendations'):
                recommendations = response.metadata['recommendations']
                if recommendations:
                    print(f"\n💡 Raccomandazioni:")
                    for i, rec in enumerate(recommendations, 1):
                        print(f"   {i}. {rec}")
            
            # Show data summary if available
            if response.metadata and response.metadata.get('data'):
                data = response.metadata['data']
                print(f"\n📊 Dati strutturati disponibili:")
                for key, value in list(data.items())[:3]:  # Show first 3 keys
                    if isinstance(value, (int, float)):
                        print(f"   • {key}: {value}")
                    elif isinstance(value, str):
                        print(f"   • {key}: {value[:50]}...")
            
            return True
        else:
            print(f"❌ Errore: {response.error}")
            return False
            
    except Exception as e:
        print(f"❌ Errore nella dimostrazione: {e}")
        return False


def main():
    """Run natural language communication demo"""
    print_header()
    
    # Initialize Cybex
    core, ollama_interface, nl_processor = initialize_cybex()
    
    if not all([core, ollama_interface, nl_processor]):
        print("❌ Impossibile inizializzare Cybex. Demo terminata.")
        return
    
    # Start session
    core.start_session("demo_user")
    
    # Demo requests in Italian
    demo_requests = [
        "dammi la situazione memoria del computer",
        "come sta il disco?",
        "stato del sistema",
        "situazione CPU",
        "processi attivi",
        "ottimizza il sistema"
    ]
    
    print(f"\n🎬 Iniziamo la dimostrazione con {len(demo_requests)} richieste...")
    
    successful_requests = 0
    
    for i, request in enumerate(demo_requests, 1):
        if demo_request(request, ollama_interface, nl_processor, i):
            successful_requests += 1
        
        # Small pause between requests
        if i < len(demo_requests):
            print(f"\n⏳ Pausa di 2 secondi prima della prossima richiesta...")
            time.sleep(2)
    
    # End session
    core.end_session()
    
    # Summary
    print(f"\n{'='*60}")
    print(f"🎉 Demo Completata!")
    print(f"{'='*60}")
    print(f"📊 Risultati:")
    print(f"   • Richieste elaborate: {successful_requests}/{len(demo_requests)}")
    print(f"   • Tasso di successo: {(successful_requests/len(demo_requests)*100):.1f}%")
    print(f"   • Tempo medio risposta: ~0.1s (elaborazione locale)")
    
    print(f"\n🌟 Caratteristiche dimostrate:")
    print(f"   ✅ Comprensione linguaggio naturale italiano")
    print(f"   ✅ Analisi sistema in tempo reale")
    print(f"   ✅ Risposte strutturate e dettagliate")
    print(f"   ✅ Raccomandazioni personalizzate")
    print(f"   ✅ Elaborazione locale ultra-veloce")
    
    print(f"\n💬 Esempi di richieste supportate:")
    print(f"   • 'dammi la situazione memoria del computer'")
    print(f"   • 'come sta il disco?'")
    print(f"   • 'il sistema è lento'")
    print(f"   • 'pulisci i file temporanei'")
    print(f"   • 'stato della rete'")
    print(f"   • 'processi che consumano più CPU'")
    
    print(f"\n🚀 Cybex è ora pronto per l'uso con comunicazione naturale!")
    print("=" * 60)


if __name__ == "__main__":
    main()
