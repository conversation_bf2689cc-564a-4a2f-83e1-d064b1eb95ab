#!/usr/bin/env python3
"""
CYBEX Enterprise Web Server
Modern FastAPI + WebSocket interface for futuristic UI
"""

import asyncio
import json
import os
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
import uvicorn
from fastapi import FastAP<PERSON>, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import H<PERSON><PERSON><PERSON>ponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
import logging

# Import CYBEX core
sys.path.insert(0, str(Path(__file__).parent.parent.parent))
try:
    from cybex.core.cybex_core import CybexCore
except ImportError:
    # Fallback - create minimal core
    class CybexCore:
        def __init__(self):
            self.ollama_interface = None
            self.agent_tools = None

class CybexWebServer:
    """Modern web-based interface for CYBEX Enterprise"""
    
    def __init__(self, host: str = "127.0.0.1", port: int = 8888):
        self.host = host
        self.port = port
        self.app = FastAPI(
            title="CYBEX Enterprise",
            description="Advanced AI Assistant with Enterprise Tools",
            version="2.0.0"
        )
        
        # Initialize CYBEX core
        self.cybex_core = None
        self.active_connections: List[WebSocket] = []
        
        # Setup FastAPI app
        self.setup_middleware()
        self.setup_routes()
        self.setup_websockets()
        
        # Initialize CYBEX
        self.initialize_cybex()
    
    def setup_middleware(self):
        """Setup CORS and other middleware"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def setup_routes(self):
        """Setup HTTP routes"""
        
        @self.app.get("/")
        async def root():
            """Serve main application"""
            return FileResponse("cybex/interfaces/web_ui/index.html")
        
        @self.app.get("/api/status")
        async def get_status():
            """Get system status"""
            if not self.cybex_core:
                return {"status": "initializing"}
            
            return {
                "status": "ready",
                "ollama_connected": self.cybex_core.ollama_interface.is_server_available() if self.cybex_core.ollama_interface else False,
                "tools_count": len(self.cybex_core.agent_tools.get_available_tools()) if self.cybex_core.agent_tools else 0,
                "current_model": self.cybex_core.ollama_interface.model if self.cybex_core.ollama_interface else "unknown"
            }
        
        @self.app.get("/api/models")
        async def get_models():
            """Get available Ollama models"""
            if not self.cybex_core or not self.cybex_core.ollama_interface:
                raise HTTPException(status_code=503, detail="Ollama not available")
            
            models_info = self.cybex_core.ollama_interface.get_available_models()
            return models_info
        
        @self.app.post("/api/models/{model_name}/switch")
        async def switch_model(model_name: str):
            """Switch to a different model"""
            if not self.cybex_core or not self.cybex_core.ollama_interface:
                raise HTTPException(status_code=503, detail="Ollama not available")
            
            success = self.cybex_core.ollama_interface.switch_model(model_name)
            if success:
                # Broadcast model change to all connected clients
                await self.broadcast_message({
                    "type": "model_changed",
                    "model": model_name
                })
                return {"success": True, "model": model_name}
            else:
                raise HTTPException(status_code=400, detail="Failed to switch model")
        
        @self.app.get("/api/tools")
        async def get_tools():
            """Get available tools"""
            if not self.cybex_core or not self.cybex_core.agent_tools:
                return {"tools": []}
            
            tools = self.cybex_core.agent_tools.get_available_tools()
            return {"tools": tools}
        
        @self.app.get("/api/performance/{model_name}")
        async def get_model_performance(model_name: str):
            """Get model performance statistics"""
            if not self.cybex_core or not self.cybex_core.ollama_interface:
                raise HTTPException(status_code=503, detail="Ollama not available")
            
            stats = self.cybex_core.ollama_interface.get_model_performance_stats(model_name)
            return stats
        
        # Mount static files
        static_path = Path(__file__).parent / "web_ui" / "dist"
        if static_path.exists():
            self.app.mount("/static", StaticFiles(directory=str(static_path)), name="static")
    
    def setup_websockets(self):
        """Setup WebSocket connections for real-time communication"""
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            await self.connect_websocket(websocket)
            try:
                while True:
                    # Receive message from client
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Process message
                    await self.handle_websocket_message(websocket, message)
                    
            except WebSocketDisconnect:
                await self.disconnect_websocket(websocket)
            except Exception as e:
                logging.error(f"WebSocket error: {e}")
                await self.disconnect_websocket(websocket)
    
    async def connect_websocket(self, websocket: WebSocket):
        """Handle new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        
        # Send welcome message
        await websocket.send_text(json.dumps({
            "type": "connected",
            "message": "Connected to CYBEX Enterprise",
            "timestamp": asyncio.get_event_loop().time()
        }))
    
    async def disconnect_websocket(self, websocket: WebSocket):
        """Handle WebSocket disconnection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
    
    async def handle_websocket_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        message_type = message.get("type")
        
        if message_type == "chat":
            await self.handle_chat_message(websocket, message)
        elif message_type == "execute_tool":
            await self.handle_tool_execution(websocket, message)
        elif message_type == "system_command":
            await self.handle_system_command(websocket, message)
        else:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }))
    
    async def handle_chat_message(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle chat message with AI"""
        if not self.cybex_core or not self.cybex_core.ollama_interface:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "AI not available"
            }))
            return
        
        user_message = message.get("content", "")
        
        # Send typing indicator
        await websocket.send_text(json.dumps({
            "type": "typing",
            "message": "AI is thinking..."
        }))
        
        try:
            # Generate AI response
            response = self.cybex_core.ollama_interface.generate_response(
                user_message,
                {"mode": "web_chat", "user_input": user_message}
            )
            
            if response.success:
                await websocket.send_text(json.dumps({
                    "type": "ai_response",
                    "content": response.content,
                    "model": response.model,
                    "timestamp": asyncio.get_event_loop().time()
                }))
            else:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"AI Error: {response.error}"
                }))
                
        except Exception as e:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Processing error: {str(e)}"
            }))
    
    async def handle_tool_execution(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle tool execution request"""
        if not self.cybex_core or not self.cybex_core.agent_tools:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": "Tools not available"
            }))
            return
        
        tool_name = message.get("tool")
        tool_params = message.get("params", {})
        
        try:
            # Execute tool
            result = self.cybex_core.agent_tools.execute_tool(tool_name, tool_params)
            
            await websocket.send_text(json.dumps({
                "type": "tool_result",
                "tool": tool_name,
                "success": result.success,
                "output": result.output,
                "error": result.error,
                "metadata": result.metadata
            }))
            
        except Exception as e:
            await websocket.send_text(json.dumps({
                "type": "error",
                "message": f"Tool execution error: {str(e)}"
            }))
    
    async def handle_system_command(self, websocket: WebSocket, message: Dict[str, Any]):
        """Handle system command"""
        command = message.get("command", "")
        
        # Process system commands
        if command == "get_system_info":
            # Return system information
            await websocket.send_text(json.dumps({
                "type": "system_info",
                "data": {
                    "python_version": sys.version,
                    "platform": sys.platform,
                    "cybex_version": "2.0.0"
                }
            }))
    
    async def broadcast_message(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        if self.active_connections:
            message_text = json.dumps(message)
            for connection in self.active_connections.copy():
                try:
                    await connection.send_text(message_text)
                except:
                    # Remove disconnected clients
                    if connection in self.active_connections:
                        self.active_connections.remove(connection)
    
    def initialize_cybex(self):
        """Initialize CYBEX core systems"""
        try:
            self.cybex_core = CybexCore()
            logging.info("CYBEX core initialized successfully")
        except Exception as e:
            logging.error(f"Failed to initialize CYBEX core: {e}")
            self.cybex_core = None
    
    def run(self):
        """Run the web server"""
        print(f"""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 CYBEX ENTERPRISE WEB SERVER                          ║
║                      Futuristic Web Interface                               ║
║                        http://{self.host}:{self.port}                                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)
        
        uvicorn.run(
            self.app,
            host=self.host,
            port=self.port,
            log_level="info",
            access_log=True
        )

if __name__ == "__main__":
    server = CybexWebServer()
    server.run()
