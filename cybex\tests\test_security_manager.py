"""
Test suite for SecurityManager module
"""

import unittest
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent.parent))

from cybex.modules.security_manager import SecurityManager
from cybex.modules.config_manager import ConfigManager


class TestSecurityManager(unittest.TestCase):
    """Test cases for SecurityManager"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.config_manager = ConfigManager()
        self.security_manager = SecurityManager(self.config_manager)
    
    def test_validate_safe_command(self):
        """Test validation of safe commands"""
        safe_commands = [
            "ls -la",
            "dir",
            "systeminfo",
            "hostname",
            "ping google.com"
        ]
        
        for command in safe_commands:
            is_safe, reason = self.security_manager.validate_command(command)
            self.assertTrue(is_safe, f"Command '{command}' should be safe: {reason}")
    
    def test_validate_dangerous_command(self):
        """Test validation of dangerous commands"""
        dangerous_commands = [
            "sudo rm -rf /",
            "format c:",
            "del /f /s /q c:\\",
            "rm -rf /home"
        ]
        
        for command in dangerous_commands:
            is_safe, reason = self.security_manager.validate_command(command)
            self.assertFalse(is_safe, f"Command '{command}' should be blocked")
    
    def test_destructive_operation_detection(self):
        """Test detection of destructive operations"""
        destructive_commands = [
            "rm -rf /tmp/test",
            "del /f test.txt",
            "format d:",
            "fdisk /dev/sdb"
        ]
        
        for command in destructive_commands:
            is_destructive = self.security_manager.is_destructive_operation(command)
            self.assertTrue(is_destructive, f"Command '{command}' should be detected as destructive")
    
    def test_system_altering_detection(self):
        """Test detection of system-altering operations"""
        altering_commands = [
            "sudo systemctl stop nginx",
            "chmod 777 /etc/passwd",
            "chown root:root file.txt",
            "net stop spooler"
        ]
        
        for command in altering_commands:
            is_altering = self.security_manager.is_system_altering(command)
            self.assertTrue(is_altering, f"Command '{command}' should be detected as system-altering")
    
    def test_critical_operation_detection(self):
        """Test detection of critical operations"""
        critical_commands = [
            "shutdown -h now",
            "reboot",
            "killall -9 nginx",
            "taskkill /f /im explorer.exe"
        ]
        
        for command in critical_commands:
            is_critical = self.security_manager.is_critical_operation(command)
            self.assertTrue(is_critical, f"Command '{command}' should be detected as critical")
    
    def test_security_level_classification(self):
        """Test security level classification"""
        test_cases = [
            ("ls -la", "safe"),
            ("mkdir test", "moderate"),
            ("rm -rf /tmp/test", "critical"),
            ("sudo rm -rf /", "blocked")
        ]
        
        for command, expected_level in test_cases:
            actual_level = self.security_manager.get_security_level(command)
            self.assertEqual(actual_level, expected_level, 
                           f"Command '{command}' should have security level '{expected_level}', got '{actual_level}'")
    
    def test_output_sanitization(self):
        """Test output sanitization"""
        sensitive_output = "password=secret123 key=abc123 token=xyz789"
        sanitized = self.security_manager.sanitize_output(sensitive_output)
        
        self.assertNotIn("secret123", sanitized)
        self.assertNotIn("abc123", sanitized)
        self.assertNotIn("xyz789", sanitized)
        self.assertIn("[REDACTED]", sanitized)
    
    def test_empty_command_validation(self):
        """Test validation of empty commands"""
        empty_commands = ["", "   ", "\t", "\n"]
        
        for command in empty_commands:
            is_safe, reason = self.security_manager.validate_command(command)
            self.assertFalse(is_safe, f"Empty command should not be valid")
    
    def test_command_length_validation(self):
        """Test command length validation"""
        # Create a very long command
        long_command = "ls " + "a" * 2000
        
        is_safe, reason = self.security_manager.validate_command(long_command)
        self.assertFalse(is_safe, "Very long command should be rejected")
        self.assertIn("too long", reason.lower())


if __name__ == '__main__':
    unittest.main()
