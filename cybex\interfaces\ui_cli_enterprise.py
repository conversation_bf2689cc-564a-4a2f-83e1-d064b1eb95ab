"""
Cybex CLI Interface - Enterprise Edition
Advanced Command Line Interface for Cybex system administration
Features: Natural Language Processing, Quick Actions, Model Configuration
"""

import os
import sys
import time
import platform
import json
from typing import Dict, List, Optional, Tuple
from pathlib import Path
from colorama import init, Fore, Back, Style

# Initialize colorama for Windows compatibility
init(autoreset=True)

# Add parent directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent))

from cybex.core.cybex_core import CybexCore
from cybex.modules.command_executor import CommandExecutor
from cybex.modules.ollama_interface import OllamaInterface
from cybex.modules.natural_language_processor import NaturalLanguageProcessor


# Sunrise Color Palette
class SunriseColors:
    """Sunrise color palette for Cybex Enterprise"""
    BLUE = '\033[38;2;117;189;224m'      # 75bde0
    YELLOW = '\033[38;2;248;212;155m'    # f8d49b  
    ORANGE = '\033[38;2;248;188;155m'    # f8bc9b
    RED = '\033[38;2;251;155;155m'       # fb9b9b
    WHITE = '\033[97m'
    GRAY = '\033[90m'
    RESET = '\033[0m'
    
    # Status colors
    SUCCESS = BLUE
    WARNING = YELLOW
    ERROR = RED
    INFO = ORANGE
    ACCENT = WHITE
    
    # Background colors
    BG_BLUE = '\033[48;2;117;189;224m'
    BG_DARK = '\033[48;2;30;30;30m'


class QuickAction:
    """Quick action definition"""
    def __init__(self, key: str, name: str, description: str, action: str, icon: str = "•"):
        self.key = key
        self.name = name
        self.description = description
        self.action = action
        self.icon = icon


class CybexEnterpriseUI:
    """
    Enterprise-grade CLI interface for Cybex
    """
    
    def __init__(self):
        """Initialize Enterprise CLI interface"""
        self.running = False
        self.core = None
        self.command_executor = None
        self.ollama_interface = None
        self.nl_processor = None
        self.logger = None
        
        # CLI state
        self.current_directory = os.getcwd()
        self.current_mode = "chat"  # chat or agent
        self.show_colors = True
        self.in_chat_mode = False  # Track if we're in continuous chat
        self.chat_history = []  # Store chat history for saving
        self.show_operations_panel = False  # Show operations monitoring panel

        # Initialize agent tools and operation monitor
        self._init_agent_tools()
        
        # Quick actions (DISABLED - using advanced AI tools instead)
        self.quick_actions = {}  # Disabled to allow AI to use advanced tools
        
        # Available models (will be populated dynamically)
        self.available_models = []
        self._load_available_models()
        
        # Initialize components
        self._initialize_components()

    def _load_available_models(self):
        """Load available Ollama models from system"""
        try:
            import requests

            # Try to get models from Ollama API
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])

                # Extract model names
                self.available_models = []
                for model in models:
                    name = model.get('name', '')
                    if name:
                        self.available_models.append(name)

                # Sort models alphabetically
                self.available_models.sort()

                if self.available_models:
                    print(f"{SunriseColors.SUCCESS}✅ Found {len(self.available_models)} Ollama models{SunriseColors.RESET}")
                else:
                    print(f"{SunriseColors.WARNING}⚠️  No Ollama models found{SunriseColors.RESET}")
                    self._set_default_models()
            else:
                print(f"{SunriseColors.WARNING}⚠️  Could not connect to Ollama API{SunriseColors.RESET}")
                self._set_default_models()

        except Exception as e:
            print(f"{SunriseColors.WARNING}⚠️  Error loading models: {e}{SunriseColors.RESET}")
            self._set_default_models()

    def _set_default_models(self):
        """Set default models if Ollama is not available"""
        self.available_models = [
            "gemma2:7b",
            "llama3.1:8b",
            "mistral:7b",
            "codellama:7b",
            "phi3:mini"
        ]
        print(f"{SunriseColors.INFO}ℹ️  Using default model list{SunriseColors.RESET}")

    def _init_agent_tools(self):
        """Initialize agent tools and operation monitor"""
        try:
            from cybex.modules.agent_tools import AgentTools
            from cybex.modules.operation_monitor import init_global_monitor, get_global_monitor

            # Initialize operation monitor
            self.operation_monitor = init_global_monitor(
                self.core.log_manager if self.core else None
            )

            # Initialize agent tools
            self.agent_tools = AgentTools(
                self.core.log_manager if self.core else None
            )

            # Add operation monitor observer for UI updates
            self.operation_monitor.add_observer(self._on_operation_update)

            print(f"{SunriseColors.SUCCESS}✅ Agent tools initialized{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Failed to initialize agent tools: {e}{SunriseColors.RESET}")
            self.agent_tools = None
            self.operation_monitor = None

    def _on_operation_update(self, operation, event_type):
        """Handle operation updates for UI refresh"""
        # This will be called when operations change
        # We can use this to trigger UI updates if needed
        pass

    def _init_quick_actions(self) -> Dict[str, QuickAction]:
        """Initialize quick actions"""
        return {
            '1': QuickAction('1', 'System Status', 'Complete system overview', 'stato del sistema', '🖥️'),
            '2': QuickAction('2', 'Memory Status', 'RAM usage and recommendations', 'dammi la situazione memoria del computer', '🧠'),
            '3': QuickAction('3', 'Disk Status', 'Storage analysis and health', 'come sta il disco?', '💾'),
            '4': QuickAction('4', 'CPU Status', 'Processor usage and performance', 'situazione CPU', '⚡'),
            '5': QuickAction('5', 'Network Status', 'Network connectivity and traffic', 'stato della rete', '🌐'),
            '6': QuickAction('6', 'Active Processes', 'Running processes analysis', 'processi attivi', '🔄'),
            '7': QuickAction('7', 'System Cleanup', 'Clean temporary files', 'pulisci i file temporanei', '🧹'),
            '8': QuickAction('8', 'Optimize System', 'Performance optimization', 'ottimizza il sistema', '⚡'),
            '9': QuickAction('9', 'Performance Analysis', 'Detailed performance report', 'analizza le performance', '📊'),
        }
    
    def _initialize_components(self) -> bool:
        """Initialize Cybex components"""
        try:
            # Initialize core
            self.core = CybexCore()
            self.logger = self.core.log_manager.get_logger(__name__)
            
            # Initialize command executor
            self.command_executor = CommandExecutor(
                self.core.config_manager,
                self.core.security_manager,
                self.core.log_manager
            )
            
            # Initialize Ollama interface
            self.ollama_interface = OllamaInterface(
                self.core.config_manager,
                self.core.log_manager
            )
            
            # Initialize agent modules
            self.core._init_agent_modules(self.command_executor, self.ollama_interface)
            
            # Initialize natural language processor
            self.nl_processor = NaturalLanguageProcessor(
                self.core,
                self.core.system_monitor,
                self.core.disk_manager
            )
            
            self.logger.info("Enterprise CLI initialized successfully")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to initialize components: {e}")
            else:
                print(f"Failed to initialize components: {e}")
            return False
    
    def _print_banner(self):
        """Print Cybex Enterprise banner"""
        banner = f"""
{SunriseColors.BLUE}╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   {SunriseColors.WHITE}  ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝                                 {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗                                 {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE}  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝                                {SunriseColors.BLUE}║
║                                                                              ║
║   {SunriseColors.ORANGE}                    Enterprise Edition                                    {SunriseColors.BLUE}║
║   {SunriseColors.GRAY}                      by AGTECHdesigne                                     {SunriseColors.BLUE}║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}

{SunriseColors.INFO}🚀 Cybernetic Expert Agent - Advanced System Administration{SunriseColors.RESET}
{SunriseColors.GRAY}   Natural Language Processing • Real-time Monitoring • Intelligent Automation{SunriseColors.RESET}
"""
        print(banner)
    
    def _print_status_bar(self):
        """Print current status bar"""
        mode_color = SunriseColors.SUCCESS if self.current_mode == "chat" else SunriseColors.WARNING
        mode_icon = "💬" if self.current_mode == "chat" else "🤖"
        
        # Get system info
        cpu_usage = "N/A"
        memory_usage = "N/A"
        
        if self.core and self.core.system_monitor:
            try:
                current_state = self.core.system_monitor.get_current_state()
                if current_state:
                    cpu_usage = f"{current_state.get('cpu', {}).get('percent', 0):.1f}%"
                    memory_usage = f"{current_state.get('memory', {}).get('percent', 0):.1f}%"
            except:
                pass
        
        status_bar = f"""
{SunriseColors.BLUE}┌─────────────────────────────────────────────────────────────────────────────┐
│ {mode_icon} Mode: {mode_color}{self.current_mode.upper()}{SunriseColors.BLUE} │ CPU: {SunriseColors.ORANGE}{cpu_usage}{SunriseColors.BLUE} │ RAM: {SunriseColors.ORANGE}{memory_usage}{SunriseColors.BLUE} │ System: {SunriseColors.SUCCESS}{platform.system()}{SunriseColors.BLUE} │
└─────────────────────────────────────────────────────────────────────────────┘{SunriseColors.RESET}
"""
        print(status_bar)
    
    def _print_quick_actions_menu(self):
        """Print AI agent menu (quick actions disabled)"""
        print(f"\n{SunriseColors.ACCENT}╔═══════════════════════════════════════════════════════════════════════════════╗{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}║                           {SunriseColors.SUCCESS}🤖 AI AGENT MODE{SunriseColors.ACCENT}                                    ║{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}╠═══════════════════════════════════════════════════════════════════════════════╣{SunriseColors.RESET}")
        print(f"║                                                                               ║")
        print(f"║ {SunriseColors.SUCCESS}🎯 ADVANCED AI CAPABILITIES:{SunriseColors.ACCENT}                                                  ║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.INFO}• Disk Analysis & Cleanup{SunriseColors.ACCENT}     {SunriseColors.WARNING}• System Commands{SunriseColors.ACCENT}     {SunriseColors.SUCCESS}• File Operations{SunriseColors.ACCENT}    ║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.INFO}• Process Management{SunriseColors.ACCENT}          {SunriseColors.WARNING}• Network Scanning{SunriseColors.ACCENT}    {SunriseColors.SUCCESS}• Real-time Monitoring{SunriseColors.ACCENT} ║{SunriseColors.RESET}")
        print(f"║                                                                               ║")
        print(f"║ {SunriseColors.SUCCESS}💬 NATURAL LANGUAGE EXAMPLES:{SunriseColors.ACCENT}                                                ║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}• \"Scansiona disco C\"                                                     {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}• \"Elimina file temp dal disco C\"                                        {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}• \"Mostrami i processi che usano più CPU\"                                {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}• \"Controlla connessioni di rete\"                                        {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}• \"Esegui comando systeminfo\"                                            {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║                                                                               ║")
        print(f"║ {SunriseColors.SUCCESS}⚡ DIRECT TOOL COMMANDS:{SunriseColors.ACCENT}                                                     ║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}/tools                    - Show available tools                          {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}/ops                      - Toggle operations monitor                     {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║   {SunriseColors.GRAY}/tool scan_disk drive=C   - Direct disk scan                             {SunriseColors.ACCENT}║{SunriseColors.RESET}")
        print(f"║                                                                               ║")
        print(f"{SunriseColors.ACCENT}╠═══════════════════════════════════════════════════════════════════════════════╣{SunriseColors.RESET}")
        print(f"║ {SunriseColors.INFO}⚙️  [C] Configuration{SunriseColors.ACCENT}  │  {SunriseColors.WARNING}🔄 [M] Switch Mode{SunriseColors.ACCENT}  │  {SunriseColors.ERROR}❌ [Q] Quit{SunriseColors.ACCENT}           ║{SunriseColors.RESET}")
        print(f"║                                                                               ║")
        print(f"║ {SunriseColors.ACCENT}💬 Type any message to start AI conversation • Press [H] for help{SunriseColors.ACCENT}        ║{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}")
    
    def _print_help(self):
        """Print help information"""
        # Get current model
        current_model = "gemma3:4b"
        if self.core and self.core.config_manager:
            try:
                current_model = self.core.config_manager.get('ollama.model', 'gemma3:4b')
            except:
                pass

        help_text = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                                {SunriseColors.ACCENT}📖 CYBEX HELP{SunriseColors.SUCCESS}                                    ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🤖 AI AGENT CAPABILITIES:{SunriseColors.SUCCESS}                                                   ║
║   {SunriseColors.GRAY}• Advanced system analysis with AI reasoning                               {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• Natural language commands for complex operations                         {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• Real-time tool execution with monitoring                                 {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• Safe disk operations with preview mode                                   {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ACCENT}🤖 AI CHAT MODE (Continuous):{SunriseColors.SUCCESS}                                               ║
║   {SunriseColors.GRAY}• Type any message to start continuous chat with AI                         {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• Current model: {current_model:<25}                                {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• Chat continues without interruptions                                       {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• Use /exit to return to menu, /save to save chat                           {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ACCENT}🗣️  NATURAL LANGUAGE COMMANDS:{SunriseColors.SUCCESS}                                              ║
║   {SunriseColors.GRAY}• "dammi la situazione memoria del computer" (processed locally)           {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• "come sta il disco?" (instant system analysis)                           {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• "stato del sistema" (real-time monitoring)                               {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• "il sistema è lento" (performance analysis)                              {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ACCENT}🔧 BUILT-IN COMMANDS:{SunriseColors.SUCCESS}                                                        ║
║   {SunriseColors.GRAY}• help, h          - Show this help                                          {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• config, c        - Configuration (change AI model)                        {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• mode, m          - Switch Chat/Agent mode                                 {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• status, s        - System status overview                                  {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• clear, cls       - Clear screen                                            {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• quit, q, exit    - Exit Cybex                                             {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ACCENT}⚡ DIRECT EXECUTION:{SunriseColors.SUCCESS}                                                         ║
║   {SunriseColors.GRAY}• !<command>       - Execute system command directly                         {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• !dir             - List directory contents                                 {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}• !systeminfo      - Show system information                                {SunriseColors.SUCCESS}║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
        print(help_text)
    
    def start(self):
        """Start the Enterprise CLI interface"""
        try:
            self.running = True
            
            # Clear screen and show banner
            os.system('cls' if os.name == 'nt' else 'clear')
            self._print_banner()
            
            # Start session
            if self.core:
                self.core.start_session("enterprise_user")
            
            # Main interaction loop
            while self.running:
                try:
                    # Show menu only if not in continuous chat mode
                    if not self.in_chat_mode:
                        self._print_status_bar()
                        self._print_quick_actions_menu()

                    # Show operations panel if enabled and in chat mode
                    if self.in_chat_mode and self.show_operations_panel:
                        self._show_operations_status()

                    # Get user input with appropriate prompt
                    if self.in_chat_mode:
                        prompt = f"\n{SunriseColors.ACCENT}💬 You{SunriseColors.RESET} {SunriseColors.BLUE}❯{SunriseColors.RESET} "
                    else:
                        prompt = f"\n{SunriseColors.ACCENT}cybex{SunriseColors.GRAY}@{SunriseColors.INFO}enterprise{SunriseColors.RESET} {SunriseColors.BLUE}❯{SunriseColors.RESET} "

                    user_input = input(prompt).strip()

                    if not user_input:
                        continue

                    # Handle input
                    self._handle_input(user_input)
                    
                except KeyboardInterrupt:
                    print(f"\n{SunriseColors.WARNING}⚠️  Use 'quit' or 'q' to exit gracefully{SunriseColors.RESET}")
                    continue
                except EOFError:
                    break
        
        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Fatal error: {e}{SunriseColors.RESET}")
        
        finally:
            self._shutdown()
    
    def _handle_input(self, user_input: str):
        """Handle user input"""
        # Convert to lowercase for command matching
        cmd = user_input.lower().strip()

        # Special commands in chat mode
        if self.in_chat_mode:
            if cmd in ['/exit', '/quit', '/back', '/menu']:
                self._exit_chat_mode()
                return
            elif cmd in ['/save']:
                self._save_current_chat()
                return
            elif cmd in ['/clear']:
                self._clear_chat_history()
                return
            elif cmd in ['/help']:
                self._show_chat_help()
                return
            elif cmd in ['/ops', '/operations']:
                self._toggle_operations_panel()
                return
            elif cmd in ['/tools']:
                self._show_available_tools()
                return
            elif cmd.startswith('/tool '):
                self._execute_tool_command(user_input[6:])
                return

        # Quick actions disabled - all input goes to AI agent
        # (Quick actions removed to allow AI to use advanced tools)

        # Built-in commands - exit chat mode first
        if cmd in ['help', 'h']:
            if self.in_chat_mode:
                self._exit_chat_mode()
            self._print_help()
            return

        if cmd in ['config', 'c']:
            if self.in_chat_mode:
                self._exit_chat_mode()
            self._show_configuration()
            return

        if cmd in ['mode', 'm']:
            if self.in_chat_mode:
                self._exit_chat_mode()
            self._switch_mode()
            return

        if cmd.startswith('mode '):
            new_mode = cmd.split(' ', 1)[1]
            if new_mode in ['chat', 'agent']:
                self._set_mode(new_mode)
            else:
                print(f"{SunriseColors.ERROR}❌ Invalid mode. Use 'chat' or 'agent'{SunriseColors.RESET}")
            return

        if cmd in ['clear', 'cls']:
            os.system('cls' if os.name == 'nt' else 'clear')
            self._print_banner()
            return

        if cmd in ['quit', 'q', 'exit']:
            # Ask to save chat if there's history
            if self.chat_history:
                self._ask_save_chat_on_exit()
            self.running = False
            return

        if cmd in ['status', 's']:
            self._show_system_status()
            return

        # Direct command execution
        if user_input.startswith('!'):
            command = user_input[1:]
            self._execute_system_command(command)
            return

        # Check if it's a natural language request that can be processed locally
        # or if it should go directly to AI model
        self._process_user_message(user_input)
    
    def _execute_quick_action(self, action: QuickAction):
        """Execute a quick action"""
        self._process_natural_language(action.action)

    def _process_user_message(self, user_input: str):
        """Process user message - either natural language or direct AI chat"""
        try:
            # First, try to process as natural language command
            if self.nl_processor:
                nl_result = self.nl_processor.process_request(user_input, self.ollama_interface.nl_patterns)

                # If it's a recognized natural language command, process it locally
                if nl_result.get('success') and nl_result.get('action'):
                    print(f"\n{SunriseColors.SUCCESS}🤖 Cybex (Local Analysis):{SunriseColors.RESET}")
                    print(nl_result['message'])

                    # Show recommendations
                    recommendations = nl_result.get('recommendations', [])
                    if recommendations:
                        print(f"\n{SunriseColors.WARNING}💡 Recommendations:{SunriseColors.RESET}")
                        for i, rec in enumerate(recommendations, 1):
                            print(f"  {i}. {rec}")

                    # Wait for user to continue
                    input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")
                    return

            # If not a recognized command, send directly to AI model
            self._chat_with_ai(user_input)

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error processing message: {e}{SunriseColors.RESET}")
            input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")

    def _chat_with_ai(self, user_input: str):
        """Direct chat with AI model"""
        try:
            print(f"\n{SunriseColors.INFO}🤖 Sending to AI model...{SunriseColors.RESET}")

            # Get current model
            current_model = "gemma3:4b"  # Default from config
            if self.core and self.core.config_manager:
                try:
                    current_model = self.core.config_manager.get('ollama.model', 'gemma3:4b')
                except:
                    pass

            # Calculate expected timeout
            timeout = self._get_model_timeout(current_model)

            print(f"{SunriseColors.GRAY}Model: {current_model}{SunriseColors.RESET}")
            if timeout > 300:  # More than 5 minutes
                print(f"{SunriseColors.WARNING}⏱️  Large model detected - may take up to {timeout//60} minutes{SunriseColors.RESET}")
            elif timeout > 120:  # More than 2 minutes
                print(f"{SunriseColors.INFO}⏱️  Processing may take up to {timeout//60} minutes{SunriseColors.RESET}")

            # Prepare context for AI with agent tools
            context = {
                'mode': self.current_mode,
                'system_type': self.core.system_type if self.core else 'Unknown',
                'current_directory': self.current_directory,
                'model': current_model,
                'agent_tools': self.agent_tools,
                'operation_monitor': self.operation_monitor
                # Deliberately not including nl_processor to force AI processing
            }

            # Generate response from AI
            start_time = time.time()
            response = self.ollama_interface.generate_response(user_input, context)
            processing_time = time.time() - start_time

            if response.success:
                print(f"\n{SunriseColors.SUCCESS}🤖 {current_model}:{SunriseColors.RESET}")
                print(f"{SunriseColors.GRAY}Response time: {processing_time:.2f}s{SunriseColors.RESET}")
                print()

                # Display AI response with proper formatting
                self._display_ai_response(response.content)

                # Show metadata if available
                if response.metadata:
                    tokens = response.metadata.get('tokens_estimated', 0)
                    if tokens > 0:
                        print(f"\n{SunriseColors.GRAY}Tokens: ~{tokens}{SunriseColors.RESET}")
            else:
                print(f"{SunriseColors.ERROR}❌ AI Error: {response.error}{SunriseColors.RESET}")

                # Provide troubleshooting suggestions
                print(f"\n{SunriseColors.INFO}💡 Troubleshooting:{SunriseColors.RESET}")
                print(f"  • Check that Ollama is running")
                print(f"  • Verify model '{current_model}' is installed")
                print(f"  • Try: ollama pull {current_model}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Chat error: {e}{SunriseColors.RESET}")

        # Add to chat history for potential saving
        if response and response.success:
            self.chat_history.append({
                'timestamp': time.time(),
                'user': user_input,
                'assistant': response.content,
                'model': current_model,
                'response_time': processing_time
            })

        # Enter continuous chat mode
        self.in_chat_mode = True

    def _get_model_timeout(self, model_name: str) -> int:
        """Get expected timeout for a model"""
        try:
            model_lower = model_name.lower()
            base_timeout = 180  # 3 minutes base

            if '27b' in model_lower or '24b' in model_lower:
                return base_timeout * 3  # 9 minutes
            elif '8b' in model_lower or '7b' in model_lower:
                return base_timeout * 2  # 6 minutes
            elif '4b' in model_lower or '1.5b' in model_lower:
                return int(base_timeout * 1.5)  # 4.5 minutes
            else:
                return base_timeout * 2  # Default to 6 minutes for unknown
        except:
            return 360  # 6 minutes default

    def _display_ai_response(self, content: str):
        """Display AI response with proper formatting"""
        # Split content into lines for better formatting
        lines = content.split('\n')

        for line in lines:
            if line.strip():
                # Highlight code blocks or special formatting
                if line.strip().startswith('```'):
                    print(f"{SunriseColors.ACCENT}{line}{SunriseColors.RESET}")
                elif line.strip().startswith('#'):
                    print(f"{SunriseColors.INFO}{line}{SunriseColors.RESET}")
                elif line.strip().startswith('*') or line.strip().startswith('-'):
                    print(f"{SunriseColors.WARNING}{line}{SunriseColors.RESET}")
                else:
                    print(line)
            else:
                print()  # Empty line
    
    def _process_natural_language(self, user_input: str):
        """Process natural language input (for quick actions)"""
        try:
            print(f"{SunriseColors.INFO}🔍 Processing quick action...{SunriseColors.RESET}")

            # For quick actions, always try local processing first
            if self.nl_processor:
                nl_result = self.nl_processor.process_request(user_input, self.ollama_interface.nl_patterns)

                if nl_result.get('success'):
                    print(f"\n{SunriseColors.SUCCESS}🤖 Cybex (System Analysis):{SunriseColors.RESET}")
                    print(nl_result['message'])

                    # Show recommendations
                    recommendations = nl_result.get('recommendations', [])
                    if recommendations:
                        print(f"\n{SunriseColors.WARNING}💡 Recommendations:{SunriseColors.RESET}")
                        for i, rec in enumerate(recommendations, 1):
                            print(f"  {i}. {rec}")

                    # Wait for user to continue
                    input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")
                    return

            # If local processing fails, fall back to AI
            print(f"{SunriseColors.WARNING}⚠️  Local processing unavailable, using AI...{SunriseColors.RESET}")
            self._chat_with_ai(user_input)

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Processing error: {e}{SunriseColors.RESET}")
            input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")

    def _show_configuration(self):
        """Show configuration menu"""
        while True:
            os.system('cls' if os.name == 'nt' else 'clear')

            # Get current model
            current_model = "gemma2:7b"  # Default
            if self.core and self.core.config_manager:
                try:
                    current_model = self.core.config_manager.get('ollama.model', 'gemma2:7b')
                except:
                    pass

            # Calculate max models to show (limit to prevent overflow)
            max_models_display = min(len(self.available_models), 15)

            config_menu = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                            {SunriseColors.ACCENT}⚙️  CONFIGURATION{SunriseColors.SUCCESS}                                    ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🤖 AI MODEL SELECTION ({len(self.available_models)} available):{SunriseColors.SUCCESS}                                        ║
║   {SunriseColors.INFO}Current Model: {SunriseColors.WARNING}{current_model}{SunriseColors.SUCCESS}                                                    ║
║                                                                               ║"""

            # Show models in two columns if many models
            if len(self.available_models) > 8:
                # Two column layout
                for i in range(0, max_models_display, 2):
                    left_model = self.available_models[i] if i < len(self.available_models) else ""
                    right_model = self.available_models[i+1] if i+1 < len(self.available_models) else ""

                    left_line = ""
                    right_line = ""

                    if left_model:
                        status = "●" if left_model == current_model else "○"
                        color = SunriseColors.SUCCESS if left_model == current_model else SunriseColors.GRAY
                        left_line = f"{color}[{i+1:2}] {status} {left_model:<18}{SunriseColors.SUCCESS}"

                    if right_model:
                        status = "●" if right_model == current_model else "○"
                        color = SunriseColors.SUCCESS if right_model == current_model else SunriseColors.GRAY
                        right_line = f"{color}[{i+2:2}] {status} {right_model:<18}{SunriseColors.SUCCESS}"

                    config_menu += f"\n║   {left_line:<35} {right_line:<35} ║"
            else:
                # Single column layout
                for i, model in enumerate(self.available_models[:max_models_display], 1):
                    status = "●" if model == current_model else "○"
                    color = SunriseColors.SUCCESS if model == current_model else SunriseColors.GRAY
                    config_menu += f"\n║   {color}[{i:2}] {status} {model:<65}{SunriseColors.SUCCESS} ║"

            if len(self.available_models) > max_models_display:
                config_menu += f"\n║   {SunriseColors.GRAY}... and {len(self.available_models) - max_models_display} more models{SunriseColors.SUCCESS}                                                ║"

            config_menu += f"""
║                                                                               ║
║ {SunriseColors.ACCENT}🔄 MODEL MANAGEMENT:{SunriseColors.SUCCESS}                                                        ║
║   {SunriseColors.INFO}[R] Refresh Model List                                                     {SunriseColors.SUCCESS}║
║   {SunriseColors.INFO}[A] Show All Models                                                        {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ACCENT}🎨 DISPLAY SETTINGS:{SunriseColors.SUCCESS}                                                        ║
║   {SunriseColors.INFO}[C] Toggle Colors: {SunriseColors.WARNING}{'Enabled' if self.show_colors else 'Disabled'}{SunriseColors.SUCCESS}                                          ║
║                                                                               ║
║ {SunriseColors.ACCENT}📊 SYSTEM SETTINGS:{SunriseColors.SUCCESS}                                                         ║
║   {SunriseColors.INFO}[S] System Information                                                     {SunriseColors.SUCCESS}║
║   {SunriseColors.INFO}[L] View Logs                                                              {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ERROR}[B] Back to Main Menu{SunriseColors.SUCCESS}                                                        ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
            print(config_menu)

            choice = input(f"\n{SunriseColors.ACCENT}Select option: {SunriseColors.RESET}").strip().lower()

            if choice == 'b':
                break
            elif choice == 'c':
                self.show_colors = not self.show_colors
                print(f"{SunriseColors.INFO}Colors {'enabled' if self.show_colors else 'disabled'}{SunriseColors.RESET}")
                time.sleep(1)
            elif choice == 'r':
                self._refresh_models()
            elif choice == 'a':
                self._show_all_models()
            elif choice == 's':
                self._show_system_info()
            elif choice == 'l':
                self._show_logs()
            elif choice.isdigit():
                model_index = int(choice) - 1
                if 0 <= model_index < len(self.available_models):
                    new_model = self.available_models[model_index]
                    self._change_model(new_model)
                else:
                    print(f"{SunriseColors.ERROR}Invalid selection (1-{len(self.available_models)}){SunriseColors.RESET}")
                    time.sleep(1)
            else:
                print(f"{SunriseColors.ERROR}Invalid option{SunriseColors.RESET}")
                time.sleep(1)

    def _change_model(self, model: str):
        """Change AI model"""
        try:
            print(f"{SunriseColors.INFO}🔄 Changing model to {model}...{SunriseColors.RESET}")

            # Update configuration
            if self.core and self.core.config_manager:
                self.core.config_manager.set('ollama.model', model)

                # Update Ollama interface model and timeout
                if self.ollama_interface:
                    self.ollama_interface.update_model(model)

                # Calculate and show new timeout
                timeout = self._get_model_timeout(model)
                minutes = timeout // 60

                print(f"{SunriseColors.SUCCESS}✅ Model changed to {model}{SunriseColors.RESET}")
                print(f"{SunriseColors.INFO}⏱️  Timeout set to {timeout}s ({minutes} minutes){SunriseColors.RESET}")

                if timeout > 300:
                    print(f"{SunriseColors.WARNING}💡 Large model - responses may take up to {minutes} minutes{SunriseColors.RESET}")
            else:
                print(f"{SunriseColors.ERROR}❌ Failed to change model{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error changing model: {e}{SunriseColors.RESET}")

        time.sleep(3)

    def _refresh_models(self):
        """Refresh the list of available models"""
        print(f"{SunriseColors.INFO}🔄 Refreshing model list...{SunriseColors.RESET}")
        self._load_available_models()
        print(f"{SunriseColors.SUCCESS}✅ Model list refreshed ({len(self.available_models)} models found){SunriseColors.RESET}")
        time.sleep(2)

    def _show_all_models(self):
        """Show all available models in detail"""
        try:
            import requests

            print(f"{SunriseColors.INFO}📋 Fetching detailed model information...{SunriseColors.RESET}")

            response = requests.get("http://localhost:11434/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])

                if not models:
                    print(f"{SunriseColors.WARNING}⚠️  No models found{SunriseColors.RESET}")
                    input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")
                    return

                # Display detailed model information
                model_info = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                          {SunriseColors.ACCENT}📋 ALL AVAILABLE MODELS{SunriseColors.SUCCESS}                                  ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║"""

                for i, model in enumerate(models, 1):
                    name = model.get('name', 'Unknown')
                    size = model.get('size', 0)
                    modified = model.get('modified_at', 'Unknown')

                    # Convert size to human readable
                    if size > 1024**3:  # GB
                        size_str = f"{size / (1024**3):.1f} GB"
                    elif size > 1024**2:  # MB
                        size_str = f"{size / (1024**2):.1f} MB"
                    else:
                        size_str = f"{size} bytes"

                    # Format modified date
                    try:
                        from datetime import datetime
                        if modified != 'Unknown':
                            dt = datetime.fromisoformat(modified.replace('Z', '+00:00'))
                            modified_str = dt.strftime('%Y-%m-%d %H:%M')
                        else:
                            modified_str = 'Unknown'
                    except:
                        modified_str = 'Unknown'

                    model_info += f"""
║ {SunriseColors.ACCENT}[{i:2}] {name:<30}{SunriseColors.SUCCESS}                                           ║
║     {SunriseColors.INFO}Size: {size_str:<15} Modified: {modified_str:<20}{SunriseColors.SUCCESS}                    ║"""

                model_info += f"""
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
                print(model_info)

            else:
                print(f"{SunriseColors.ERROR}❌ Could not fetch model details{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error fetching model details: {e}{SunriseColors.RESET}")

        input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")

    def _switch_mode(self):
        """Switch between chat and agent mode"""
        new_mode = "agent" if self.current_mode == "chat" else "chat"
        self._set_mode(new_mode)

    def _set_mode(self, mode: str):
        """Set operating mode"""
        try:
            self.current_mode = mode
            if self.core:
                self.core.switch_mode(mode)

            mode_icon = "💬" if mode == "chat" else "🤖"
            print(f"{SunriseColors.SUCCESS}✅ {mode_icon} Switched to {mode.upper()} mode{SunriseColors.RESET}")
            time.sleep(1)

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error switching mode: {e}{SunriseColors.RESET}")
            time.sleep(1)

    def _show_system_status(self):
        """Show detailed system status"""
        try:
            print(f"\n{SunriseColors.INFO}📊 Gathering system information...{SunriseColors.RESET}")

            if self.core:
                status = self.core.get_system_status()

                status_display = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                            {SunriseColors.ACCENT}📊 SYSTEM STATUS{SunriseColors.SUCCESS}                                     ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🖥️  SYSTEM INFORMATION:{SunriseColors.SUCCESS}                                                      ║
║   Platform: {SunriseColors.INFO}{status.get('platform', 'Unknown'):<20}{SunriseColors.SUCCESS}                                           ║
║   Python: {SunriseColors.INFO}{status.get('python_version', 'Unknown'):<22}{SunriseColors.SUCCESS}                                         ║
║   Mode: {SunriseColors.WARNING}{status.get('mode', 'Unknown').upper():<25}{SunriseColors.SUCCESS}                                             ║
║                                                                               ║"""

                # Add monitoring info if available
                if 'monitoring' in status:
                    mon = status['monitoring']
                    current = mon.get('current_state', {})
                    cpu = current.get('cpu', {}).get('percent', 0)
                    memory = current.get('memory', {}).get('percent', 0)
                    disk = current.get('disk', {}).get('percent', 0)

                    status_display += f"""
║ {SunriseColors.ACCENT}📈 PERFORMANCE METRICS:{SunriseColors.SUCCESS}                                                    ║
║   CPU Usage: {SunriseColors.WARNING}{cpu:.1f}%{SunriseColors.SUCCESS}                                                           ║
║   Memory Usage: {SunriseColors.WARNING}{memory:.1f}%{SunriseColors.SUCCESS}                                                      ║
║   Disk Usage: {SunriseColors.WARNING}{disk:.1f}%{SunriseColors.SUCCESS}                                                        ║
║   Active Alerts: {SunriseColors.ERROR}{mon.get('alerts', 0)}{SunriseColors.SUCCESS}                                                         ║
║                                                                               ║"""

                status_display += f"""
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
                print(status_display)
            else:
                print(f"{SunriseColors.ERROR}❌ Core not available{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error getting system status: {e}{SunriseColors.RESET}")

        input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")

    def _show_system_info(self):
        """Show detailed system information"""
        try:
            info = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                          {SunriseColors.ACCENT}🖥️  SYSTEM INFORMATION{SunriseColors.SUCCESS}                                  ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}Operating System:{SunriseColors.SUCCESS}                                                           ║
║   {SunriseColors.INFO}{platform.system()} {platform.release()}{SunriseColors.SUCCESS}                                                      ║
║   {SunriseColors.INFO}{platform.version()}{SunriseColors.SUCCESS}                                                               ║
║                                                                               ║
║ {SunriseColors.ACCENT}Hardware:{SunriseColors.SUCCESS}                                                                   ║
║   {SunriseColors.INFO}Architecture: {platform.architecture()[0]}{SunriseColors.SUCCESS}                                          ║
║   {SunriseColors.INFO}Processor: {platform.processor()[:50]}{SunriseColors.SUCCESS}                                             ║
║                                                                               ║
║ {SunriseColors.ACCENT}Python Environment:{SunriseColors.SUCCESS}                                                         ║
║   {SunriseColors.INFO}Version: {platform.python_version()}{SunriseColors.SUCCESS}                                                   ║
║   {SunriseColors.INFO}Implementation: {platform.python_implementation()}{SunriseColors.SUCCESS}                                     ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
            print(info)
        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error getting system info: {e}{SunriseColors.RESET}")

        input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")

    def _show_logs(self):
        """Show recent logs"""
        print(f"{SunriseColors.INFO}📋 Recent logs feature coming soon...{SunriseColors.RESET}")
        time.sleep(2)

    def _execute_system_command(self, command: str):
        """Execute system command"""
        try:
            print(f"{SunriseColors.INFO}⚡ Executing: {command}{SunriseColors.RESET}")

            if self.command_executor:
                result = self.command_executor.execute_command(command)
                if result.success:
                    print(f"{SunriseColors.SUCCESS}✅ Command executed successfully{SunriseColors.RESET}")
                    if result.output:
                        print(result.output)
                else:
                    print(f"{SunriseColors.ERROR}❌ Command failed: {result.error}{SunriseColors.RESET}")
            else:
                print(f"{SunriseColors.ERROR}❌ Command executor not available{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Execution error: {e}{SunriseColors.RESET}")

        input(f"\n{SunriseColors.GRAY}Press Enter to continue...{SunriseColors.RESET}")

    def _exit_chat_mode(self):
        """Exit continuous chat mode"""
        self.in_chat_mode = False
        print(f"\n{SunriseColors.INFO}📋 Exited chat mode. Returning to main menu.{SunriseColors.RESET}")

    def _show_chat_help(self):
        """Show chat mode help"""
        help_text = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                            {SunriseColors.ACCENT}💬 CHAT MODE HELP{SunriseColors.SUCCESS}                                     ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}💬 CHAT COMMANDS:{SunriseColors.SUCCESS}                                                               ║
║   {SunriseColors.GRAY}/exit, /quit, /back, /menu - Return to main menu                           {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}/save                      - Save current chat session                     {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}/clear                     - Clear chat history                            {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}/help                      - Show this help                                {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}/ops, /operations          - Toggle operations monitoring panel            {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}/tools                     - Show available agent tools                    {SunriseColors.SUCCESS}║
║   {SunriseColors.GRAY}/tool <name> [params]      - Execute agent tool directly                   {SunriseColors.SUCCESS}║
║                                                                               ║
║ {SunriseColors.ACCENT}💡 TIPS:{SunriseColors.SUCCESS}                                                                       ║
║   • Type any message to continue chatting                                    ║
║   • Chat history is automatically saved when you exit                        ║
║   • Use /save to manually save the current session                           ║
║   • Your chat will be remembered for future sessions                         ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
        print(help_text)

    def _save_current_chat(self):
        """Save current chat session"""
        if not self.chat_history:
            print(f"{SunriseColors.WARNING}⚠️  No chat history to save{SunriseColors.RESET}")
            return

        try:
            # Create chats directory if it doesn't exist
            chat_dir = Path("cybex/data/chats")
            chat_dir.mkdir(parents=True, exist_ok=True)

            # Generate filename with timestamp
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chat_{timestamp}.json"
            filepath = chat_dir / filename

            # Prepare chat data
            chat_data = {
                'timestamp': timestamp,
                'session_start': datetime.fromtimestamp(self.chat_history[0]['timestamp']).isoformat(),
                'session_end': datetime.fromtimestamp(self.chat_history[-1]['timestamp']).isoformat(),
                'total_messages': len(self.chat_history),
                'models_used': list(set(msg['model'] for msg in self.chat_history)),
                'messages': self.chat_history
            }

            # Save to file
            import json
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(chat_data, f, indent=2, ensure_ascii=False)

            print(f"{SunriseColors.SUCCESS}✅ Chat saved: {filename}{SunriseColors.RESET}")
            print(f"{SunriseColors.INFO}📁 Location: {filepath}{SunriseColors.RESET}")
            print(f"{SunriseColors.GRAY}Messages: {len(self.chat_history)}, Models: {', '.join(chat_data['models_used'])}{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Failed to save chat: {e}{SunriseColors.RESET}")

    def _clear_chat_history(self):
        """Clear current chat history"""
        if self.chat_history:
            count = len(self.chat_history)
            self.chat_history.clear()
            print(f"{SunriseColors.INFO}🗑️  Cleared {count} messages from chat history{SunriseColors.RESET}")
        else:
            print(f"{SunriseColors.WARNING}⚠️  Chat history is already empty{SunriseColors.RESET}")

    def _ask_save_chat_on_exit(self):
        """Ask user if they want to save chat before exiting"""
        try:
            print(f"\n{SunriseColors.ACCENT}💬 You have {len(self.chat_history)} messages in your chat history.{SunriseColors.RESET}")

            save_choice = input(f"{SunriseColors.INFO}💾 Save chat session? (Y/n): {SunriseColors.RESET}").strip().lower()

            if save_choice in ['', 'y', 'yes', 'si', 's']:
                self._save_current_chat()

                # Ask for session name/description
                description = input(f"{SunriseColors.ACCENT}📝 Add description (optional): {SunriseColors.RESET}").strip()
                if description:
                    # Update the last saved file with description
                    self._add_description_to_last_chat(description)
            else:
                print(f"{SunriseColors.GRAY}Chat session not saved{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Error during save prompt: {e}{SunriseColors.RESET}")

    def _add_description_to_last_chat(self, description: str):
        """Add description to the last saved chat"""
        try:
            chat_dir = Path("cybex/data/chats")
            if not chat_dir.exists():
                return

            # Find the most recent chat file
            chat_files = list(chat_dir.glob("chat_*.json"))
            if not chat_files:
                return

            latest_file = max(chat_files, key=lambda f: f.stat().st_mtime)

            # Update with description
            import json
            with open(latest_file, 'r', encoding='utf-8') as f:
                chat_data = json.load(f)

            chat_data['description'] = description

            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(chat_data, f, indent=2, ensure_ascii=False)

            print(f"{SunriseColors.SUCCESS}✅ Description added to chat session{SunriseColors.RESET}")

        except Exception as e:
            print(f"{SunriseColors.WARNING}⚠️  Could not add description: {e}{SunriseColors.RESET}")

    def _toggle_operations_panel(self):
        """Toggle operations monitoring panel"""
        self.show_operations_panel = not self.show_operations_panel

        if self.show_operations_panel:
            print(f"\n{SunriseColors.SUCCESS}📊 Operations panel enabled{SunriseColors.RESET}")
            self._show_operations_status()
        else:
            print(f"\n{SunriseColors.INFO}📊 Operations panel disabled{SunriseColors.RESET}")

    def _show_operations_status(self):
        """Show current operations status"""
        if not self.operation_monitor:
            print(f"{SunriseColors.WARNING}⚠️  Operation monitor not available{SunriseColors.RESET}")
            return

        summary = self.operation_monitor.get_display_summary()

        print(f"\n{SunriseColors.ACCENT}╔══════════════════════════════════════════════════════════════════════════════╗{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}║                            {SunriseColors.INFO}🔧 OPERATIONS MONITOR{SunriseColors.ACCENT}                               ║{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}╠══════════════════════════════════════════════════════════════════════════════╣{SunriseColors.RESET}")

        # Active operations
        if summary['active_operations']:
            print(f"{SunriseColors.ACCENT}║ {SunriseColors.WARNING}⚡ ACTIVE OPERATIONS:{SunriseColors.ACCENT}                                                        ║{SunriseColors.RESET}")
            for op in summary['active_operations']:
                print(f"{SunriseColors.ACCENT}║   {op:<74} ║{SunriseColors.RESET}")
        else:
            print(f"{SunriseColors.ACCENT}║ {SunriseColors.GRAY}💤 No active operations{SunriseColors.ACCENT}                                                      ║{SunriseColors.RESET}")

        print(f"{SunriseColors.ACCENT}║                                                                              ║{SunriseColors.RESET}")

        # Recent operations
        if summary['recent_operations']:
            print(f"{SunriseColors.ACCENT}║ {SunriseColors.INFO}📋 RECENT OPERATIONS:{SunriseColors.ACCENT}                                                        ║{SunriseColors.RESET}")
            for op in summary['recent_operations'][-3:]:  # Last 3
                print(f"{SunriseColors.ACCENT}║   {op:<74} ║{SunriseColors.RESET}")

        # Stats
        stats = summary['stats']
        print(f"{SunriseColors.ACCENT}║                                                                              ║{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}║ {SunriseColors.SUCCESS}📊 STATS:{SunriseColors.ACCENT} Total: {stats['total_operations']:<3} | Success: {stats['successful_operations']:<3} | Failed: {stats['failed_operations']:<3} | Avg: {stats['average_duration']:.1f}s ║{SunriseColors.RESET}")
        print(f"{SunriseColors.ACCENT}╚══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}")

    def _show_available_tools(self):
        """Show available agent tools"""
        if not self.agent_tools:
            print(f"{SunriseColors.WARNING}⚠️  Agent tools not available{SunriseColors.RESET}")
            return

        tools = self.agent_tools.get_available_tools()

        print(f"\n{SunriseColors.SUCCESS}╔══════════════════════════════════════════════════════════════════════════════╗{SunriseColors.RESET}")
        print(f"{SunriseColors.SUCCESS}║                              {SunriseColors.ACCENT}🛠️  AGENT TOOLS{SunriseColors.SUCCESS}                                   ║{SunriseColors.RESET}")
        print(f"{SunriseColors.SUCCESS}╠══════════════════════════════════════════════════════════════════════════════╣{SunriseColors.RESET}")

        for tool_name, tool_info in tools.items():
            print(f"{SunriseColors.SUCCESS}║ {SunriseColors.ACCENT}{tool_name:<20}{SunriseColors.SUCCESS} - {tool_info['description']:<50} ║{SunriseColors.RESET}")

            # Show parameters
            if tool_info.get('parameters'):
                for param_name, param_info in tool_info['parameters'].items():
                    param_desc = param_info.get('description', 'No description')
                    param_type = param_info.get('type', 'unknown')
                    print(f"{SunriseColors.SUCCESS}║   {SunriseColors.GRAY}• {param_name} ({param_type}): {param_desc:<45} {SunriseColors.SUCCESS}║{SunriseColors.RESET}")
            print(f"{SunriseColors.SUCCESS}║{' ' * 78}║{SunriseColors.RESET}")

        print(f"{SunriseColors.SUCCESS}║                                                                              ║{SunriseColors.RESET}")
        print(f"{SunriseColors.SUCCESS}║ {SunriseColors.INFO}💡 Usage: /tool <tool_name> [parameters]{SunriseColors.SUCCESS}                                      ║{SunriseColors.RESET}")
        print(f"{SunriseColors.SUCCESS}║ {SunriseColors.INFO}Example: /tool execute_command command='dir' shell='cmd'{SunriseColors.SUCCESS}                      ║{SunriseColors.RESET}")
        print(f"{SunriseColors.SUCCESS}╚══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}")

    def _execute_tool_command(self, tool_command: str):
        """Execute tool command"""
        if not self.agent_tools:
            print(f"{SunriseColors.WARNING}⚠️  Agent tools not available{SunriseColors.RESET}")
            return

        try:
            # Parse tool command
            parts = tool_command.strip().split(' ', 1)
            tool_name = parts[0]

            parameters = {}
            if len(parts) > 1:
                # Simple parameter parsing (key=value format)
                param_str = parts[1]
                for param in param_str.split():
                    if '=' in param:
                        key, value = param.split('=', 1)
                        # Remove quotes if present
                        value = value.strip('\'"')
                        parameters[key] = value

            print(f"\n{SunriseColors.INFO}🔧 Executing tool: {tool_name}{SunriseColors.RESET}")
            if parameters:
                print(f"{SunriseColors.GRAY}Parameters: {parameters}{SunriseColors.RESET}")

            # Execute tool
            execution_id = self.agent_tools.execute_tool(tool_name, parameters)

            print(f"{SunriseColors.SUCCESS}⚡ Tool execution started: {execution_id}{SunriseColors.RESET}")

            # Wait for completion and show result
            self._wait_for_tool_completion(execution_id)

        except Exception as e:
            print(f"{SunriseColors.ERROR}❌ Tool execution error: {e}{SunriseColors.RESET}")

    def _wait_for_tool_completion(self, execution_id: str):
        """Wait for tool completion and show result"""
        if not self.agent_tools:
            return

        print(f"{SunriseColors.INFO}⏳ Waiting for tool completion...{SunriseColors.RESET}")

        # Poll for completion
        max_wait = 30  # 30 seconds max
        wait_time = 0

        while wait_time < max_wait:
            execution = self.agent_tools.get_execution_status(execution_id)

            if not execution:
                print(f"{SunriseColors.ERROR}❌ Execution not found{SunriseColors.RESET}")
                return

            if execution.status.value in ['completed', 'failed', 'cancelled']:
                break

            time.sleep(0.5)
            wait_time += 0.5

            # Show progress
            if wait_time % 2 == 0:  # Every 2 seconds
                print(f"{SunriseColors.GRAY}⏳ Still running... ({wait_time:.0f}s){SunriseColors.RESET}")

        # Show result
        execution = self.agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result

            if result.success:
                print(f"\n{SunriseColors.SUCCESS}✅ Tool completed in {result.execution_time:.2f}s{SunriseColors.RESET}")
                if result.output:
                    print(f"\n{SunriseColors.ACCENT}📄 Output:{SunriseColors.RESET}")
                    print(result.output)
            else:
                print(f"\n{SunriseColors.ERROR}❌ Tool failed: {result.error}{SunriseColors.RESET}")
        else:
            print(f"{SunriseColors.WARNING}⚠️  Tool execution timed out{SunriseColors.RESET}")

    def _shutdown(self):
        """Shutdown Cybex Enterprise"""
        print(f"\n{SunriseColors.INFO}🔄 Shutting down Cybex Enterprise...{SunriseColors.RESET}")
        
        if self.core and self.core.session_active:
            self.core.end_session()
        
        print(f"{SunriseColors.SUCCESS}✅ Cybex Enterprise shutdown complete{SunriseColors.RESET}")
        print(f"{SunriseColors.GRAY}Thank you for using Cybex Enterprise by AGTECHdesigne{SunriseColors.RESET}")


if __name__ == "__main__":
    try:
        ui = CybexEnterpriseUI()
        ui.start()
    except KeyboardInterrupt:
        print("\nShutdown requested by user")
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)
