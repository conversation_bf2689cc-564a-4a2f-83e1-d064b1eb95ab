#!/usr/bin/env python3
"""
Web Browsing Agent for CYBEX Enterprise
Provides comprehensive web browsing, searching, and content analysis capabilities
"""

import requests
import json
import time
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
from urllib.parse import urljoin, urlparse, quote_plus
from bs4 import BeautifulSoup
import html2text
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


@dataclass
class WebSearchResult:
    """Web search result structure"""
    title: str
    url: str
    snippet: str
    domain: str
    relevance_score: float
    timestamp: float


@dataclass
class WebPageContent:
    """Web page content structure"""
    url: str
    title: str
    content: str
    markdown_content: str
    links: List[str]
    images: List[str]
    metadata: Dict[str, Any]
    load_time: float
    status_code: int


class WebBrowsingAgent:
    """Advanced web browsing agent with search and analysis capabilities"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Configuration
        self.config = {
            'user_agent': 'CYBEX-Enterprise-Agent/1.0 (AI Assistant)',
            'timeout': 30,
            'max_retries': 3,
            'enable_javascript': True,
            'enable_images': False,
            'max_page_size': 10 * 1024 * 1024,  # 10MB
            'search_engines': {
                'google': 'https://www.google.com/search?q={}',
                'bing': 'https://www.bing.com/search?q={}',
                'duckduckgo': 'https://duckduckgo.com/?q={}'
            }
        }
        
        # Session management
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': self.config['user_agent'],
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        })
        
        # Browser setup for JavaScript-heavy sites
        self.browser_driver = None
        self._setup_browser()
        
        # Content processing
        self.html_converter = html2text.HTML2Text()
        self.html_converter.ignore_links = False
        self.html_converter.ignore_images = True
        
        # Search history and cache
        self.search_history = []
        self.page_cache = {}
        self.max_cache_size = 100
        
        if self.logger:
            self.logger.info("Web browsing agent initialized")
    
    def _setup_browser(self):
        """Setup Selenium browser for JavaScript-heavy sites"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--headless')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            chrome_options.add_argument(f'--user-agent={self.config["user_agent"]}')
            
            if not self.config['enable_images']:
                chrome_options.add_argument('--blink-settings=imagesEnabled=false')
            
            # Try to initialize Chrome driver
            self.browser_driver = webdriver.Chrome(options=chrome_options)
            self.browser_driver.set_page_load_timeout(self.config['timeout'])
            
            if self.logger:
                self.logger.info("Browser driver initialized successfully")
                
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not initialize browser driver: {e}")
            self.browser_driver = None
    
    def search_web(self, query: str, engine: str = 'google', max_results: int = 10) -> List[WebSearchResult]:
        """Search the web using specified search engine"""
        try:
            if engine not in self.config['search_engines']:
                engine = 'google'
            
            search_url = self.config['search_engines'][engine].format(quote_plus(query))
            
            if self.logger:
                self.logger.info(f"Searching web: '{query}' using {engine}")
            
            # Use browser for better results
            if self.browser_driver and engine == 'google':
                return self._search_google_with_browser(query, max_results)
            else:
                return self._search_with_requests(search_url, query, max_results)
                
        except Exception as e:
            if self.logger:
                self.logger.error(f"Web search failed: {e}")
            return []
    
    def _search_google_with_browser(self, query: str, max_results: int) -> List[WebSearchResult]:
        """Search Google using browser for better results"""
        try:
            search_url = f"https://www.google.com/search?q={quote_plus(query)}&num={max_results}"
            self.browser_driver.get(search_url)
            
            # Wait for results to load
            WebDriverWait(self.browser_driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "div.g"))
            )
            
            results = []
            search_results = self.browser_driver.find_elements(By.CSS_SELECTOR, "div.g")
            
            for i, result in enumerate(search_results[:max_results]):
                try:
                    # Extract title and URL
                    title_element = result.find_element(By.CSS_SELECTOR, "h3")
                    title = title_element.text
                    
                    link_element = result.find_element(By.CSS_SELECTOR, "a")
                    url = link_element.get_attribute("href")
                    
                    # Extract snippet
                    snippet_elements = result.find_elements(By.CSS_SELECTOR, "span")
                    snippet = " ".join([elem.text for elem in snippet_elements if elem.text])[:200]
                    
                    if title and url and not url.startswith('javascript:'):
                        domain = urlparse(url).netloc
                        relevance_score = 1.0 - (i * 0.1)  # Simple relevance scoring
                        
                        results.append(WebSearchResult(
                            title=title,
                            url=url,
                            snippet=snippet,
                            domain=domain,
                            relevance_score=relevance_score,
                            timestamp=time.time()
                        ))
                        
                except Exception as e:
                    continue
            
            # Add to search history
            self.search_history.append({
                'query': query,
                'engine': 'google',
                'results_count': len(results),
                'timestamp': time.time()
            })
            
            return results
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Google search with browser failed: {e}")
            return []
    
    def _search_with_requests(self, search_url: str, query: str, max_results: int) -> List[WebSearchResult]:
        """Fallback search using requests"""
        try:
            response = self.session.get(search_url, timeout=self.config['timeout'])
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            results = []
            
            # This is a simplified parser - real implementation would need
            # specific parsers for each search engine
            links = soup.find_all('a', href=True)[:max_results]
            
            for i, link in enumerate(links):
                href = link.get('href', '')
                text = link.get_text(strip=True)
                
                if href.startswith('http') and text:
                    domain = urlparse(href).netloc
                    results.append(WebSearchResult(
                        title=text[:100],
                        url=href,
                        snippet=text[:200],
                        domain=domain,
                        relevance_score=1.0 - (i * 0.1),
                        timestamp=time.time()
                    ))
            
            return results
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Search with requests failed: {e}")
            return []
    
    def fetch_page_content(self, url: str, use_browser: bool = False) -> Optional[WebPageContent]:
        """Fetch and parse web page content"""
        try:
            # Check cache first
            if url in self.page_cache:
                cached_content = self.page_cache[url]
                if time.time() - cached_content.timestamp < 3600:  # 1 hour cache
                    return cached_content
            
            start_time = time.time()
            
            if use_browser and self.browser_driver:
                content = self._fetch_with_browser(url)
            else:
                content = self._fetch_with_requests(url)
            
            if content:
                load_time = time.time() - start_time
                content.load_time = load_time
                
                # Cache the content
                self._cache_content(url, content)
                
                if self.logger:
                    self.logger.info(f"Fetched page content: {url} ({load_time:.2f}s)")
                
                return content
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Failed to fetch page content from {url}: {e}")
        
        return None
    
    def _fetch_with_browser(self, url: str) -> Optional[WebPageContent]:
        """Fetch page content using browser (for JavaScript-heavy sites)"""
        try:
            self.browser_driver.get(url)
            
            # Wait for page to load
            WebDriverWait(self.browser_driver, 10).until(
                lambda driver: driver.execute_script("return document.readyState") == "complete"
            )
            
            # Get page content
            title = self.browser_driver.title
            html_content = self.browser_driver.page_source
            current_url = self.browser_driver.current_url
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Extract text content
            for script in soup(["script", "style"]):
                script.decompose()
            
            text_content = soup.get_text()
            markdown_content = self.html_converter.handle(html_content)
            
            # Extract links and images
            links = [urljoin(current_url, link.get('href', '')) 
                    for link in soup.find_all('a', href=True)]
            images = [urljoin(current_url, img.get('src', '')) 
                     for img in soup.find_all('img', src=True)]
            
            # Extract metadata
            metadata = self._extract_metadata(soup)
            
            return WebPageContent(
                url=current_url,
                title=title,
                content=text_content,
                markdown_content=markdown_content,
                links=links,
                images=images,
                metadata=metadata,
                load_time=0,  # Will be set by caller
                status_code=200
            )
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Browser fetch failed for {url}: {e}")
            return None
    
    def _fetch_with_requests(self, url: str) -> Optional[WebPageContent]:
        """Fetch page content using requests"""
        try:
            response = self.session.get(url, timeout=self.config['timeout'])
            response.raise_for_status()
            
            # Check content size
            if len(response.content) > self.config['max_page_size']:
                if self.logger:
                    self.logger.warning(f"Page too large: {url}")
                return None
            
            # Parse content
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract title
            title_tag = soup.find('title')
            title = title_tag.get_text(strip=True) if title_tag else urlparse(url).netloc
            
            # Extract text content
            for script in soup(["script", "style"]):
                script.decompose()
            
            text_content = soup.get_text()
            markdown_content = self.html_converter.handle(response.text)
            
            # Extract links and images
            links = [urljoin(url, link.get('href', '')) 
                    for link in soup.find_all('a', href=True)]
            images = [urljoin(url, img.get('src', '')) 
                     for img in soup.find_all('img', src=True)]
            
            # Extract metadata
            metadata = self._extract_metadata(soup)
            metadata['content_type'] = response.headers.get('content-type', '')
            metadata['content_length'] = len(response.content)
            
            return WebPageContent(
                url=response.url,
                title=title,
                content=text_content,
                markdown_content=markdown_content,
                links=links,
                images=images,
                metadata=metadata,
                load_time=0,  # Will be set by caller
                status_code=response.status_code
            )
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Requests fetch failed for {url}: {e}")
            return None
    
    def _extract_metadata(self, soup: BeautifulSoup) -> Dict[str, Any]:
        """Extract metadata from HTML"""
        metadata = {}
        
        # Meta tags
        for meta in soup.find_all('meta'):
            name = meta.get('name') or meta.get('property') or meta.get('http-equiv')
            content = meta.get('content')
            if name and content:
                metadata[name.lower()] = content
        
        # Open Graph tags
        og_tags = soup.find_all('meta', property=lambda x: x and x.startswith('og:'))
        for tag in og_tags:
            property_name = tag.get('property', '').replace('og:', '')
            content = tag.get('content')
            if property_name and content:
                metadata[f'og_{property_name}'] = content
        
        # Twitter Card tags
        twitter_tags = soup.find_all('meta', attrs={'name': lambda x: x and x.startswith('twitter:')})
        for tag in twitter_tags:
            name = tag.get('name', '').replace('twitter:', '')
            content = tag.get('content')
            if name and content:
                metadata[f'twitter_{name}'] = content
        
        return metadata
    
    def _cache_content(self, url: str, content: WebPageContent):
        """Cache page content"""
        if len(self.page_cache) >= self.max_cache_size:
            # Remove oldest entry
            oldest_url = min(self.page_cache.keys(), 
                           key=lambda k: self.page_cache[k].timestamp)
            del self.page_cache[oldest_url]
        
        content.timestamp = time.time()
        self.page_cache[url] = content
    
    def analyze_page_content(self, content: WebPageContent, analysis_type: str = 'general') -> Dict[str, Any]:
        """Analyze web page content"""
        try:
            analysis = {
                'url': content.url,
                'title': content.title,
                'word_count': len(content.content.split()),
                'character_count': len(content.content),
                'links_count': len(content.links),
                'images_count': len(content.images),
                'load_time': content.load_time,
                'analysis_type': analysis_type,
                'timestamp': time.time()
            }
            
            if analysis_type == 'seo':
                analysis.update(self._analyze_seo(content))
            elif analysis_type == 'content':
                analysis.update(self._analyze_content_quality(content))
            elif analysis_type == 'technical':
                analysis.update(self._analyze_technical_aspects(content))
            
            return analysis
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"Content analysis failed: {e}")
            return {'error': str(e)}
    
    def _analyze_seo(self, content: WebPageContent) -> Dict[str, Any]:
        """Analyze SEO aspects of the page"""
        seo_analysis = {}
        
        # Title analysis
        title_length = len(content.title)
        seo_analysis['title_length'] = title_length
        seo_analysis['title_seo_score'] = 100 if 30 <= title_length <= 60 else 50
        
        # Meta description
        meta_desc = content.metadata.get('description', '')
        seo_analysis['meta_description_length'] = len(meta_desc)
        seo_analysis['has_meta_description'] = bool(meta_desc)
        
        # Headings analysis
        soup = BeautifulSoup(content.content, 'html.parser')
        headings = {}
        for i in range(1, 7):
            headings[f'h{i}_count'] = len(soup.find_all(f'h{i}'))
        seo_analysis['headings'] = headings
        
        return seo_analysis
    
    def _analyze_content_quality(self, content: WebPageContent) -> Dict[str, Any]:
        """Analyze content quality"""
        quality_analysis = {}
        
        # Reading time estimation (average 200 words per minute)
        word_count = len(content.content.split())
        quality_analysis['estimated_reading_time'] = max(1, word_count // 200)
        
        # Content density
        quality_analysis['content_to_html_ratio'] = len(content.content) / max(len(content.markdown_content), 1)
        
        # Language detection (simplified)
        quality_analysis['primary_language'] = content.metadata.get('language', 'unknown')
        
        return quality_analysis
    
    def _analyze_technical_aspects(self, content: WebPageContent) -> Dict[str, Any]:
        """Analyze technical aspects"""
        technical_analysis = {}
        
        # Performance metrics
        technical_analysis['load_time'] = content.load_time
        technical_analysis['status_code'] = content.status_code
        technical_analysis['content_size'] = len(content.content)
        
        # Security headers (from metadata)
        security_headers = ['x-frame-options', 'x-content-type-options', 'x-xss-protection']
        technical_analysis['security_headers'] = {
            header: content.metadata.get(header, 'missing') for header in security_headers
        }
        
        return technical_analysis
    
    def get_browsing_history(self) -> List[Dict[str, Any]]:
        """Get browsing and search history"""
        return {
            'search_history': self.search_history[-50:],  # Last 50 searches
            'cached_pages': list(self.page_cache.keys()),
            'total_searches': len(self.search_history),
            'cache_size': len(self.page_cache)
        }
    
    def clear_cache(self):
        """Clear page cache and history"""
        self.page_cache.clear()
        self.search_history.clear()
        if self.logger:
            self.logger.info("Web browsing cache and history cleared")
    
    def close(self):
        """Close browser and cleanup resources"""
        if self.browser_driver:
            try:
                self.browser_driver.quit()
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"Error closing browser: {e}")
        
        self.session.close()
        
        if self.logger:
            self.logger.info("Web browsing agent closed")


def create_web_browsing_agent(log_manager=None) -> WebBrowsingAgent:
    """Factory function to create web browsing agent"""
    return WebBrowsingAgent(log_manager)


# Example usage
if __name__ == "__main__":
    agent = create_web_browsing_agent()
    
    # Test web search
    results = agent.search_web("artificial intelligence trends 2024", max_results=5)
    print(f"Found {len(results)} search results")
    
    if results:
        # Test page content fetching
        first_result = results[0]
        content = agent.fetch_page_content(first_result.url)
        
        if content:
            print(f"Fetched content from: {content.title}")
            
            # Test content analysis
            analysis = agent.analyze_page_content(content, 'general')
            print(f"Content analysis: {analysis}")
    
    agent.close()
