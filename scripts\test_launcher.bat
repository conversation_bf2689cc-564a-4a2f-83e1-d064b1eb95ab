@echo off
REM ============================================================================
REM Test Launcher Files
REM Test dei file batch di avvio di Cybex Enterprise
REM ============================================================================

title Test Cybex Launcher Files

color 0E

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║                    🧪 CYBEX LAUNCHER FILES TEST                             ║
echo ║                           by AGTECHdesigne                                  ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Testing Cybex launcher files...
echo.

REM Test 1: Check if files exist
echo [1/5] Checking launcher files...
set files_ok=1

if not exist "start_cybex.bat" (
    echo ❌ start_cybex.bat not found
    set files_ok=0
) else (
    echo ✅ start_cybex.bat found
)

if not exist "cybex_launcher.bat" (
    echo ❌ cybex_launcher.bat not found
    set files_ok=0
) else (
    echo ✅ cybex_launcher.bat found
)

if not exist "install_cybex.bat" (
    echo ❌ install_cybex.bat not found
    set files_ok=0
) else (
    echo ✅ install_cybex.bat found
)

if not exist "main_enterprise.py" (
    echo ❌ main_enterprise.py not found
    set files_ok=0
) else (
    echo ✅ main_enterprise.py found
)

echo.

REM Test 2: Check Python
echo [2/5] Testing Python availability...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not available
    set files_ok=0
) else (
    echo ✅ Python is available:
    python --version
)
echo.

REM Test 3: Check Cybex components
echo [3/5] Testing Cybex components...
if not exist "cybex" (
    echo ❌ cybex directory not found
    set files_ok=0
) else (
    echo ✅ cybex directory found
)

if not exist "cybex\interfaces\ui_cli_enterprise.py" (
    echo ❌ Enterprise UI not found
    set files_ok=0
) else (
    echo ✅ Enterprise UI found
)
echo.

REM Test 4: Test Ollama connection
echo [4/5] Testing Ollama connection...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not running (AI features will be limited)
) else (
    echo ✅ Ollama server is running
    
    REM Count models
    for /f %%i in ('curl -s http://localhost:11434/api/tags ^| python -c "import sys,json; data=json.load(sys.stdin); print(len(data.get('models', [])))" 2^>nul') do set model_count=%%i
    if defined model_count (
        echo ✅ Found !model_count! Ollama models
    )
)
echo.

REM Test 5: Test basic functionality
echo [5/5] Testing basic Cybex functionality...
python -c "from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI, SunriseColors; print('✅ Enterprise UI can be imported')" 2>nul
if errorlevel 1 (
    echo ❌ Cannot import Enterprise UI
    set files_ok=0
) else (
    echo ✅ Enterprise UI imports successfully
)
echo.

REM Results
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                RESULTS                                       ║
echo ╠══════════════════════════════════════════════════════════════════════════════╣

if "%files_ok%"=="1" (
    echo ║  🎉 ALL TESTS PASSED - Cybex Enterprise is ready to use!                   ║
    echo ║                                                                              ║
    echo ║  🚀 Available launcher options:                                             ║
    echo ║     • start_cybex.bat        - Quick start                                  ║
    echo ║     • cybex_launcher.bat     - Advanced menu                               ║
    echo ║     • install_cybex.bat      - First-time setup                            ║
    echo ║                                                                              ║
    echo ║  💬 Try natural language commands like:                                     ║
    echo ║     "dammi la situazione memoria del computer"                              ║
    echo ║                                                                              ║
) else (
    echo ║  ❌ SOME TESTS FAILED - Please check the errors above                       ║
    echo ║                                                                              ║
    echo ║  🔧 Troubleshooting:                                                        ║
    echo ║     • Run install_cybex.bat for first-time setup                           ║
    echo ║     • Ensure Python 3.8+ is installed                                      ║
    echo ║     • Check that all Cybex files are present                               ║
    echo ║                                                                              ║
)

echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

if "%files_ok%"=="1" (
    set /p launch="Launch Cybex Enterprise now? (Y/N): "
    if /i "!launch!"=="Y" (
        echo.
        echo 🚀 Starting Cybex Enterprise...
        call start_cybex.bat
    )
) else (
    echo Run install_cybex.bat to fix any issues
)

echo.
pause
