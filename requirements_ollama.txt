# Ollama Integration Requirements for CYBEX Enterprise
# Install with: pip install -r requirements_ollama.txt

# Core HTTP client for Ollama API
requests>=2.31.0

# JSON processing (usually included with Python)
# json - built-in module

# Optional: Enhanced HTTP client
httpx>=0.25.0

# Optional: Async support
aiohttp>=3.8.0

# Note: Ollama itself must be installed separately
# Download from: https://ollama.ai/
# Or install with: curl https://ollama.ai/install.sh | sh
