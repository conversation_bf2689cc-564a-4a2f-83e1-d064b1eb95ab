#!/usr/bin/env python3
"""
Reset all model timeouts to 320 seconds
"""

import sys
import os
sys.path.insert(0, '.')

try:
    print("🔄 RESETTING MODEL TIMEOUTS TO 320 SECONDS")
    print("=" * 50)
    
    # Import required modules
    from cybex.modules.model_performance_analyzer import ModelPerformanceAnalyzer
    from cybex.core.cybex_core import LogManager, SystemConfig
    
    print("✅ Modules imported successfully")
    
    # Initialize components
    config = SystemConfig()
    log_manager = LogManager(config)
    analyzer = ModelPerformanceAnalyzer(log_manager)
    
    print(f"✅ Performance analyzer initialized with {len(analyzer.models)} models")
    
    # Show current timeouts
    print("\n📊 CURRENT TIMEOUTS:")
    for model_name, model_perf in analyzer.models.items():
        print(f"   • {model_name}: {model_perf.timeout_seconds}s")
    
    # Reset all timeouts to base (320s)
    print(f"\n🔄 Resetting all timeouts to {analyzer.base_timeout}s...")
    reset_count = analyzer.reset_all_timeouts_to_base()
    
    print(f"✅ Reset {reset_count} model timeouts")
    
    # Show new timeouts
    print("\n📊 NEW TIMEOUTS:")
    for model_name, model_perf in analyzer.models.items():
        print(f"   • {model_name}: {model_perf.timeout_seconds}s")
    
    print(f"\n🎉 All model timeouts reset to {analyzer.base_timeout} seconds!")
    print("💡 Timeouts will adapt dynamically based on actual performance during use")
    
except Exception as e:
    print(f"❌ Reset failed: {e}")
    import traceback
    traceback.print_exc()
