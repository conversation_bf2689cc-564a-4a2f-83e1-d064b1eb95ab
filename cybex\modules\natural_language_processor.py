"""
Natural Language Processor Module
Processes natural language requests and executes appropriate system actions
"""

import re
import time
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from pathlib import Path
from .enhanced_ai_integration import EnhancedAIIntegration


class NaturalLanguageProcessor:
    """
    Processes natural language requests and maps them to system actions
    """
    
    def __init__(self, core, system_monitor=None, disk_manager=None):
        """Initialize natural language processor"""
        self.core = core
        self.system_monitor = system_monitor
        self.disk_manager = disk_manager
        self.logger = core.log_manager.get_logger(__name__)

        # Initialize enhanced AI integration
        try:
            self.ai_integration = EnhancedAIIntegration()
            self.logger.info("Enhanced AI integration initialized in NLP")
        except Exception as e:
            self.logger.warning(f"Could not initialize enhanced AI integration: {e}")
            self.ai_integration = None

        # Initialize enhanced context
        self.context = {
            'last_command': None,
            'last_result': None,
            'session_start': datetime.now(),
            'command_history': [],
            'user_preferences': {},
            'security_level': 'MEDIUM',
            'enterprise_mode': True
        }

        # Action handlers mapping
        self.action_handlers = {
            'get_memory_status': self._get_memory_status,
            'get_disk_status': self._get_disk_status,
            'get_cpu_status': self._get_cpu_status,
            'analyze_performance': self._analyze_performance,
            'cleanup_system': self._cleanup_system,
            'get_system_overview': self._get_system_overview,
            'get_processes': self._get_processes,
            'get_network_status': self._get_network_status
        }
    
    def process_request(self, user_input: str, nl_patterns: Dict) -> Dict[str, Any]:
        """
        Process natural language request and return structured response
        """
        try:
            # Detect intent from user input
            detected_action = self._detect_intent(user_input.lower(), nl_patterns)
            
            if not detected_action:
                return {
                    'success': False,
                    'message': 'Non ho capito la richiesta. Puoi essere più specifico?',
                    'suggestions': [
                        'Prova: "dammi la situazione memoria"',
                        'Oppure: "come sta il disco?"',
                        'O ancora: "stato del sistema"'
                    ]
                }
            
            # Execute the detected action
            handler = self.action_handlers.get(detected_action)
            if handler:
                result = handler()
                result['action'] = detected_action
                result['timestamp'] = datetime.now().isoformat()
                return result
            else:
                return {
                    'success': False,
                    'message': f'Azione "{detected_action}" non ancora implementata',
                    'action': detected_action
                }
        
        except Exception as e:
            self.logger.error(f"Error processing natural language request: {e}")
            return {
                'success': False,
                'message': f'Errore nell\'elaborazione della richiesta: {e}',
                'error': str(e)
            }
    
    def _detect_intent(self, user_input: str, nl_patterns: Dict) -> Optional[str]:
        """Detect user intent from input text"""
        for action, pattern_info in nl_patterns.items():
            for pattern in pattern_info['patterns']:
                if re.search(pattern, user_input, re.IGNORECASE):
                    self.logger.debug(f"Detected intent: {action} (pattern: {pattern})")
                    return pattern_info['action']
        return None
    
    def _get_memory_status(self) -> Dict[str, Any]:
        """Get detailed memory status"""
        try:
            if not self.system_monitor:
                return {
                    'success': False,
                    'message': 'Sistema di monitoraggio non disponibile'
                }
            
            current_state = self.system_monitor.get_current_state()
            memory_info = current_state.get('memory', {})
            
            if not memory_info:
                return {
                    'success': False,
                    'message': 'Impossibile ottenere informazioni sulla memoria'
                }
            
            # Calculate memory values
            total_gb = memory_info.get('total', 0) / (1024**3)
            used_gb = memory_info.get('used', 0) / (1024**3)
            available_gb = memory_info.get('available', 0) / (1024**3)
            percent = memory_info.get('percent', 0)
            
            # Determine status
            if percent < 50:
                status = "🟢 Ottimale"
                recommendation = "La memoria è utilizzata in modo efficiente."
            elif percent < 75:
                status = "🟡 Moderato"
                recommendation = "Utilizzo memoria nella norma, monitora applicazioni pesanti."
            elif percent < 90:
                status = "🟠 Alto"
                recommendation = "Considera di chiudere applicazioni non necessarie."
            else:
                status = "🔴 Critico"
                recommendation = "ATTENZIONE: Memoria quasi esaurita! Chiudi applicazioni immediatamente."
            
            # Get top memory processes if available
            top_processes = []
            if hasattr(self.system_monitor, 'get_top_processes'):
                processes = self.system_monitor.get_top_processes(5)
                for proc in processes:
                    if proc.get('memory_percent', 0) > 1:  # Show processes using >1% memory
                        top_processes.append({
                            'name': proc.get('name', 'Unknown'),
                            'memory_percent': proc.get('memory_percent', 0),
                            'pid': proc.get('pid', 0)
                        })
            
            return {
                'success': True,
                'message': f'📊 **Situazione Memoria Sistema**\n\n'
                          f'**Stato:** {status}\n'
                          f'**Utilizzo:** {percent:.1f}% ({used_gb:.1f} GB di {total_gb:.1f} GB)\n'
                          f'**Disponibile:** {available_gb:.1f} GB\n\n'
                          f'**Raccomandazione:** {recommendation}',
                'data': {
                    'total_gb': round(total_gb, 2),
                    'used_gb': round(used_gb, 2),
                    'available_gb': round(available_gb, 2),
                    'percent': round(percent, 1),
                    'status': status,
                    'top_processes': top_processes
                },
                'recommendations': [recommendation] + (
                    ['Considera un upgrade di RAM'] if percent > 80 else []
                )
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nell\'analisi memoria: {e}'
            }
    
    def _get_disk_status(self) -> Dict[str, Any]:
        """Get detailed disk status"""
        try:
            if not self.disk_manager:
                return {
                    'success': False,
                    'message': 'Gestione dischi non disponibile'
                }
            
            health_summary = self.disk_manager.get_disk_health_summary()
            disk_info = self.disk_manager.get_disk_info()
            
            # Overall status
            if health_summary['critical_disks'] > 0:
                overall_status = "🔴 Critico"
            elif health_summary['warning_disks'] > 0:
                overall_status = "🟠 Attenzione"
            else:
                overall_status = "🟢 Buono"
            
            # Build disk details
            disk_details = []
            for disk in health_summary['disks']:
                health_emoji = {
                    'good': '🟢',
                    'warning': '🟡',
                    'critical': '🔴',
                    'unknown': '⚪'
                }.get(disk['health'], '⚪')
                
                disk_details.append(
                    f"  • **{disk['device']}**: {health_emoji} {disk['usage_percent']:.1f}% "
                    f"({disk['free_gb']:.1f} GB liberi di {disk['total_gb']:.1f} GB)"
                )
            
            # Recommendations
            recommendations = []
            if health_summary['average_usage_percent'] > 90:
                recommendations.append("Libera spazio su disco urgentemente")
            elif health_summary['average_usage_percent'] > 80:
                recommendations.append("Considera una pulizia dei file temporanei")
            
            if health_summary['critical_disks'] > 0:
                recommendations.append("BACKUP IMMEDIATO: Disco in stato critico!")
            elif health_summary['warning_disks'] > 0:
                recommendations.append("Monitora i dischi con warning")
            
            message = f'💾 **Situazione Dischi Sistema**\n\n' \
                     f'**Stato Generale:** {overall_status}\n' \
                     f'**Dischi Totali:** {health_summary["total_disks"]}\n' \
                     f'**Spazio Totale:** {health_summary["total_space_gb"]:.1f} GB\n' \
                     f'**Spazio Libero:** {health_summary["free_space_gb"]:.1f} GB\n' \
                     f'**Utilizzo Medio:** {health_summary["average_usage_percent"]:.1f}%\n\n' \
                     f'**Dettagli Dischi:**\n' + '\n'.join(disk_details)
            
            return {
                'success': True,
                'message': message,
                'data': health_summary,
                'recommendations': recommendations
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nell\'analisi dischi: {e}'
            }
    
    def _get_cpu_status(self) -> Dict[str, Any]:
        """Get detailed CPU status"""
        try:
            if not self.system_monitor:
                return {
                    'success': False,
                    'message': 'Sistema di monitoraggio non disponibile'
                }
            
            current_state = self.system_monitor.get_current_state()
            cpu_info = current_state.get('cpu', {})
            
            if not cpu_info:
                return {
                    'success': False,
                    'message': 'Impossibile ottenere informazioni CPU'
                }
            
            percent = cpu_info.get('percent', 0)
            cores = cpu_info.get('count', 0)
            frequency = cpu_info.get('frequency', 0)
            
            # Determine status
            if percent < 30:
                status = "🟢 Basso"
                recommendation = "CPU utilizzata in modo efficiente."
            elif percent < 60:
                status = "🟡 Moderato"
                recommendation = "Utilizzo CPU normale."
            elif percent < 85:
                status = "🟠 Alto"
                recommendation = "CPU sotto carico, verifica processi attivi."
            else:
                status = "🔴 Critico"
                recommendation = "CPU sovraccarica! Chiudi applicazioni pesanti."
            
            message = f'⚡ **Situazione CPU Sistema**\n\n' \
                     f'**Stato:** {status}\n' \
                     f'**Utilizzo:** {percent:.1f}%\n' \
                     f'**Core:** {cores}\n' \
                     f'**Frequenza:** {frequency:.0f} MHz\n\n' \
                     f'**Raccomandazione:** {recommendation}'
            
            return {
                'success': True,
                'message': message,
                'data': {
                    'percent': round(percent, 1),
                    'cores': cores,
                    'frequency': round(frequency, 0),
                    'status': status
                },
                'recommendations': [recommendation]
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nell\'analisi CPU: {e}'
            }
    
    def _analyze_performance(self) -> Dict[str, Any]:
        """Analyze overall system performance"""
        try:
            if not self.system_monitor:
                return {
                    'success': False,
                    'message': 'Sistema di monitoraggio non disponibile'
                }
            
            # Get performance analysis
            analysis = self.system_monitor.analyze_performance()
            performance_summary = self.system_monitor.get_performance_summary()
            
            status_emoji = {
                'normal': '🟢',
                'warning': '🟡',
                'critical': '🔴'
            }.get(analysis.get('status', 'normal'), '🟢')
            
            message = f'📈 **Analisi Performance Sistema**\n\n' \
                     f'**Stato:** {status_emoji} {analysis.get("status", "normal").title()}\n' \
                     f'**Uptime:** {performance_summary.get("uptime", "Unknown")}\n' \
                     f'**Processi Attivi:** {performance_summary.get("processes", 0)}\n' \
                     f'**Alert Attivi:** {performance_summary.get("alerts", 0)}\n\n'
            
            if analysis.get('issues'):
                message += '**Problemi Rilevati:**\n'
                for issue in analysis['issues']:
                    message += f'  • {issue}\n'
                message += '\n'
            
            if analysis.get('recommendations'):
                message += '**Raccomandazioni:**\n'
                for rec in analysis['recommendations']:
                    message += f'  • {rec}\n'
            
            return {
                'success': True,
                'message': message,
                'data': analysis,
                'recommendations': analysis.get('recommendations', [])
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nell\'analisi performance: {e}'
            }
    
    def _cleanup_system(self) -> Dict[str, Any]:
        """Perform system cleanup"""
        try:
            if not self.disk_manager:
                return {
                    'success': False,
                    'message': 'Gestione dischi non disponibile per la pulizia'
                }
            
            # Perform automatic cleanup
            cleanup_result = self.disk_manager.auto_cleanup_if_needed()
            
            if cleanup_result:
                message = f'🧹 **Pulizia Sistema Completata**\n\n' \
                         f'**File Rimossi:** {cleanup_result["files_removed"]}\n' \
                         f'**Spazio Liberato:** {cleanup_result["space_freed_gb"]:.2f} GB\n\n'
                
                if cleanup_result['errors']:
                    message += f'**Errori:** {len(cleanup_result["errors"])}\n'
                
                return {
                    'success': True,
                    'message': message,
                    'data': cleanup_result,
                    'recommendations': ['Esegui pulizia regolarmente per mantenere performance ottimali']
                }
            else:
                return {
                    'success': True,
                    'message': '✅ **Sistema Già Pulito**\n\nNon sono stati trovati file da rimuovere.',
                    'data': {'files_removed': 0, 'space_freed_gb': 0},
                    'recommendations': ['Il sistema è già ottimizzato']
                }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nella pulizia sistema: {e}'
            }
    
    def _get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system overview"""
        try:
            # Get system status
            status = self.core.get_system_status()
            
            message = f'🖥️ **Panoramica Sistema Completa**\n\n' \
                     f'**Sistema:** {status.get("platform", "Unknown")}\n' \
                     f'**Modalità:** {status.get("mode", "Unknown")}\n' \
                     f'**Sessione Attiva:** {"Sì" if status.get("session_active") else "No"}\n\n'
            
            # Add monitoring info if available
            if 'monitoring' in status:
                mon = status['monitoring']
                current = mon.get('current_state', {})
                message += f'**Monitoraggio:**\n' \
                          f'  • CPU: {current.get("cpu", {}).get("percent", 0):.1f}%\n' \
                          f'  • RAM: {current.get("memory", {}).get("percent", 0):.1f}%\n' \
                          f'  • Disk: {current.get("disk", {}).get("percent", 0):.1f}%\n' \
                          f'  • Alert: {mon.get("alerts", 0)}\n\n'
            
            # Add disk health if available
            if 'disk_health' in status:
                disk = status['disk_health']
                message += f'**Dischi:**\n' \
                          f'  • Totali: {disk["total_disks"]}\n' \
                          f'  • Sani: {disk["healthy_disks"]}\n' \
                          f'  • Warning: {disk["warning_disks"]}\n' \
                          f'  • Critici: {disk["critical_disks"]}\n' \
                          f'  • Spazio: {disk["free_space_gb"]:.1f} GB liberi\n\n'
            
            # Add recommendations
            recommendations = self.core.get_system_recommendations()
            if recommendations:
                message += f'**Raccomandazioni:** {len(recommendations)} suggerimenti disponibili'
            
            return {
                'success': True,
                'message': message,
                'data': status,
                'recommendations': [rec.get('message', '') for rec in recommendations[:3]]
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nel recupero panoramica sistema: {e}'
            }
    
    def _get_processes(self) -> Dict[str, Any]:
        """Get information about running processes"""
        try:
            if not self.system_monitor:
                return {
                    'success': False,
                    'message': 'Sistema di monitoraggio non disponibile'
                }
            
            # Get top processes
            top_processes = self.system_monitor.get_top_processes(10)
            current_state = self.system_monitor.get_current_state()
            total_processes = current_state.get('processes', 0)
            
            message = f'🔄 **Processi Sistema**\n\n' \
                     f'**Processi Totali:** {total_processes}\n\n' \
                     f'**Top Processi per CPU:**\n'
            
            for i, proc in enumerate(top_processes[:5], 1):
                cpu_percent = proc.get('cpu_percent', 0)
                memory_percent = proc.get('memory_percent', 0)
                name = proc.get('name', 'Unknown')
                pid = proc.get('pid', 0)
                
                message += f'  {i}. **{name}** (PID: {pid})\n' \
                          f'     CPU: {cpu_percent:.1f}% | RAM: {memory_percent:.1f}%\n'
            
            return {
                'success': True,
                'message': message,
                'data': {
                    'total_processes': total_processes,
                    'top_processes': top_processes
                },
                'recommendations': [
                    'Monitora processi con alto utilizzo CPU',
                    'Termina processi non necessari per liberare risorse'
                ]
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nel recupero processi: {e}'
            }
    
    def _get_network_status(self) -> Dict[str, Any]:
        """Get network status information"""
        try:
            if not self.system_monitor:
                return {
                    'success': False,
                    'message': 'Sistema di monitoraggio non disponibile'
                }
            
            # Get network usage
            network_usage = self.system_monitor.get_network_usage()
            current_state = self.system_monitor.get_current_state()
            network_info = current_state.get('network', {})
            
            message = f'🌐 **Stato Rete**\n\n'
            
            # Show current traffic
            sent_kb = network_info.get('sent_bytes', 0) / 1024
            recv_kb = network_info.get('recv_bytes', 0) / 1024
            
            message += f'**Traffico Corrente:**\n' \
                      f'  • Inviato: {sent_kb:.1f} KB/s\n' \
                      f'  • Ricevuto: {recv_kb:.1f} KB/s\n\n'
            
            # Show interfaces if available
            if network_usage:
                message += f'**Interfacce Attive:** {len(network_usage)}\n'
                for interface, data in list(network_usage.items())[:3]:
                    if data.get('addresses'):
                        ipv4_addrs = [addr['address'] for addr in data['addresses'] 
                                    if addr.get('family') == 'IPv4']
                        if ipv4_addrs:
                            message += f'  • **{interface}**: {ipv4_addrs[0]}\n'
            
            return {
                'success': True,
                'message': message,
                'data': {
                    'current_traffic': network_info,
                    'interfaces': network_usage
                },
                'recommendations': [
                    'Monitora il traffico per identificare utilizzo anomalo',
                    'Verifica la connettività se ci sono problemi'
                ]
            }
        
        except Exception as e:
            return {
                'success': False,
                'message': f'Errore nel recupero stato rete: {e}'
            }

    def process_enhanced_command(self, user_input: str) -> Dict[str, Any]:
        """Process command using enhanced AI integration patterns"""
        if not self.ai_integration:
            return self.process_request(user_input)

        try:
            # Update context with current system state
            self.context.update({
                'current_time': datetime.now(),
                'system_state': self._get_current_system_state()
            })

            # Parse command using enhanced AI
            parsed_command = self.ai_integration.parse_natural_language_command(user_input)

            # Add to command history
            self.context['command_history'].append({
                'input': user_input,
                'parsed': parsed_command,
                'timestamp': datetime.now()
            })

            # Handle web-related commands
            if parsed_command.get('tool') in ['web_search', 'fetch_webpage', 'analyze_webpage', 'browsing_history']:
                return self._process_web_command(parsed_command, user_input)

            # Handle Excel-related commands
            if parsed_command.get('tool') in ['create_excel_analysis', 'analyze_excel_file']:
                return self._process_excel_command(parsed_command, user_input)

            # Keep only last 10 commands in history
            if len(self.context['command_history']) > 10:
                self.context['command_history'] = self.context['command_history'][-10:]

            # Process based on parsed intent
            if parsed_command.get('tool') in ['security_audit', 'performance_analysis',
                                            'network_security_scan', 'enterprise_health_check',
                                            'system_hardening', 'backup_analysis']:
                return self._process_enterprise_command(parsed_command, user_input)
            else:
                return self._process_standard_command(parsed_command, user_input)

        except Exception as e:
            self.logger.error(f"Enhanced command processing failed: {e}")
            return self.process_request(user_input)

    def _get_current_system_state(self) -> Dict[str, Any]:
        """Get current system state for context"""
        try:
            import psutil
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_percent': psutil.disk_usage('/').percent if hasattr(psutil.disk_usage('/'), 'percent') else 0,
                'process_count': len(psutil.pids()),
                'boot_time': psutil.boot_time(),
                'timestamp': time.time()
            }
        except Exception as e:
            self.logger.warning(f"Could not get system state: {e}")
            return {'timestamp': time.time()}

    def _process_enterprise_command(self, parsed_command: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Process enterprise-level commands with enhanced formatting"""
        try:
            tool_name = parsed_command.get('tool')
            intent = parsed_command.get('intent', 'unknown')
            priority = parsed_command.get('priority', 'medium')

            # Create enterprise response structure
            response = {
                'success': True,
                'message': f"🏢 **CYBEX ENTERPRISE - {intent.upper().replace('_', ' ')}**\n\n",
                'tool_executed': tool_name,
                'priority': priority,
                'enterprise_mode': True
            }

            # Add executive summary
            response['message'] += f"**🎯 EXECUTIVE SUMMARY**\n"
            response['message'] += f"Operation: {tool_name.replace('_', ' ').title()}\n"
            response['message'] += f"Priority: {priority.upper()}\n"
            response['message'] += f"Status: ✅ Initiated\n\n"

            # Add technical details based on tool
            if tool_name == 'security_audit':
                response['message'] += "**🔒 SECURITY ANALYSIS**\n"
                response['message'] += "• Comprehensive system security audit initiated\n"
                response['message'] += "• Vulnerability assessment in progress\n"
                response['message'] += "• Compliance check scheduled\n\n"
            elif tool_name == 'performance_analysis':
                response['message'] += "**⚡ PERFORMANCE MONITORING**\n"
                response['message'] += f"• CPU Usage: {self.context.get('system_state', {}).get('cpu_percent', 0):.1f}%\n"
                response['message'] += f"• Memory Usage: {self.context.get('system_state', {}).get('memory_percent', 0):.1f}%\n"
                response['message'] += "• Real-time monitoring active\n\n"
            elif tool_name == 'network_security_scan':
                response['message'] += "**🌐 NETWORK SECURITY**\n"
                response['message'] += "• Network vulnerability scan initiated\n"
                response['message'] += "• Port analysis in progress\n"
                response['message'] += "• Security assessment active\n\n"

            # Add recommendations
            response['message'] += "**✅ NEXT STEPS**\n"
            response['message'] += "• Monitor operation progress\n"
            response['message'] += "• Review detailed results when complete\n"
            response['message'] += "• Implement recommended actions\n"

            return response

        except Exception as e:
            return {
                'success': False,
                'message': f'❌ Enterprise command processing error: {e}',
                'enterprise_mode': True
            }

    def _process_standard_command(self, parsed_command: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Process standard commands with enhanced context"""
        try:
            # Use existing process_request but with enhanced context
            result = self.process_request(user_input)

            # Enhance result with AI integration formatting
            if result.get('success') and self.ai_integration:
                enhanced_context = {
                    'operation': parsed_command.get('intent', 'system_operation'),
                    'priority': parsed_command.get('priority', 'medium'),
                    'intent': parsed_command.get('intent', 'unknown')
                }

                # Format using enterprise patterns
                formatted_response = self.ai_integration.format_enterprise_response(
                    {'success': True, 'output': result.get('message', '')},
                    enhanced_context
                )

                result['message'] = formatted_response
                result['enhanced'] = True

            return result

        except Exception as e:
            return {
                'success': False,
                'message': f'❌ Standard command processing error: {e}'
            }

    def _process_web_command(self, parsed_command: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Process web-related commands with enhanced formatting"""
        try:
            tool_name = parsed_command.get('tool')
            intent = parsed_command.get('intent', 'unknown')
            priority = parsed_command.get('priority', 'medium')

            # Create web response structure
            response = {
                'success': True,
                'message': f"🌐 **CYBEX WEB AGENT - {intent.upper().replace('_', ' ')}**\n\n",
                'tool_executed': tool_name,
                'priority': priority,
                'web_mode': True
            }

            # Add operation summary
            response['message'] += f"**🎯 WEB OPERATION**\n"
            response['message'] += f"Operation: {tool_name.replace('_', ' ').title()}\n"
            response['message'] += f"Priority: {priority.upper()}\n"
            response['message'] += f"Status: ✅ Initiated\n\n"

            # Add operation-specific details
            if tool_name == 'web_search':
                query = parsed_command.get('parameters', {}).get('query', user_input)
                engine = parsed_command.get('parameters', {}).get('engine', 'google')
                response['message'] += "**🔍 WEB SEARCH**\n"
                response['message'] += f"• Search Query: '{query}'\n"
                response['message'] += f"• Search Engine: {engine.title()}\n"
                response['message'] += f"• Max Results: {parsed_command.get('parameters', {}).get('max_results', 10)}\n"
                response['message'] += "• Searching the web for relevant information...\n\n"

            elif tool_name == 'fetch_webpage':
                url = parsed_command.get('parameters', {}).get('url', '')
                response['message'] += "**📄 WEBPAGE FETCH**\n"
                response['message'] += f"• Target URL: {url}\n"
                response['message'] += f"• Browser Mode: {'Yes' if parsed_command.get('parameters', {}).get('use_browser') else 'No'}\n"
                response['message'] += "• Fetching webpage content and metadata...\n\n"

            elif tool_name == 'analyze_webpage':
                url = parsed_command.get('parameters', {}).get('url', '')
                analysis_type = parsed_command.get('parameters', {}).get('analysis_type', 'general')
                response['message'] += "**📊 WEBPAGE ANALYSIS**\n"
                response['message'] += f"• Target URL: {url}\n"
                response['message'] += f"• Analysis Type: {analysis_type.title()}\n"
                response['message'] += "• Analyzing webpage content and structure...\n\n"

            elif tool_name == 'browsing_history':
                response['message'] += "**📚 BROWSING HISTORY**\n"
                response['message'] += "• Retrieving search and browsing history\n"
                response['message'] += "• Compiling usage statistics\n\n"

            # Add next steps
            response['message'] += "**✅ NEXT STEPS**\n"
            response['message'] += "• Processing web request\n"
            response['message'] += "• Analyzing retrieved data\n"
            response['message'] += "• Formatting results for presentation\n"

            return response

        except Exception as e:
            return {
                'success': False,
                'message': f'❌ Web command processing error: {e}',
                'web_mode': True
            }

    def _process_excel_command(self, parsed_command: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Process Excel-related commands with enhanced formatting"""
        try:
            tool_name = parsed_command.get('tool')
            intent = parsed_command.get('intent', 'unknown')
            priority = parsed_command.get('priority', 'medium')

            # Create Excel response structure
            response = {
                'success': True,
                'message': f"📊 **CYBEX EXCEL AGENT - {intent.upper().replace('_', ' ')}**\n\n",
                'tool_executed': tool_name,
                'priority': priority,
                'excel_mode': True
            }

            # Add operation summary
            response['message'] += f"**📈 EXCEL OPERATION**\n"
            response['message'] += f"Operation: {tool_name.replace('_', ' ').title()}\n"
            response['message'] += f"Priority: {priority.upper()}\n"
            response['message'] += f"Status: ✅ Initiated\n\n"

            # Add operation-specific details
            if tool_name == 'create_excel_analysis':
                analysis_type = parsed_command.get('parameters', {}).get('analysis_type', 'general')
                style_theme = parsed_command.get('parameters', {}).get('style_theme', 'corporate_blue')
                file_path = parsed_command.get('parameters', {}).get('file_path', 'analysis.xlsx')

                response['message'] += "**📊 EXCEL CREATION**\n"
                response['message'] += f"• Analysis Type: {analysis_type.title()}\n"
                response['message'] += f"• Style Theme: {style_theme.replace('_', ' ').title()}\n"
                response['message'] += f"• Output File: {file_path}\n"
                response['message'] += "• Creating professional Excel with multiple sheets\n"
                response['message'] += "• Applying corporate styling and formatting\n"
                response['message'] += "• Generating charts and statistical analysis\n\n"

            elif tool_name == 'analyze_excel_file':
                file_path = parsed_command.get('parameters', {}).get('file_path', '')
                response['message'] += "**📋 EXCEL ANALYSIS**\n"
                response['message'] += f"• Target File: {file_path}\n"
                response['message'] += "• Analyzing sheet structure and content\n"
                response['message'] += "• Extracting metadata and statistics\n"
                response['message'] += "• Generating comprehensive report\n\n"

            # Add Excel capabilities info
            response['message'] += "**✨ EXCEL CAPABILITIES**\n"
            response['message'] += "• Professional formatting and styling\n"
            response['message'] += "• Multiple analysis types (General, Financial, Sales, System)\n"
            response['message'] += "• Corporate themes (Blue, Green, Executive)\n"
            response['message'] += "• Charts and data visualization\n"
            response['message'] += "• Statistical analysis and summaries\n"
            response['message'] += "• Multi-sheet workbooks with navigation\n"

            return response

        except Exception as e:
            return {
                'success': False,
                'message': f'❌ Excel command processing error: {e}',
                'excel_mode': True
            }
