"""
Command Executor Mo<PERSON>le
Handles safe command execution with subprocess wrapper and error control
"""

import subprocess
import platform
import os
import time
import signal
import logging
from typing import Tuple, Optional, Dict, Any, List
from pathlib import Path


class CommandExecutor:
    """
    Executes system commands safely with proper error handling and timeouts
    """
    
    def __init__(self, config_manager, security_manager, log_manager):
        """Initialize command executor"""
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        
        # Get system type
        self.system_type = platform.system().lower()
        self.is_windows = self.system_type == 'windows'
        
        # Default timeout for commands
        self.default_timeout = 30
        
        # Command history
        self.command_history: List[Dict] = []
    
    def execute_command(self, command: str, timeout: Optional[int] = None, 
                       cwd: Optional[str] = None, confirm_required: bool = True) -> Tuple[bool, str, Dict]:
        """
        Execute a system command safely
        Returns (success, output, metadata)
        """
        start_time = time.time()
        
        # Validate command security
        is_safe, reason = self.security_manager.validate_command(command)
        if not is_safe:
            error_msg = f"Command blocked by security policy: {reason}"
            self.logger.error(error_msg)
            return False, error_msg, {'execution_time': 0, 'security_blocked': True}
        
        # Check if confirmation is required
        if confirm_required and self.security_manager.get_security_level(command) != 'safe':
            self.logger.warning(f"Command requires confirmation: {command}")
            return False, "Command requires user confirmation", {'requires_confirmation': True}
        
        # Set timeout
        if timeout is None:
            timeout = self.default_timeout
        
        # Prepare command for execution
        cmd_args = self._prepare_command(command)
        
        try:
            # Execute command
            self.logger.info(f"Executing command: {command}")
            
            result = subprocess.run(
                cmd_args,
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=cwd,
                shell=True if self.is_windows else False
            )
            
            execution_time = time.time() - start_time
            
            # Process results
            success = result.returncode == 0
            output = result.stdout if success else result.stderr
            
            # Sanitize output
            output = self.security_manager.sanitize_output(output)
            
            # Create metadata
            metadata = {
                'return_code': result.returncode,
                'execution_time': execution_time,
                'command': command,
                'cwd': cwd or os.getcwd(),
                'timeout': timeout
            }
            
            # Log execution
            self.log_manager.log_command_execution(command, output, success, execution_time)
            
            # Add to history
            self._add_to_history(command, output, success, metadata)
            
            return success, output, metadata
            
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            error_msg = f"Command timed out after {timeout} seconds"
            self.logger.error(f"Command timeout: {command}")
            
            metadata = {
                'return_code': -1,
                'execution_time': execution_time,
                'command': command,
                'timeout_expired': True
            }
            
            self._add_to_history(command, error_msg, False, metadata)
            return False, error_msg, metadata
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Command execution failed: {str(e)}"
            self.logger.error(f"Command execution error: {command} - {e}")
            
            metadata = {
                'return_code': -1,
                'execution_time': execution_time,
                'command': command,
                'exception': str(e)
            }
            
            self._add_to_history(command, error_msg, False, metadata)
            return False, error_msg, metadata
    
    def _prepare_command(self, command: str) -> List[str]:
        """Prepare command for execution based on OS"""
        if self.is_windows:
            # Windows: use cmd.exe for command execution
            return ['cmd', '/c', command]
        else:
            # Unix-like: use bash
            return ['/bin/bash', '-c', command]
    
    def execute_batch_commands(self, commands: List[str], stop_on_error: bool = True,
                             timeout_per_command: Optional[int] = None) -> List[Tuple[bool, str, Dict]]:
        """
        Execute multiple commands in sequence
        Returns list of (success, output, metadata) tuples
        """
        results = []
        
        for i, command in enumerate(commands):
            self.logger.info(f"Executing batch command {i+1}/{len(commands)}: {command}")
            
            success, output, metadata = self.execute_command(
                command, 
                timeout=timeout_per_command,
                confirm_required=False  # Batch commands should be pre-confirmed
            )
            
            results.append((success, output, metadata))
            
            if not success and stop_on_error:
                self.logger.warning(f"Batch execution stopped at command {i+1} due to error")
                break
        
        return results
    
    def get_system_commands(self) -> Dict[str, List[str]]:
        """Get available system commands by category"""
        if self.is_windows:
            return {
                'file_operations': ['dir', 'type', 'copy', 'move', 'del', 'mkdir', 'rmdir'],
                'system_info': ['systeminfo', 'hostname', 'whoami', 'ver'],
                'network': ['ping', 'ipconfig', 'netstat', 'nslookup'],
                'process': ['tasklist', 'taskkill', 'wmic process'],
                'disk': ['wmic logicaldisk', 'chkdsk', 'diskpart'],
                'service': ['net start', 'net stop', 'sc query']
            }
        else:
            return {
                'file_operations': ['ls', 'cat', 'cp', 'mv', 'rm', 'mkdir', 'rmdir'],
                'system_info': ['uname', 'hostname', 'whoami', 'uptime', 'free'],
                'network': ['ping', 'ifconfig', 'netstat', 'dig'],
                'process': ['ps', 'kill', 'killall', 'top'],
                'disk': ['df', 'du', 'fdisk', 'mount'],
                'service': ['systemctl', 'service']
            }
    
    def validate_command_syntax(self, command: str) -> Tuple[bool, str]:
        """Validate command syntax without executing"""
        try:
            # Basic syntax validation
            if not command.strip():
                return False, "Empty command"
            
            # Check for basic shell injection patterns
            dangerous_chars = [';', '&&', '||', '|', '>', '>>', '<']
            if any(char in command for char in dangerous_chars):
                # Allow some safe uses
                if not self._is_safe_shell_usage(command):
                    return False, "Potentially unsafe shell operators detected"
            
            # Platform-specific validation
            if self.is_windows:
                return self._validate_windows_command(command)
            else:
                return self._validate_unix_command(command)
                
        except Exception as e:
            return False, f"Syntax validation error: {e}"
    
    def _is_safe_shell_usage(self, command: str) -> bool:
        """Check if shell operators are used safely"""
        # This is a simplified check - in production, this would be more sophisticated
        safe_patterns = [
            r'echo .* > .*\.txt',  # Simple file redirection
            r'cat .* \| grep .*',  # Simple pipe usage
        ]
        
        import re
        for pattern in safe_patterns:
            if re.match(pattern, command):
                return True
        
        return False
    
    def _validate_windows_command(self, command: str) -> Tuple[bool, str]:
        """Validate Windows-specific command syntax"""
        # Check for valid Windows commands
        valid_commands = ['dir', 'type', 'copy', 'move', 'del', 'mkdir', 'rmdir',
                         'systeminfo', 'hostname', 'whoami', 'ver', 'ping', 'ipconfig',
                         'netstat', 'nslookup', 'tasklist', 'taskkill', 'wmic', 'net', 'sc']
        
        first_word = command.strip().split()[0].lower()
        if first_word not in valid_commands:
            return False, f"Unknown or restricted Windows command: {first_word}"
        
        return True, "Valid Windows command"
    
    def _validate_unix_command(self, command: str) -> Tuple[bool, str]:
        """Validate Unix-specific command syntax"""
        # Check for valid Unix commands
        valid_commands = ['ls', 'cat', 'cp', 'mv', 'rm', 'mkdir', 'rmdir',
                         'uname', 'hostname', 'whoami', 'uptime', 'free', 'ping',
                         'ifconfig', 'netstat', 'dig', 'ps', 'kill', 'killall',
                         'top', 'df', 'du', 'fdisk', 'mount', 'systemctl', 'service']
        
        first_word = command.strip().split()[0].lower()
        if first_word not in valid_commands:
            return False, f"Unknown or restricted Unix command: {first_word}"
        
        return True, "Valid Unix command"
    
    def _add_to_history(self, command: str, output: str, success: bool, metadata: Dict) -> None:
        """Add command execution to history"""
        history_entry = {
            'timestamp': time.time(),
            'command': command,
            'output': output[:500],  # Truncate long outputs
            'success': success,
            'metadata': metadata
        }
        
        self.command_history.append(history_entry)
        
        # Keep only last 100 commands
        if len(self.command_history) > 100:
            self.command_history = self.command_history[-100:]
    
    def get_command_history(self, limit: int = 10) -> List[Dict]:
        """Get recent command history"""
        return self.command_history[-limit:] if self.command_history else []
    
    def clear_history(self) -> None:
        """Clear command history"""
        self.command_history.clear()
        self.logger.info("Command history cleared")
