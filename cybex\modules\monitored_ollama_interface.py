#!/usr/bin/env python3
"""
Monitored Ollama Interface
Wrapper for OllamaInterface with automatic monitoring integration
"""

import time
from typing import Dict, Any, Optional
from .ollama_interface import OllamaInterface
from .ollama_monitor import OllamaMonitor, OllamaStatus

class MonitoredOllamaInterface:
    """Ollama interface with integrated monitoring"""
    
    def __init__(self, config_manager, log_manager, monitor: Optional[OllamaMonitor] = None):
        self.ollama_interface = OllamaInterface(config_manager, log_manager)
        self.monitor = monitor
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
    
    def set_monitor(self, monitor: OllamaMonitor):
        """Set the monitor instance"""
        self.monitor = monitor
        self.logger.info("Monitor attached to Ollama interface")
    
    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> Any:
        """Generate response with monitoring"""
        try:
            # Start monitoring
            if self.monitor:
                current_model = getattr(self.ollama_interface, 'model', 'unknown')
                self.monitor.start_activity(current_model, prompt)
            
            # Generate response
            start_time = time.time()
            response = self.ollama_interface.generate_response(prompt, context)
            response_time = time.time() - start_time
            
            # End monitoring
            if self.monitor:
                if hasattr(response, 'success') and response.success:
                    self.monitor.end_activity(success=True)
                else:
                    error_msg = getattr(response, 'error', 'Unknown error')
                    self.monitor.end_activity(success=False, error=error_msg)
            
            self.logger.info(f"Response generated in {response_time:.2f}s")
            return response
            
        except Exception as e:
            # End monitoring on error
            if self.monitor:
                self.monitor.end_activity(success=False, error=str(e))
            
            self.logger.error(f"Response generation failed: {e}")
            raise
    
    def switch_model(self, model_name: str) -> bool:
        """Switch model with monitoring"""
        try:
            # Start monitoring
            if self.monitor:
                self.monitor.start_activity(model_name, f"Switching to {model_name}")
            
            # Switch model
            success = self.ollama_interface.switch_model(model_name)
            
            # End monitoring
            if self.monitor:
                if success:
                    self.monitor.end_activity(success=True)
                else:
                    self.monitor.end_activity(success=False, error="Model switch failed")
            
            return success
            
        except Exception as e:
            # End monitoring on error
            if self.monitor:
                self.monitor.end_activity(success=False, error=str(e))
            raise
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get available models with monitoring"""
        try:
            # Start monitoring
            if self.monitor:
                self.monitor.start_activity("system", "Fetching available models")
            
            # Get models
            result = self.ollama_interface.get_available_models()
            
            # End monitoring
            if self.monitor:
                if result.get('success', False):
                    self.monitor.end_activity(success=True)
                else:
                    self.monitor.end_activity(success=False, error="Failed to fetch models")
            
            return result
            
        except Exception as e:
            # End monitoring on error
            if self.monitor:
                self.monitor.end_activity(success=False, error=str(e))
            raise
    
    def is_server_available(self) -> bool:
        """Check if server is available"""
        return self.ollama_interface.is_server_available()
    
    def test_connection(self) -> Dict[str, Any]:
        """Test connection with monitoring"""
        try:
            # Start monitoring
            if self.monitor:
                self.monitor.start_activity("system", "Testing connection")
            
            # Test connection
            result = self.ollama_interface.test_connection()
            
            # End monitoring
            if self.monitor:
                if result.get('success', False):
                    self.monitor.end_activity(success=True)
                else:
                    error_msg = result.get('error', 'Connection test failed')
                    self.monitor.end_activity(success=False, error=error_msg)
            
            return result
            
        except Exception as e:
            # End monitoring on error
            if self.monitor:
                self.monitor.end_activity(success=False, error=str(e))
            raise
    
    # Delegate all other attributes to the underlying interface
    def __getattr__(self, name):
        """Delegate attribute access to underlying interface"""
        return getattr(self.ollama_interface, name)
    
    def __setattr__(self, name, value):
        """Handle attribute setting"""
        if name in ['ollama_interface', 'monitor', 'log_manager', 'logger']:
            super().__setattr__(name, value)
        else:
            # Try to set on underlying interface if it exists
            if hasattr(self, 'ollama_interface') and hasattr(self.ollama_interface, name):
                setattr(self.ollama_interface, name, value)
            else:
                super().__setattr__(name, value)
