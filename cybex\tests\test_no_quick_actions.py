#!/usr/bin/env python3
"""
Test No Quick Actions
Verifica che le quick actions siano disabilitate e l'AI usi i tool avanzati
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_quick_actions_disabled():
    """Test that quick actions are disabled"""
    print("🚫 Testing Quick Actions Disabled")
    print("=" * 40)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        # Initialize UI
        ui = CybexEnterpriseUI()
        
        print("✅ Enterprise UI initialized")
        
        # Check that quick actions are empty
        if not ui.quick_actions:
            print("✅ Quick actions are disabled (empty dict)")
        else:
            print(f"❌ Quick actions still active: {len(ui.quick_actions)} actions")
            return False
        
        # Test that numbers 1-9 don't trigger quick actions
        test_inputs = ['1', '2', '3', '4', '5', '6', '7', '8', '9']
        
        for test_input in test_inputs:
            if test_input in ui.quick_actions:
                print(f"❌ Quick action {test_input} still exists")
                return False
        
        print("✅ Numbers 1-9 will not trigger quick actions")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_ai_tools_available():
    """Test that AI tools are available"""
    print(f"\n🛠️  Testing AI Tools Available")
    print("=" * 35)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        
        # Check agent tools
        if hasattr(ui, 'agent_tools') and ui.agent_tools:
            print("✅ Agent tools are available")
            
            tools = ui.agent_tools.get_available_tools()
            print(f"✅ Found {len(tools)} available tools:")
            
            expected_tools = [
                'execute_command',
                'list_directory', 
                'get_system_info',
                'list_processes',
                'network_scan',
                'scan_disk',
                'cleanup_temp_files'
            ]
            
            for tool_name in expected_tools:
                if tool_name in tools:
                    print(f"  ✅ {tool_name}")
                else:
                    print(f"  ❌ {tool_name} missing")
                    return False
            
            return True
        else:
            print("❌ Agent tools not available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_natural_language_processing():
    """Test that natural language goes to AI instead of quick actions"""
    print(f"\n💬 Testing Natural Language Processing")
    print("=" * 45)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        
        # Test messages that would previously trigger quick actions
        test_messages = [
            "scansiona disco C",
            "stato del sistema", 
            "memoria del computer",
            "processi in esecuzione",
            "connessioni di rete"
        ]
        
        print("Testing that these messages will go to AI:")
        
        for message in test_messages:
            # These should NOT match any quick action
            cmd = message.lower().strip()
            
            if cmd in ui.quick_actions:
                print(f"❌ '{message}' still triggers quick action")
                return False
            else:
                print(f"✅ '{message}' will go to AI")
        
        print("✅ All natural language messages will be processed by AI")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_direct_tool_commands():
    """Test direct tool commands still work"""
    print(f"\n⚡ Testing Direct Tool Commands")
    print("=" * 40)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        
        # Test that tool commands are recognized
        tool_commands = [
            '/tools',
            '/ops',
            '/tool scan_disk',
            '/help'
        ]
        
        print("Testing direct tool commands:")
        
        for cmd in tool_commands:
            # These should be handled by the chat command system
            print(f"✅ '{cmd}' - Direct tool command available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def show_new_interface_summary():
    """Show summary of new interface"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    summary = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                      {SunriseColors.ACCENT}🤖 NEW AI AGENT INTERFACE{SunriseColors.SUCCESS}                               ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}✅ CHANGES MADE:{SunriseColors.SUCCESS}                                                               ║
║                                                                               ║
║ {SunriseColors.INFO}🚫 Quick Actions Disabled{SunriseColors.SUCCESS}                                                      ║
║   • Numbers 1-9 no longer trigger preset actions                             ║
║   • All input goes directly to AI agent                                      ║
║   • AI can use advanced tools for better responses                           ║
║                                                                               ║
║ {SunriseColors.INFO}🤖 AI Agent Enhanced{SunriseColors.SUCCESS}                                                           ║
║   • Natural language processing improved                                     ║
║   • Access to 7 advanced tools                                               ║
║   • Real-time operation monitoring                                           ║
║   • Intelligent tool selection                                               ║
║                                                                               ║
║ {SunriseColors.INFO}💬 Natural Language Examples{SunriseColors.SUCCESS}                                                  ║
║   • "Scansiona disco C" → AI uses scan_disk tool                             ║
║   • "Elimina file temp" → AI uses cleanup_temp_files with preview            ║
║   • "Mostra processi" → AI uses list_processes tool                          ║
║   • "Stato sistema" → AI uses get_system_info tool                           ║
║                                                                               ║
║ {SunriseColors.INFO}⚡ Direct Commands Still Available{SunriseColors.SUCCESS}                                            ║
║   • /tools - Show available tools                                            ║
║   • /ops - Toggle operations monitor                                         ║
║   • /tool <name> - Execute tool directly                                     ║
║   • /help - Show help                                                        ║
║                                                                               ║
║ {SunriseColors.ACCENT}🎯 RESULT:{SunriseColors.SUCCESS}                                                                   ║
║   Now when you type "Scansiona disco C", the AI will:                        ║
║   1. Understand your request                                                 ║
║   2. Choose the appropriate tool (scan_disk)                                 ║
║   3. Execute comprehensive disk analysis                                     ║
║   4. Provide intelligent interpretation of results                           ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(summary)


def main():
    """Run no quick actions tests"""
    print("🎯 Cybex Enterprise - No Quick Actions Test")
    print("=" * 60)
    
    tests = [
        ("Quick Actions Disabled", test_quick_actions_disabled),
        ("AI Tools Available", test_ai_tools_available),
        ("Natural Language Processing", test_natural_language_processing),
        ("Direct Tool Commands", test_direct_tool_commands)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        show_new_interface_summary()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Quick actions successfully disabled!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}AI agent now has full control over all input!{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Try now:{SunriseColors.RESET}")
        print(f"  1. python main_enterprise.py")
        print(f"  2. Type: 'Scansiona disco C'")
        print(f"  3. Watch the AI use advanced tools!")
        print(f"  4. No more quick action interference!")
    else:
        print(f"⚠️  Some tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
