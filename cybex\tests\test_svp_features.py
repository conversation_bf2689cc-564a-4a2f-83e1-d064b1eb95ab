#!/usr/bin/env python3
"""
Test SVP Features
Comprehensive test of Cybex SVP (Strategic Viable Product) features
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

from cybex.core.cybex_core import CybexCore
from cybex.modules.command_executor import CommandExecutor
from cybex.modules.ollama_interface import OllamaInterface


def test_svp_initialization():
    """Test SVP module initialization"""
    print("🚀 Testing SVP Module Initialization")
    
    try:
        # Initialize core
        core = CybexCore()
        print("✅ Core initialized")
        
        # Initialize command executor
        command_executor = CommandExecutor(
            core.config_manager,
            core.security_manager,
            core.log_manager
        )
        print("✅ Command executor initialized")
        
        # Initialize Ollama interface
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        print("✅ Ollama interface initialized")
        
        # Initialize agent modules
        core._init_agent_modules(command_executor, ollama_interface)
        print("✅ Agent modules initialized")
        
        return core, command_executor, ollama_interface
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return None, None, None


def test_system_monitoring(core):
    """Test system monitoring features"""
    print("\n📊 Testing System Monitoring")
    
    if not core.system_monitor:
        print("⚠️  System monitoring not available")
        return
    
    try:
        # Get current state
        current_state = core.system_monitor.get_current_state()
        print(f"✅ Current state retrieved: CPU {current_state.get('cpu', {}).get('percent', 0):.1f}%")
        
        # Get performance summary
        summary = core.system_monitor.get_performance_summary()
        print(f"✅ Performance summary: {summary.get('uptime', 'Unknown')} uptime")
        
        # Get alerts
        alerts = core.system_monitor.get_alerts()
        print(f"✅ Alerts retrieved: {len(alerts)} active alerts")
        
    except Exception as e:
        print(f"❌ System monitoring test failed: {e}")


def test_disk_management(core):
    """Test disk management features"""
    print("\n💾 Testing Disk Management")
    
    if not core.disk_manager:
        print("⚠️  Disk management not available")
        return
    
    try:
        # Get disk info
        disk_info = core.disk_manager.get_disk_info()
        print(f"✅ Disk info retrieved: {len(disk_info)} disks found")
        
        # Get health summary
        health_summary = core.disk_manager.get_disk_health_summary()
        print(f"✅ Health summary: {health_summary['healthy_disks']} healthy disks")
        
        # Scan for cleanup (dry run)
        from cybex.modules.disk_manager import CleanupCategory
        cleanup_data = core.disk_manager.scan_for_cleanup([CleanupCategory.TEMP_FILES])
        total_files = sum(data['total_count'] for data in cleanup_data.values())
        print(f"✅ Cleanup scan: {total_files} files found for cleanup")
        
    except Exception as e:
        print(f"❌ Disk management test failed: {e}")


def test_database_persistence(core):
    """Test database persistence features"""
    print("\n🗄️  Testing Database Persistence")
    
    if not core.database_manager:
        print("⚠️  Database management not available")
        return
    
    try:
        # Create test user profile
        success = core.database_manager.create_user_profile(
            "test_user", 
            "Test User",
            {"theme": "dark", "auto_confirm": False}
        )
        print(f"✅ User profile created: {success}")
        
        # Get user profile
        profile = core.database_manager.get_user_profile("test_user")
        print(f"✅ User profile retrieved: {profile['display_name'] if profile else 'None'}")
        
        # Get database stats
        stats = core.database_manager.get_database_stats()
        print(f"✅ Database stats: {stats.get('file_size_mb', 0):.2f} MB")
        
    except Exception as e:
        print(f"❌ Database persistence test failed: {e}")


def test_agent_planning(core):
    """Test agent planning features"""
    print("\n🤖 Testing Agent Planning")
    
    if not core.agent_planner:
        print("⚠️  Agent planning not available")
        return
    
    try:
        # Create a simple plan
        plan_id = core.create_agent_plan(
            "Check system status and show disk usage",
            {"system_info": "Windows 11", "username": "test_user"}
        )
        
        if plan_id:
            print(f"✅ Agent plan created: {plan_id[:8]}...")
            
            # Get plan details
            plan = core.agent_planner.get_plan(plan_id)
            if plan:
                print(f"✅ Plan details: {len(plan.tasks)} tasks, risk level: {plan.risk_level}")
            
        else:
            print("❌ Failed to create agent plan")
        
    except Exception as e:
        print(f"❌ Agent planning test failed: {e}")


def test_system_snapshots(core):
    """Test system snapshot features"""
    print("\n📸 Testing System Snapshots")
    
    if not core.system_snapshot:
        print("⚠️  System snapshots not available")
        return
    
    try:
        # Create test snapshot
        snapshot_id = core.system_snapshot.create_snapshot(
            "test_snapshot",
            "Test snapshot for SVP testing"
        )
        
        if snapshot_id:
            print(f"✅ Snapshot created: {snapshot_id}")
            
            # List snapshots
            snapshots = core.system_snapshot.get_snapshots()
            print(f"✅ Snapshots listed: {len(snapshots)} total")
            
            # Clean up test snapshot
            core.system_snapshot.delete_snapshot(snapshot_id)
            print("✅ Test snapshot cleaned up")
        else:
            print("❌ Failed to create snapshot")
        
    except Exception as e:
        print(f"❌ System snapshots test failed: {e}")


def test_system_optimization(core):
    """Test system optimization features"""
    print("\n⚡ Testing System Optimization")
    
    try:
        # Get system recommendations
        recommendations = core.get_system_recommendations()
        print(f"✅ Recommendations retrieved: {len(recommendations)} items")
        
        # Perform optimization (safe operations only)
        result = core.perform_system_optimization()
        print(f"✅ Optimization completed: {result['success']}")
        
        if result['optimizations']:
            for opt in result['optimizations']:
                print(f"  • {opt['type']}: completed")
        
    except Exception as e:
        print(f"❌ System optimization test failed: {e}")


def test_comprehensive_status(core):
    """Test comprehensive status reporting"""
    print("\n📋 Testing Comprehensive Status")
    
    try:
        status = core.get_system_status()
        
        print(f"✅ System status retrieved:")
        print(f"  • Platform: {status.get('platform', 'Unknown')}")
        print(f"  • Mode: {status.get('mode', 'Unknown')}")
        
        if 'monitoring' in status:
            print(f"  • Monitoring: Active")
        
        if 'disk_health' in status:
            disk = status['disk_health']
            print(f"  • Disks: {disk['total_disks']} total, {disk['healthy_disks']} healthy")
        
        if 'database' in status:
            print(f"  • Database: {status['database'].get('file_size_mb', 0):.1f} MB")
        
        if 'agent' in status:
            print(f"  • Agent: {status['agent']['metrics']['total_executions']} executions")
        
    except Exception as e:
        print(f"❌ Comprehensive status test failed: {e}")


def main():
    """Run all SVP tests"""
    print("🎯 Cybex SVP Feature Testing")
    print("=" * 50)
    
    # Initialize components
    core, command_executor, ollama_interface = test_svp_initialization()
    
    if not core:
        print("❌ Cannot proceed without core initialization")
        return
    
    # Start a test session
    core.start_session("test_user")
    
    try:
        # Run all tests
        test_system_monitoring(core)
        test_disk_management(core)
        test_database_persistence(core)
        test_agent_planning(core)
        test_system_snapshots(core)
        test_system_optimization(core)
        test_comprehensive_status(core)
        
        print("\n🎉 SVP Feature Testing Completed!")
        print("All major SVP components have been tested successfully.")
        
    except Exception as e:
        print(f"\n❌ Testing failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # End test session
        core.end_session()


if __name__ == "__main__":
    main()
