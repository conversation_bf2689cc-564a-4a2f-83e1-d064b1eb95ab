#!/usr/bin/env python3
"""
Test script for performance analyzer
"""

import sys
import os
sys.path.insert(0, '.')

try:
    print("🔧 Testing Performance Analyzer...")
    
    # Test imports
    from cybex.modules.model_performance_analyzer import ModelPerformanceAnalyzer
    from cybex.core.cybex_core import LogManager, SystemConfig
    
    print("✅ Imports successful")
    
    # Test initialization
    config = SystemConfig()
    log_manager = LogManager(config)
    analyzer = ModelPerformanceAnalyzer(log_manager)
    
    print("✅ Performance analyzer initialized")
    
    # Test basic functionality
    timeout = analyzer.get_timeout_for_model("gemma3:4b")
    print(f"✅ Default timeout for gemma3:4b: {timeout}s")
    
    # Test recording a request
    analyzer.record_request(
        model_name="test_model",
        prompt_length=50,
        response_time=2.5,
        success=True
    )
    
    print("✅ Request recorded successfully")
    
    # Test getting stats
    stats = analyzer.get_model_stats("test_model")
    print(f"✅ Model stats: {stats}")
    
    print("\n🎉 Performance Analyzer test completed successfully!")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
