#!/usr/bin/env python3
"""
Test Timeout Fix
Test rapido per verificare che il timeout dinamico funzioni
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_timeout_fix():
    """Test that timeout is calculated dynamically"""
    print("🔧 Testing Timeout Fix")
    print("=" * 30)
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.ollama_interface import OllamaInterface
        
        # Initialize components
        core = CybexCore()
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Get current model and timeout
        current_model = ollama_interface.model
        print(f"Current model: {current_model}")
        
        # Test timeout calculation
        base_timeout = 180
        calculated_timeout = ollama_interface._calculate_model_timeout(base_timeout)
        
        print(f"Base timeout: {base_timeout}s")
        print(f"Calculated timeout: {calculated_timeout}s")
        
        # Test model update
        print(f"\nTesting model update...")
        
        # Update to a different model
        test_model = "devstral:24b"
        ollama_interface.update_model(test_model)
        
        new_timeout = ollama_interface._calculate_model_timeout(base_timeout)
        print(f"New model: {ollama_interface.model}")
        print(f"New timeout: {new_timeout}s ({new_timeout//60} minutes)")
        
        # Verify timeout is different for large model
        if new_timeout > calculated_timeout:
            print("✅ Timeout correctly increased for large model")
        else:
            print("⚠️  Timeout not increased as expected")
        
        # Test with medium model
        ollama_interface.update_model("gemma3:4b")
        medium_timeout = ollama_interface._calculate_model_timeout(base_timeout)
        print(f"Medium model timeout: {medium_timeout}s ({medium_timeout//60} minutes)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_request_timeout():
    """Test that API request uses dynamic timeout"""
    print(f"\n🌐 Testing API Request Timeout")
    print("=" * 40)
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.ollama_interface import OllamaInterface
        
        # Initialize components
        core = CybexCore()
        ollama_interface = OllamaInterface(
            core.config_manager,
            core.log_manager
        )
        
        # Test with current model (gemma3:4b)
        current_model = ollama_interface.model
        print(f"Testing with model: {current_model}")
        
        # Calculate expected timeout
        base_timeout = ollama_interface.ollama_config.get('timeout', 180)
        expected_timeout = ollama_interface._calculate_model_timeout(base_timeout)
        
        print(f"Expected timeout: {expected_timeout}s ({expected_timeout//60} minutes)")
        
        # Prepare a simple test message
        messages = [{"role": "user", "content": "Say 'Hello' and nothing else."}]
        
        print("Testing API request with dynamic timeout...")
        print("(This will attempt to connect to Ollama)")
        
        # This should use the dynamic timeout
        response = ollama_interface._make_api_request(messages)
        
        if response.success:
            print(f"✅ API request successful: '{response.content.strip()}'")
        else:
            print(f"⚠️  API request failed: {response.error}")
            
            # Check if it's a timeout with the correct duration
            if "timed out after" in response.error:
                if str(expected_timeout) in response.error:
                    print("✅ Timeout error shows correct dynamic timeout")
                else:
                    print("❌ Timeout error shows wrong timeout value")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def show_timeout_summary():
    """Show timeout summary for all models"""
    print(f"\n📊 Timeout Summary for Your Models")
    print("=" * 45)
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.ollama_interface import OllamaInterface
        
        core = CybexCore()
        ollama_interface = OllamaInterface(core.config_manager, core.log_manager)
        
        # Your models with expected timeouts
        your_models = [
            "devstral:24b",
            "gemma3:27b", 
            "gemma3:latest",
            "gemma3:4b",
            "AGtech:latest",
            "SocialAI:latest",
            "deepseek-ideas:latest",
            "deepseek-r1:1.5b",
            "deepseek-r1:latest",
            "deepseek-r1:8b"
        ]
        
        base_timeout = 180
        
        print("Model                    Timeout    Minutes")
        print("-" * 45)
        
        for model in your_models:
            ollama_interface.model = model
            timeout = ollama_interface._calculate_model_timeout(base_timeout)
            minutes = timeout // 60
            print(f"{model:<20} {timeout:>7}s  {minutes:>7}m")
        
        print("-" * 45)
        print("✅ All models now have appropriate timeouts!")
        
    except Exception as e:
        print(f"❌ Error: {e}")


def main():
    """Run timeout fix tests"""
    print("🎯 Cybex - Timeout Fix Test")
    print("=" * 40)
    
    tests = [
        ("Timeout Calculation Fix", test_timeout_fix),
        ("API Request Timeout", test_api_request_timeout)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*10} {test_name} {'='*10}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    show_timeout_summary()
    
    print(f"\n{'='*40}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= 1:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Timeout fix is working!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Your models should now work without timeout issues{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Try again:{SunriseColors.RESET}")
        print(f"  python main_enterprise.py")
        print(f"  Then type: ciao chi sei?")
        print(f"  Should now wait up to 4.5 minutes for gemma3:4b")
    else:
        print(f"⚠️  Tests failed. Check the errors above.")
    
    print("=" * 40)


if __name__ == "__main__":
    main()
