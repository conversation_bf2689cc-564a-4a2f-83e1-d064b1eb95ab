<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CYBEX Enterprise - AI Assistant</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Monaco Editor (VSCode Editor) -->
    <script src="https://unpkg.com/monaco-editor@0.45.0/min/vs/loader.js"></script>
    
    <!-- Framer Motion for animations -->
    <script src="https://unpkg.com/framer-motion@10.16.4/dist/framer-motion.js"></script>
    
    <!-- Custom CSS -->
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        :root {
            --cybex-primary: #00f5ff;
            --cybex-secondary: #ff0080;
            --cybex-accent: #00ff88;
            --cybex-dark: #0a0a0f;
            --cybex-darker: #050508;
            --cybex-light: #1a1a2e;
            --cybex-border: #2a2a3e;
            --cybex-text: #e0e0e0;
            --cybex-text-dim: #a0a0a0;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, var(--cybex-darker) 0%, var(--cybex-dark) 100%);
            color: var(--cybex-text);
            overflow: hidden;
            height: 100vh;
        }
        
        .cybex-glow {
            box-shadow: 0 0 20px rgba(0, 245, 255, 0.3);
        }
        
        .cybex-border {
            border: 1px solid var(--cybex-border);
            background: rgba(26, 26, 46, 0.8);
            backdrop-filter: blur(10px);
        }
        
        .cybex-button {
            background: linear-gradient(135deg, var(--cybex-primary) 0%, var(--cybex-secondary) 100%);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 6px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .cybex-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 245, 255, 0.4);
        }
        
        .cybex-input {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid var(--cybex-border);
            color: var(--cybex-text);
            padding: 12px 16px;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .cybex-input:focus {
            outline: none;
            border-color: var(--cybex-primary);
            box-shadow: 0 0 0 3px rgba(0, 245, 255, 0.1);
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-online { background: var(--cybex-accent); }
        .status-offline { background: var(--cybex-secondary); }
        .status-processing { 
            background: var(--cybex-primary);
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .chat-message {
            margin-bottom: 16px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .chat-message.user {
            background: linear-gradient(135deg, var(--cybex-primary) 0%, var(--cybex-secondary) 100%);
            margin-left: auto;
            color: white;
        }
        
        .chat-message.ai {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid var(--cybex-border);
            margin-right: auto;
        }
        
        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            background: rgba(26, 26, 46, 0.8);
            border-radius: 12px;
            margin-bottom: 16px;
            max-width: 200px;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            background: var(--cybex-primary);
            border-radius: 50%;
            animation: typing 1.4s infinite;
        }
        
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }
        
        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-10px); }
        }
        
        .sidebar {
            background: rgba(10, 10, 15, 0.95);
            border-right: 1px solid var(--cybex-border);
            backdrop-filter: blur(15px);
        }
        
        .main-content {
            background: rgba(5, 5, 8, 0.8);
            backdrop-filter: blur(10px);
        }
        
        .tool-card {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid var(--cybex-border);
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .tool-card:hover {
            background: rgba(26, 26, 46, 0.8);
            border-color: var(--cybex-primary);
            transform: translateX(4px);
        }
        
        .monaco-editor-container {
            height: 300px;
            border: 1px solid var(--cybex-border);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .performance-chart {
            background: rgba(26, 26, 46, 0.6);
            border: 1px solid var(--cybex-border);
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .model-selector {
            background: rgba(26, 26, 46, 0.9);
            border: 1px solid var(--cybex-border);
            color: var(--cybex-text);
            padding: 8px 12px;
            border-radius: 6px;
            cursor: pointer;
        }
        
        .model-selector:focus {
            outline: none;
            border-color: var(--cybex-primary);
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: var(--cybex-dark);
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: var(--cybex-border);
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: var(--cybex-primary);
        }
    </style>
</head>
<body>
    <div id="app" class="h-screen flex">
        <!-- Sidebar -->
        <div class="sidebar w-80 flex flex-col">
            <!-- Header -->
            <div class="p-6 border-b border-gray-700">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-gradient-to-r from-cyan-400 to-pink-500 rounded-lg mr-3 flex items-center justify-center">
                        <span class="text-white font-bold text-sm">CX</span>
                    </div>
                    <div>
                        <h1 class="text-lg font-semibold text-white">CYBEX Enterprise</h1>
                        <p class="text-xs text-gray-400">AI Assistant v2.0</p>
                    </div>
                </div>
                
                <!-- Status -->
                <div class="flex items-center text-sm">
                    <span class="status-indicator status-offline" id="status-indicator"></span>
                    <span id="status-text">Connecting...</span>
                </div>
            </div>
            
            <!-- Model Selector -->
            <div class="p-4 border-b border-gray-700">
                <label class="block text-sm font-medium text-gray-300 mb-2">AI Model</label>
                <select id="model-selector" class="model-selector w-full">
                    <option value="">Loading models...</option>
                </select>
            </div>
            
            <!-- Tools -->
            <div class="flex-1 p-4 overflow-y-auto scrollbar-thin">
                <h3 class="text-sm font-medium text-gray-300 mb-3">Enterprise Tools</h3>
                <div id="tools-list">
                    <!-- Tools will be loaded here -->
                </div>
            </div>
            
            <!-- Performance Monitor -->
            <div class="p-4 border-t border-gray-700">
                <h3 class="text-sm font-medium text-gray-300 mb-2">Performance</h3>
                <div class="performance-chart">
                    <div class="text-xs text-gray-400 mb-1">Response Time</div>
                    <div class="text-lg font-semibold text-cyan-400" id="response-time">--</div>
                    <div class="text-xs text-gray-400 mt-1">Success Rate: <span id="success-rate" class="text-green-400">--</span></div>
                </div>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="main-content flex-1 flex flex-col">
            <!-- Top Bar -->
            <div class="p-4 border-b border-gray-700 flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <h2 class="text-lg font-semibold text-white">AI Chat Interface</h2>
                    <div class="text-sm text-gray-400" id="current-model">Model: Loading...</div>
                </div>
                
                <div class="flex items-center space-x-2">
                    <button class="cybex-button text-sm" onclick="clearChat()">Clear Chat</button>
                    <button class="cybex-button text-sm" onclick="toggleCodeEditor()">Code Editor</button>
                </div>
            </div>
            
            <!-- Chat Area -->
            <div class="flex-1 flex flex-col">
                <div id="chat-container" class="flex-1 p-6 overflow-y-auto scrollbar-thin">
                    <div id="chat-messages">
                        <div class="chat-message ai">
                            <div class="text-sm text-gray-400 mb-1">CYBEX Enterprise</div>
                            <div>Welcome to CYBEX Enterprise! I'm your advanced AI assistant with 52+ enterprise tools. How can I help you today?</div>
                        </div>
                    </div>
                </div>
                
                <!-- Input Area -->
                <div class="p-4 border-t border-gray-700">
                    <div class="flex space-x-3">
                        <input 
                            type="text" 
                            id="chat-input" 
                            class="cybex-input flex-1" 
                            placeholder="Ask me anything or use enterprise tools..."
                            onkeypress="handleKeyPress(event)"
                        >
                        <button class="cybex-button px-6" onclick="sendMessage()">Send</button>
                    </div>
                </div>
            </div>
            
            <!-- Code Editor (Hidden by default) -->
            <div id="code-editor-panel" class="hidden border-t border-gray-700" style="height: 300px;">
                <div class="p-3 bg-gray-800 border-b border-gray-700 flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-300">Code Editor</span>
                    <button class="text-gray-400 hover:text-white" onclick="toggleCodeEditor()">×</button>
                </div>
                <div id="monaco-editor" class="monaco-editor-container"></div>
            </div>
        </div>
    </div>
    
    <!-- JavaScript -->
    <script src="app.js"></script>
</body>
</html>
