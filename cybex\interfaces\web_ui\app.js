/**
 * CYBEX Enterprise Web Interface
 * Modern JavaScript for futuristic AI assistant
 */

class CybexApp {
    constructor() {
        this.ws = null;
        this.isConnected = false;
        this.currentModel = null;
        this.tools = [];
        this.monacoEditor = null;
        this.isTyping = false;
        
        this.init();
    }
    
    async init() {
        console.log('🚀 Initializing CYBEX Enterprise...');
        
        // Initialize WebSocket connection
        this.connectWebSocket();
        
        // Load initial data
        await this.loadModels();
        await this.loadTools();
        
        // Setup Monaco Editor
        this.setupMonacoEditor();
        
        // Setup event listeners
        this.setupEventListeners();
        
        console.log('✅ CYBEX Enterprise initialized');
    }
    
    connectWebSocket() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const wsUrl = `${protocol}//${window.location.host}/ws`;
        
        this.ws = new WebSocket(wsUrl);
        
        this.ws.onopen = () => {
            console.log('🔗 WebSocket connected');
            this.isConnected = true;
            this.updateStatus('online', 'Connected');
        };
        
        this.ws.onmessage = (event) => {
            const message = JSON.parse(event.data);
            this.handleWebSocketMessage(message);
        };
        
        this.ws.onclose = () => {
            console.log('❌ WebSocket disconnected');
            this.isConnected = false;
            this.updateStatus('offline', 'Disconnected');
            
            // Attempt to reconnect after 3 seconds
            setTimeout(() => this.connectWebSocket(), 3000);
        };
        
        this.ws.onerror = (error) => {
            console.error('WebSocket error:', error);
            this.updateStatus('offline', 'Connection Error');
        };
    }
    
    handleWebSocketMessage(message) {
        switch (message.type) {
            case 'connected':
                console.log('✅ Connected to CYBEX Enterprise');
                break;
                
            case 'ai_response':
                this.handleAIResponse(message);
                break;
                
            case 'typing':
                this.showTypingIndicator();
                break;
                
            case 'tool_result':
                this.handleToolResult(message);
                break;
                
            case 'model_changed':
                this.currentModel = message.model;
                this.updateCurrentModelDisplay();
                break;
                
            case 'error':
                this.addMessage('system', `Error: ${message.message}`, 'error');
                this.hideTypingIndicator();
                break;
                
            default:
                console.log('Unknown message type:', message.type);
        }
    }
    
    async loadModels() {
        try {
            const response = await fetch('/api/models');
            const data = await response.json();
            
            if (data.success && data.models) {
                this.populateModelSelector(data.models);
            }
        } catch (error) {
            console.error('Failed to load models:', error);
        }
    }
    
    async loadTools() {
        try {
            const response = await fetch('/api/tools');
            const data = await response.json();
            
            if (data.tools) {
                this.tools = data.tools;
                this.populateToolsList(data.tools);
            }
        } catch (error) {
            console.error('Failed to load tools:', error);
        }
    }
    
    populateModelSelector(models) {
        const selector = document.getElementById('model-selector');
        selector.innerHTML = '';
        
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.name;
            option.textContent = `${model.name} (${(model.size / (1024**3)).toFixed(1)}GB)`;
            selector.appendChild(option);
        });
        
        // Set current model
        if (models.length > 0) {
            this.currentModel = models[0].name;
            selector.value = this.currentModel;
            this.updateCurrentModelDisplay();
        }
    }
    
    populateToolsList(tools) {
        const toolsList = document.getElementById('tools-list');
        toolsList.innerHTML = '';
        
        // Group tools by category
        const categories = {
            'Core': tools.filter(t => t.includes('system') || t.includes('process') || t.includes('disk')),
            'Enterprise': tools.filter(t => t.includes('security') || t.includes('performance') || t.includes('enterprise')),
            'Web': tools.filter(t => t.includes('web') || t.includes('fetch') || t.includes('browse')),
            'Database': tools.filter(t => t.includes('database') || t.includes('query') || t.includes('backup')),
            'Development': tools.filter(t => t.includes('git') || t.includes('docker') || t.includes('test')),
            'Other': tools.filter(t => !['Core', 'Enterprise', 'Web', 'Database', 'Development'].some(cat => 
                toolsList.querySelector(`[data-category="${cat}"]`)?.dataset.tools?.includes(t)
            ))
        };
        
        Object.entries(categories).forEach(([category, categoryTools]) => {
            if (categoryTools.length === 0) return;
            
            // Category header
            const header = document.createElement('div');
            header.className = 'text-xs font-medium text-gray-400 mb-2 mt-4 first:mt-0';
            header.textContent = category;
            toolsList.appendChild(header);
            
            // Tools in category
            categoryTools.forEach(tool => {
                const toolCard = document.createElement('div');
                toolCard.className = 'tool-card';
                toolCard.innerHTML = `
                    <div class="text-sm font-medium text-white">${tool.replace(/_/g, ' ')}</div>
                    <div class="text-xs text-gray-400 mt-1">Click to execute</div>
                `;
                toolCard.onclick = () => this.executeTool(tool);
                toolsList.appendChild(toolCard);
            });
        });
    }
    
    setupMonacoEditor() {
        require.config({ paths: { vs: 'https://unpkg.com/monaco-editor@0.45.0/min/vs' } });
        
        require(['vs/editor/editor.main'], () => {
            this.monacoEditor = monaco.editor.create(document.getElementById('monaco-editor'), {
                value: '// CYBEX Enterprise Code Editor\n// Write your code here...\n\nfunction example() {\n    console.log("Hello from CYBEX!");\n}',
                language: 'javascript',
                theme: 'vs-dark',
                fontSize: 14,
                minimap: { enabled: false },
                scrollBeyondLastLine: false,
                automaticLayout: true
            });
        });
    }
    
    setupEventListeners() {
        // Model selector change
        document.getElementById('model-selector').addEventListener('change', (e) => {
            this.switchModel(e.target.value);
        });
        
        // Chat input focus
        document.getElementById('chat-input').focus();
    }
    
    updateStatus(status, text) {
        const indicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        
        indicator.className = `status-indicator status-${status}`;
        statusText.textContent = text;
    }
    
    updateCurrentModelDisplay() {
        const display = document.getElementById('current-model');
        display.textContent = `Model: ${this.currentModel || 'Unknown'}`;
    }
    
    sendMessage() {
        const input = document.getElementById('chat-input');
        const message = input.value.trim();
        
        if (!message || !this.isConnected) return;
        
        // Add user message to chat
        this.addMessage('user', message);
        
        // Send to WebSocket
        this.ws.send(JSON.stringify({
            type: 'chat',
            content: message
        }));
        
        // Clear input
        input.value = '';
    }
    
    addMessage(sender, content, type = 'normal') {
        const chatMessages = document.getElementById('chat-messages');
        const messageDiv = document.createElement('div');
        
        if (sender === 'user') {
            messageDiv.className = 'chat-message user';
            messageDiv.innerHTML = `<div>${this.escapeHtml(content)}</div>`;
        } else if (sender === 'ai') {
            messageDiv.className = 'chat-message ai';
            messageDiv.innerHTML = `
                <div class="text-sm text-gray-400 mb-1">CYBEX AI</div>
                <div>${this.formatAIResponse(content)}</div>
            `;
        } else {
            messageDiv.className = `chat-message ai ${type}`;
            messageDiv.innerHTML = `
                <div class="text-sm text-gray-400 mb-1">System</div>
                <div>${this.escapeHtml(content)}</div>
            `;
        }
        
        chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    handleAIResponse(message) {
        this.hideTypingIndicator();
        this.addMessage('ai', message.content);
        
        // Update performance metrics
        this.updatePerformanceMetrics(message);
    }
    
    handleToolResult(message) {
        const resultDiv = document.createElement('div');
        resultDiv.className = 'chat-message ai';
        
        const statusIcon = message.success ? '✅' : '❌';
        const statusColor = message.success ? 'text-green-400' : 'text-red-400';
        
        resultDiv.innerHTML = `
            <div class="text-sm text-gray-400 mb-1">Tool: ${message.tool}</div>
            <div class="mb-2">
                <span class="${statusColor}">${statusIcon} ${message.success ? 'Success' : 'Failed'}</span>
            </div>
            <div class="bg-gray-800 p-3 rounded text-sm font-mono">
                ${this.escapeHtml(message.output || message.error || 'No output')}
            </div>
        `;
        
        document.getElementById('chat-messages').appendChild(resultDiv);
        this.scrollToBottom();
    }
    
    showTypingIndicator() {
        if (this.isTyping) return;
        
        this.isTyping = true;
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typing-indicator';
        typingDiv.className = 'typing-indicator';
        typingDiv.innerHTML = `
            <div class="typing-dots">
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
            <span class="ml-2 text-sm text-gray-400">AI is thinking...</span>
        `;
        
        document.getElementById('chat-messages').appendChild(typingDiv);
        this.scrollToBottom();
    }
    
    hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.remove();
        }
        this.isTyping = false;
    }
    
    async switchModel(modelName) {
        if (!modelName || modelName === this.currentModel) return;
        
        try {
            const response = await fetch(`/api/models/${modelName}/switch`, {
                method: 'POST'
            });
            
            if (response.ok) {
                this.currentModel = modelName;
                this.updateCurrentModelDisplay();
                this.addMessage('system', `Switched to model: ${modelName}`, 'success');
            } else {
                this.addMessage('system', `Failed to switch to model: ${modelName}`, 'error');
            }
        } catch (error) {
            console.error('Model switch error:', error);
            this.addMessage('system', 'Model switch failed', 'error');
        }
    }
    
    executeTool(toolName) {
        if (!this.isConnected) {
            this.addMessage('system', 'Not connected to server', 'error');
            return;
        }
        
        // Send tool execution request
        this.ws.send(JSON.stringify({
            type: 'execute_tool',
            tool: toolName,
            params: {}
        }));
        
        this.addMessage('system', `Executing tool: ${toolName}...`, 'info');
    }
    
    updatePerformanceMetrics(message) {
        // This would be updated with real performance data
        document.getElementById('response-time').textContent = '2.3s';
        document.getElementById('success-rate').textContent = '98%';
    }
    
    formatAIResponse(content) {
        // Basic markdown-like formatting
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code class="bg-gray-800 px-1 rounded">$1</code>')
            .replace(/\n/g, '<br>');
    }
    
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
    
    scrollToBottom() {
        const container = document.getElementById('chat-container');
        container.scrollTop = container.scrollHeight;
    }
}

// Global functions
function handleKeyPress(event) {
    if (event.key === 'Enter') {
        app.sendMessage();
    }
}

function clearChat() {
    document.getElementById('chat-messages').innerHTML = `
        <div class="chat-message ai">
            <div class="text-sm text-gray-400 mb-1">CYBEX Enterprise</div>
            <div>Chat cleared. How can I help you?</div>
        </div>
    `;
}

function toggleCodeEditor() {
    const panel = document.getElementById('code-editor-panel');
    panel.classList.toggle('hidden');
    
    if (app.monacoEditor && !panel.classList.contains('hidden')) {
        setTimeout(() => app.monacoEditor.layout(), 100);
    }
}

// Initialize app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new CybexApp();
});
