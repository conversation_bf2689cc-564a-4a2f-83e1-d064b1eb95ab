#!/usr/bin/env python3
"""
Ollama Interface - Enterprise Integration
Professional Ollama API integration with model management
"""

import requests
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class OllamaResponse:
    """Ollama response wrapper"""
    success: bool
    content: str
    model: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class OllamaInterface:
    """Enterprise Ollama Interface"""
    
    def __init__(self, config_manager, log_manager):
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        
        # Load Ollama configuration
        self.ollama_config = config_manager.get_section('ollama')
        self.model = self.ollama_config.get('model', 'gemma3:4b')
        self.host = self.ollama_config.get('host', 'localhost')
        self.port = self.ollama_config.get('port', 11434)
        self.timeout = self.ollama_config.get('timeout', 180)
        
        self.base_url = f"http://{self.host}:{self.port}"
        
        # Test connection
        if self._test_connection():
            self.logger.info("Successfully connected to Ollama server")
        else:
            self.logger.warning("Could not connect to Ollama server")
    
    def _test_connection(self) -> bool:
        """Test Ollama server connection"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get list of all available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                models = []
                
                for model in data.get('models', []):
                    models.append({
                        'name': model.get('name', ''),
                        'size': model.get('size', 0),
                        'modified_at': model.get('modified_at', ''),
                        'digest': model.get('digest', ''),
                        'size_gb': round(model.get('size', 0) / (1024**3), 1)
                    })
                
                return {
                    'success': True,
                    'models': models,
                    'count': len(models),
                    'current_model': self.model
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to get models: {response.status_code}'
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> OllamaResponse:
        """Generate response using Ollama"""
        try:
            # Prepare messages
            messages = [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
            
            # Add context if provided
            if context:
                system_msg = f"Context: {json.dumps(context, indent=2)}"
                messages.insert(0, {
                    "role": "system", 
                    "content": system_msg
                })
            
            # Make API request
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data.get('message', {}).get('content', '')
                
                return OllamaResponse(
                    success=True,
                    content=content,
                    model=self.model,
                    metadata=data
                )
            else:
                error_msg = f"API request failed with status {response.status_code}"
                if response.text:
                    error_msg += f": {response.text}"
                
                return OllamaResponse(
                    success=False,
                    content="",
                    model=self.model,
                    error=error_msg
                )
                
        except requests.exceptions.Timeout:
            return OllamaResponse(
                success=False,
                content="",
                model=self.model,
                error=f"Request timed out after {self.timeout} seconds"
            )
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return OllamaResponse(
                success=False,
                content="",
                model=self.model,
                error=str(e)
            )
    
    def switch_model(self, new_model: str) -> bool:
        """Switch to a different model"""
        try:
            # Test if model is available
            available = self.get_available_models()
            if available['success']:
                model_names = [m['name'] for m in available['models']]
                if new_model in model_names:
                    self.model = new_model
                    self.logger.info(f"Switched to model: {new_model}")
                    return True
                else:
                    self.logger.error(f"Model {new_model} not available")
                    return False
            else:
                self.logger.error("Could not get available models")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to switch model: {e}")
            return False
    
    def is_server_available(self) -> bool:
        """Check if Ollama server is available"""
        return self._test_connection()
