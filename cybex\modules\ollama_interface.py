#!/usr/bin/env python3
"""
Ollama Interface - Enterprise Integration
Professional Ollama API integration with model management
"""

import requests
import json
import time
import os
from pathlib import Path
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class OllamaResponse:
    """Ollama response wrapper"""
    success: bool
    content: str
    model: str
    error: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

class OllamaInterface:
    """Enterprise Ollama Interface"""
    
    def __init__(self, config_manager, log_manager):
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)

        # Load Ollama configuration
        self.ollama_config = config_manager.get_section('ollama')
        self.model = self.ollama_config.get('model', 'gemma3:4b')
        self.host = self.ollama_config.get('host', 'localhost')
        self.port = self.ollama_config.get('port', 11434)
        self.timeout = self.ollama_config.get('timeout', 180)

        self.base_url = f"http://{self.host}:{self.port}"

        # Load system prompt
        self.system_prompt = self._load_system_prompt()

        # Test connection
        if self._test_connection():
            self.logger.info("Successfully connected to Ollama server")
        else:
            self.logger.warning("Could not connect to Ollama server")
    
    def _load_system_prompt(self) -> str:
        """Load system prompt from Prompt directory"""
        try:
            # Try different possible locations for the prompt file
            prompt_paths = [
                Path("Prompt/prompt_agent.txt"),
                Path("../Prompt/prompt_agent.txt"),
                Path("../../Prompt/prompt_agent.txt"),
                Path(__file__).parent.parent.parent / "Prompt" / "prompt_agent.txt"
            ]

            for prompt_path in prompt_paths:
                if prompt_path.exists():
                    with open(prompt_path, 'r', encoding='utf-8') as f:
                        content = f.read().strip()
                        self.logger.info(f"System prompt loaded from: {prompt_path}")
                        return content

            # Fallback system prompt if file not found
            fallback_prompt = """You are CYBEX, an advanced AI coding assistant powered by Gemma3, operating in AGtechdesigne enterprise environment.

You are pair programming with a USER to solve their coding tasks. You have access to enterprise tools and can perform complex operations.

Key capabilities:
- 52 Enterprise Tools available
- Web browsing and analysis
- Excel automation and analysis
- Database operations
- Development tools (Git, Docker, testing)
- Security auditing and compliance
- System administration
- File management operations

Communication guidelines:
- Always respond in Italian when the user writes in Italian
- Be concise and professional
- Use enterprise-level terminology
- Provide actionable solutions
- Leverage available tools when appropriate

Your main goal is to follow the USER's instructions and provide enterprise-level assistance using the available tool suite."""

            self.logger.warning("System prompt file not found, using fallback prompt")
            return fallback_prompt

        except Exception as e:
            self.logger.error(f"Error loading system prompt: {e}")
            return "You are CYBEX, an advanced AI assistant. Respond professionally and helpfully."

    def _test_connection(self) -> bool:
        """Test Ollama server connection"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get list of all available models"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                models = []
                
                for model in data.get('models', []):
                    models.append({
                        'name': model.get('name', ''),
                        'size': model.get('size', 0),
                        'modified_at': model.get('modified_at', ''),
                        'digest': model.get('digest', ''),
                        'size_gb': round(model.get('size', 0) / (1024**3), 1)
                    })
                
                return {
                    'success': True,
                    'models': models,
                    'count': len(models),
                    'current_model': self.model
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to get models: {response.status_code}'
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def generate_response(self, prompt: str, context: Dict[str, Any] = None) -> OllamaResponse:
        """Generate response using Ollama with system prompt"""
        try:
            # Prepare messages with system prompt
            messages = []

            # Add system prompt first
            if self.system_prompt:
                messages.append({
                    "role": "system",
                    "content": self.system_prompt
                })

            # Add context if provided
            if context:
                context_msg = f"Additional Context: {json.dumps(context, indent=2)}"
                messages.append({
                    "role": "system",
                    "content": context_msg
                })

            # Add user message
            messages.append({
                "role": "user",
                "content": prompt
            })

            # Make API request
            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False
            }
            
            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data.get('message', {}).get('content', '')
                
                return OllamaResponse(
                    success=True,
                    content=content,
                    model=self.model,
                    metadata=data
                )
            else:
                error_msg = f"API request failed with status {response.status_code}"
                if response.text:
                    error_msg += f": {response.text}"
                
                return OllamaResponse(
                    success=False,
                    content="",
                    model=self.model,
                    error=error_msg
                )
                
        except requests.exceptions.Timeout:
            return OllamaResponse(
                success=False,
                content="",
                model=self.model,
                error=f"Request timed out after {self.timeout} seconds"
            )
            
        except Exception as e:
            self.logger.error(f"Error generating response: {e}")
            return OllamaResponse(
                success=False,
                content="",
                model=self.model,
                error=str(e)
            )
    
    def switch_model(self, new_model: str) -> bool:
        """Switch to a different model"""
        try:
            # Test if model is available
            available = self.get_available_models()
            if available['success']:
                model_names = [m['name'] for m in available['models']]
                if new_model in model_names:
                    self.model = new_model
                    self.logger.info(f"Switched to model: {new_model}")
                    return True
                else:
                    self.logger.error(f"Model {new_model} not available")
                    return False
            else:
                self.logger.error("Could not get available models")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to switch model: {e}")
            return False

    def reload_system_prompt(self) -> bool:
        """Reload system prompt from file"""
        try:
            old_prompt = self.system_prompt
            self.system_prompt = self._load_system_prompt()

            if self.system_prompt != old_prompt:
                self.logger.info("System prompt reloaded successfully")
                return True
            else:
                self.logger.info("System prompt unchanged")
                return False

        except Exception as e:
            self.logger.error(f"Failed to reload system prompt: {e}")
            return False

    def get_system_prompt(self) -> str:
        """Get current system prompt"""
        return self.system_prompt

    def set_system_prompt(self, prompt: str) -> bool:
        """Set custom system prompt"""
        try:
            self.system_prompt = prompt
            self.logger.info("Custom system prompt set")
            return True
        except Exception as e:
            self.logger.error(f"Failed to set system prompt: {e}")
            return False

    def is_server_available(self) -> bool:
        """Check if Ollama server is available"""
        return self._test_connection()
