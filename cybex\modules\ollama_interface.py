"""
Ollama Interface Module
Handles communication with Ollama API for AI model interactions
"""

import json
import requests
import time
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from pathlib import Path
from .enhanced_ai_integration import EnhancedAIIntegration
from .advanced_context_manager import AdvancedContextManager


@dataclass
class OllamaResponse:
    """Response from Ollama API"""
    content: str
    success: bool
    error: Optional[str] = None
    metadata: Optional[Dict] = None


class OllamaInterface:
    """
    Interface for communicating with Ollama API
    """
    
    def __init__(self, config_manager, log_manager):
        """Initialize Ollama interface"""
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        
        # Load Ollama configuration with extended timeouts for large models
        self.ollama_config = config_manager.get_section('ollama')
        self.model = self.ollama_config.get('model', 'gemma3:4b')
        self.host = self.ollama_config.get('host', 'localhost')
        self.port = self.ollama_config.get('port', 11434)

        # Extended timeout settings for large models (devstral:24b, gemma3:27b, etc.)
        base_timeout = self.ollama_config.get('timeout', 180)  # Increased from 30 to 180 seconds
        self.timeout = self._calculate_model_timeout(base_timeout)
        self.max_tokens = self.ollama_config.get('max_tokens', 4000)  # Increased token limit
        
        # Build base URL
        self.base_url = f"http://{self.host}:{self.port}"
        
        # System prompt for Cybex
        self.system_prompt = self._build_system_prompt()

        # Natural language processing patterns
        self.nl_patterns = self._init_nl_patterns()
        
        # Conversation history
        self.conversation_history: List[Dict] = []
        
        # Initialize enhanced AI integration
        try:
            self.ai_integration = EnhancedAIIntegration()
            self.logger.info("Enhanced AI integration initialized")
        except Exception as e:
            self.logger.warning(f"Could not initialize enhanced AI integration: {e}")
            self.ai_integration = None

        # Initialize advanced context manager
        try:
            self.context_manager = AdvancedContextManager(self.log_manager)
            self.logger.info("Advanced context manager initialized")
        except Exception as e:
            self.logger.warning(f"Could not initialize advanced context manager: {e}")
            self.context_manager = None

        # Test connection on initialization
        self._test_connection()

    def _calculate_model_timeout(self, base_timeout: int) -> int:
        """Calculate timeout based on model size and complexity"""
        try:
            model_name = self.model.lower()

            # Timeout multipliers based on model size
            if '27b' in model_name or '24b' in model_name:
                # Very large models (gemma3:27b, devstral:24b)
                return base_timeout * 3  # 540 seconds (9 minutes)
            elif '8b' in model_name or '7b' in model_name:
                # Large models (deepseek-r1:8b, gemma2:7b)
                return base_timeout * 2  # 360 seconds (6 minutes)
            elif '4b' in model_name or '1.5b' in model_name:
                # Medium models (gemma3:4b, deepseek-r1:1.5b)
                return base_timeout * 1.5  # 270 seconds (4.5 minutes)
            elif 'latest' in model_name:
                # Unknown size, assume large
                return base_timeout * 2  # 360 seconds
            else:
                # Default timeout for unknown models
                return base_timeout  # 180 seconds (3 minutes)

        except Exception as e:
            self.logger.warning(f"Could not calculate model timeout: {e}")
            return base_timeout

    def update_model(self, new_model: str):
        """Update the current model and recalculate timeout"""
        try:
            self.model = new_model
            base_timeout = self.ollama_config.get('timeout', 180)
            self.timeout = self._calculate_model_timeout(base_timeout)

            self.logger.info(f"Model updated to {new_model} with timeout {self.timeout}s")

        except Exception as e:
            self.logger.error(f"Failed to update model: {e}")

    def _init_nl_patterns(self) -> Dict[str, Dict]:
        """Initialize natural language processing patterns"""
        return {
            'memory_status': {
                'patterns': [
                    r'memoria|ram|memory',
                    r'situazione.*memoria',
                    r'stato.*ram',
                    r'quanta.*memoria',
                    r'utilizzo.*memoria'
                ],
                'action': 'get_memory_status',
                'description': 'Analisi stato memoria sistema'
            },
            'disk_status': {
                'patterns': [
                    r'disco|disk|hard.*disk|ssd',
                    r'spazio.*disco',
                    r'come.*sta.*disco',
                    r'situazione.*disco',
                    r'storage|archiviazione'
                ],
                'action': 'get_disk_status',
                'description': 'Analisi stato dischi'
            },
            'cpu_status': {
                'patterns': [
                    r'cpu|processore|processor',
                    r'utilizzo.*cpu',
                    r'carico.*sistema',
                    r'performance.*cpu'
                ],
                'action': 'get_cpu_status',
                'description': 'Analisi stato CPU'
            },
            'system_performance': {
                'patterns': [
                    r'sistema.*lento|lento.*sistema',
                    r'performance|prestazioni',
                    r'ottimizza|ottimizzazione',
                    r'velocizza|accelera',
                    r'sistema.*rallenta'
                ],
                'action': 'analyze_performance',
                'description': 'Analisi performance sistema'
            },
            'cleanup': {
                'patterns': [
                    r'pulisci|pulizia|clean',
                    r'file.*temporanei|temp.*file',
                    r'libera.*spazio',
                    r'rimuovi.*cache',
                    r'elimina.*file.*inutili'
                ],
                'action': 'cleanup_system',
                'description': 'Pulizia sistema'
            },
            'system_status': {
                'patterns': [
                    r'stato.*sistema|sistema.*stato',
                    r'situazione.*generale',
                    r'come.*sta.*sistema',
                    r'dashboard|panoramica',
                    r'report.*sistema'
                ],
                'action': 'get_system_overview',
                'description': 'Panoramica completa sistema'
            },
            'processes': {
                'patterns': [
                    r'processi|process|task',
                    r'applicazioni.*attive',
                    r'programmi.*in.*esecuzione',
                    r'cosa.*sta.*girando'
                ],
                'action': 'get_processes',
                'description': 'Analisi processi attivi'
            },
            'network_status': {
                'patterns': [
                    r'rete|network|connessione',
                    r'internet|wifi',
                    r'traffico.*rete',
                    r'velocità.*connessione'
                ],
                'action': 'get_network_status',
                'description': 'Analisi stato rete'
            }
        }
    
    def _build_system_prompt(self) -> str:
        """Build the system prompt for Cybex"""
        return """Sei **Cybex**, un agente AI operativo in locale. Hai il know-how di un esperto senior con oltre 40 anni di esperienza reale in sicurezza informatica, amministrazione sistemi, sviluppo software e gestione dischi.

Il tuo compito è assistere, automatizzare e ottimizzare un sistema operativo (Windows o Linux) agendo in base al contesto, in due modalità:

🗨️ Modalità Chat Classica: rispondi in modo didattico, preciso e sicuro. Ogni comando che altera lo stato del sistema richiede **conferma esplicita** dell'utente.

⚙️ Modalità Agente: ricevi un obiettivo, elabori un piano in più step e lo esegui **autonomamente**, chiedendo conferma solo per operazioni critiche (formattazioni, scritture disco, modifiche ai permessi, download da fonti esterne, modifiche ai servizi).

**CAPACITÀ AVANZATE:**
- Monitoraggio sistema real-time (CPU, RAM, Disk, Network)
- Gestione dischi con SMART monitoring
- Analisi performance e ottimizzazione
- Gestione snapshot e rollback
- Database integrato per persistenza dati

**INTERPRETAZIONE LINGUAGGIO NATURALE:**
Quando l'utente fa richieste in linguaggio naturale, devi:
1. **Analizzare** la richiesta e identificare l'azione richiesta
2. **Raccogliere** i dati necessari dal sistema
3. **Presentare** i risultati in modo chiaro e strutturato
4. **Suggerire** azioni di follow-up se appropriate

Esempi di richieste che devi gestire:
- "dammi la situazione memoria del computer" → Analizza RAM, swap, processi
- "come sta il disco?" → Controlla spazio, salute, frammentazione
- "il sistema è lento" → Analizza CPU, RAM, processi, bottleneck
- "pulisci i file temporanei" → Scansiona e rimuovi file temp
- "ottimizza il sistema" → Esegui pulizia, defrag, ottimizzazioni

**FORMATO RISPOSTA:**
- Usa emoji per categorizzare (📊 per statistiche, ⚠️ per warning, ✅ per OK)
- Presenta dati in tabelle o liste quando possibile
- Includi sempre raccomandazioni pratiche
- Spiega il significato dei valori tecnici

**Non eseguire mai azioni illegali o potenzialmente dannose. Se il contesto è ambiguo, chiedi chiarimenti.**

Il tuo obiettivo è massimizzare l'efficienza, anticipare problemi, e agire come un vero collega sysadmin di alto livello che comunica in modo chiaro e professionale."""
    
    def _test_connection(self) -> bool:
        """Test connection to Ollama server"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                self.logger.info("Successfully connected to Ollama server")
                return True
            else:
                self.logger.warning(f"Ollama server responded with status {response.status_code}")
                return False
        except Exception as e:
            self.logger.error(f"Failed to connect to Ollama server: {e}")
            return False
    
    def generate_response(self, user_input: str, context: Optional[Dict] = None) -> OllamaResponse:
        """
        Generate response from Ollama model with natural language processing
        """
        try:
            # Check if this is a natural language request that can be processed directly
            nl_processor = context.get('nl_processor') if context else None
            if nl_processor:
                nl_result = nl_processor.process_request(user_input, self.nl_patterns)

                if nl_result.get('success'):
                    # Return structured response from NL processor
                    return OllamaResponse(
                        content=nl_result['message'],
                        success=True,
                        metadata={
                            'action': nl_result.get('action'),
                            'data': nl_result.get('data'),
                            'recommendations': nl_result.get('recommendations', []),
                            'processed_locally': True,
                            'execution_time': 0.1
                        }
                    )

            # Prepare the prompt
            messages = self._prepare_messages(user_input, context)

            # Make API request
            start_time = time.time()
            response = self._make_api_request(messages)
            execution_time = time.time() - start_time

            if response.success:
                # Add to conversation history
                self._add_to_history(user_input, response.content)

                # Log successful interaction
                self.logger.info(f"Generated response in {execution_time:.2f}s")

                # Add metadata
                response.metadata = {
                    'execution_time': execution_time,
                    'model': self.model,
                    'tokens_estimated': len(response.content.split()),
                    'processed_locally': False
                }

            return response

        except Exception as e:
            self.logger.error(f"Failed to generate response: {e}")
            return OllamaResponse(
                content="",
                success=False,
                error=f"Failed to generate response: {e}"
            )
    
    def _prepare_messages(self, user_input: str, context: Optional[Dict] = None) -> List[Dict]:
        """Prepare messages for the API request"""
        messages = [
            {
                "role": "system",
                "content": self.system_prompt
            }
        ]
        
        # Add context if provided
        if context:
            context_msg = self._format_context(context)
            if context_msg:
                messages.append({
                    "role": "system",
                    "content": f"Contesto attuale: {context_msg}"
                })
        
        # Add recent conversation history (last 5 exchanges)
        recent_history = self.conversation_history[-10:]  # Last 5 exchanges (user + assistant)
        messages.extend(recent_history)
        
        # Add current user input
        messages.append({
            "role": "user",
            "content": user_input
        })
        
        return messages
    
    def _format_context(self, context: Dict) -> str:
        """Format context information for the model"""
        context_parts = []
        
        if 'system_info' in context:
            context_parts.append(f"Sistema: {context['system_info']}")
        
        if 'current_directory' in context:
            context_parts.append(f"Directory corrente: {context['current_directory']}")
        
        if 'last_command' in context:
            context_parts.append(f"Ultimo comando: {context['last_command']}")
        
        if 'mode' in context:
            context_parts.append(f"Modalità: {context['mode']}")
        
        return " | ".join(context_parts)
    
    def _make_api_request(self, messages: List[Dict]) -> OllamaResponse:
        """Make the actual API request to Ollama with extended timeout for large models"""
        try:
            # Calculate timeout dynamically based on current model
            base_timeout = self.ollama_config.get('timeout', 180)
            current_timeout = self._calculate_model_timeout(base_timeout)

            # Log timeout information for large models
            if current_timeout > 300:  # More than 5 minutes
                self.logger.info(f"Using extended timeout ({current_timeout}s) for large model: {self.model}")

            payload = {
                "model": self.model,
                "messages": messages,
                "stream": False,
                "options": {
                    "num_predict": self.max_tokens,
                    "temperature": 0.7,
                    "top_p": 0.9,
                    # Additional options for large models
                    "num_ctx": 4096,  # Context window
                    "repeat_penalty": 1.1
                }
            }

            response = requests.post(
                f"{self.base_url}/api/chat",
                json=payload,
                timeout=current_timeout  # Use dynamically calculated timeout
            )
            
            if response.status_code == 200:
                data = response.json()
                content = data.get('message', {}).get('content', '')
                
                return OllamaResponse(
                    content=content,
                    success=True,
                    metadata={
                        'model': data.get('model'),
                        'created_at': data.get('created_at'),
                        'done': data.get('done', True)
                    }
                )
            else:
                error_msg = f"API request failed with status {response.status_code}"
                try:
                    error_data = response.json()
                    error_msg += f": {error_data.get('error', 'Unknown error')}"
                except:
                    pass
                
                return OllamaResponse(
                    content="",
                    success=False,
                    error=error_msg
                )
                
        except requests.exceptions.Timeout:
            # Calculate current timeout for error message
            base_timeout = self.ollama_config.get('timeout', 180)
            current_timeout = self._calculate_model_timeout(base_timeout)

            # Provide helpful timeout message based on model size
            timeout_msg = f"Request timed out after {current_timeout} seconds"

            if '27b' in self.model.lower() or '24b' in self.model.lower():
                timeout_msg += f"\n💡 Large model ({self.model}) may need more time. Consider:"
                timeout_msg += f"\n   • Using a shorter prompt"
                timeout_msg += f"\n   • Switching to a smaller model"
                timeout_msg += f"\n   • Checking system resources"
            elif current_timeout < 300:
                timeout_msg += f"\n💡 Consider increasing timeout for model {self.model}"

            return OllamaResponse(
                content="",
                success=False,
                error=timeout_msg
            )
        except Exception as e:
            return OllamaResponse(
                content="",
                success=False,
                error=f"API request failed: {e}"
            )
    
    def _add_to_history(self, user_input: str, assistant_response: str) -> None:
        """Add exchange to conversation history"""
        self.conversation_history.extend([
            {"role": "user", "content": user_input},
            {"role": "assistant", "content": assistant_response}
        ])
        
        # Keep only last 20 messages (10 exchanges)
        if len(self.conversation_history) > 20:
            self.conversation_history = self.conversation_history[-20:]
    
    def clear_history(self) -> None:
        """Clear conversation history"""
        self.conversation_history.clear()
        self.logger.info("Conversation history cleared")
    
    def get_available_models(self) -> List[str]:
        """Get list of available models from Ollama"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=10)
            if response.status_code == 200:
                data = response.json()
                models = [model['name'] for model in data.get('models', [])]
                return models
            else:
                self.logger.error(f"Failed to get models: HTTP {response.status_code}")
                return []
        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return []
    
    def switch_model(self, model_name: str) -> bool:
        """Switch to a different model"""
        try:
            available_models = self.get_available_models()
            if model_name in available_models:
                self.model = model_name
                self.config_manager.set('ollama.model', model_name)
                self.logger.info(f"Switched to model: {model_name}")
                return True
            else:
                self.logger.error(f"Model {model_name} not available")
                return False
        except Exception as e:
            self.logger.error(f"Failed to switch model: {e}")
            return False
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get list of all available models in Ollama"""
        try:
            response = requests.get(
                f"{self.base_url}/api/tags",
                timeout=10
            )

            if response.status_code == 200:
                data = response.json()
                models = []

                for model in data.get('models', []):
                    models.append({
                        'name': model.get('name', ''),
                        'size': model.get('size', 0),
                        'modified_at': model.get('modified_at', ''),
                        'digest': model.get('digest', ''),
                        'size_gb': round(model.get('size', 0) / (1024**3), 1)
                    })

                return {
                    'success': True,
                    'models': models,
                    'count': len(models),
                    'current_model': self.model
                }
            else:
                return {
                    'success': False,
                    'error': f'Failed to get models: {response.status_code}'
                }

        except Exception as e:
            self.logger.error(f"Failed to get available models: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current model"""
        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": self.model},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            return {"error": str(e)}
    
    def is_server_available(self) -> bool:
        """Check if Ollama server is available"""
        return self._test_connection()

    def process_enhanced_command(self, user_input: str, context: Dict[str, Any] = None) -> OllamaResponse:
        """Process command using enhanced AI integration patterns"""
        if not self.ai_integration:
            return self.generate_response(user_input, context)

        try:
            # Parse natural language command
            parsed_command = self.ai_integration.parse_natural_language_command(user_input)

            # Enhance prompt with context
            enhanced_context = context or {}
            enhanced_context.update({
                "parsed_command": parsed_command,
                "user_input": user_input,
                "system_state": self._get_system_context()
            })

            # Build enhanced prompt
            base_prompt = self._build_enhanced_prompt(user_input, enhanced_context)

            # Generate response with enhanced prompt
            response = self.generate_response(base_prompt, enhanced_context)

            # Format response using enterprise patterns
            if response.success and self.ai_integration:
                formatted_response = self.ai_integration.format_enterprise_response(
                    {"success": True, "output": response.content},
                    enhanced_context
                )
                response.content = formatted_response

            return response

        except Exception as e:
            self.logger.error(f"Enhanced command processing failed: {e}")
            return self.generate_response(user_input, context)

    def _build_enhanced_prompt(self, user_input: str, context: Dict[str, Any]) -> str:
        """Build enhanced prompt using AGtechdesigne patterns"""
        if not self.ai_integration:
            return user_input

        # Load enhanced prompt template
        try:
            prompt_path = Path(__file__).parent.parent / "config" / "enhanced_ai_prompt.txt"
            if prompt_path.exists():
                with open(prompt_path, 'r', encoding='utf-8') as f:
                    base_prompt = f.read()
            else:
                base_prompt = self.system_prompt
        except Exception as e:
            self.logger.warning(f"Could not load enhanced prompt: {e}")
            base_prompt = self.system_prompt

        # Enhance with context
        enhanced_prompt = self.ai_integration.enhance_prompt(base_prompt, context)

        # Add user query
        enhanced_prompt += f"\n\n<user_query>\n{user_input}\n</user_query>"

        return enhanced_prompt

    def _get_system_context(self) -> Dict[str, Any]:
        """Get current system context for enhanced processing"""
        try:
            # Use advanced context manager if available
            if self.context_manager:
                context = self.context_manager.get_context()
                return {
                    "system_metrics": context.get('system', {}),
                    "security_status": context.get('security', {}),
                    "performance_indicators": context.get('performance', {}),
                    "user_context": context.get('user', {}),
                    "predictive_analysis": context.get('predictive', {}),
                    "session_info": context.get('session', {}),
                    "timestamp": time.time(),
                    "context_version": "advanced"
                }
            else:
                # Fallback to basic context
                import psutil
                return {
                    "cpu_percent": psutil.cpu_percent(),
                    "memory_percent": psutil.virtual_memory().percent,
                    "disk_usage": psutil.disk_usage('/').percent if hasattr(psutil.disk_usage('/'), 'percent') else 0,
                    "timestamp": time.time(),
                    "context_version": "basic"
                }
        except Exception as e:
            self.logger.warning(f"Could not get system context: {e}")
            return {"timestamp": time.time(), "error": str(e), "context_version": "error"}
