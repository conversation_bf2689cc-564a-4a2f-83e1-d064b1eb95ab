#!/usr/bin/env python3
"""
Ollama Monitor - Real-time Ollama Activity Monitoring
Visual feedback system for Ollama operations
"""

import tkinter as tk
from tkinter import ttk
import threading
import time
import requests
import json
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum

class OllamaStatus(Enum):
    """Ollama server status"""
    OFFLINE = "offline"
    ONLINE = "online"
    PROCESSING = "processing"
    ERROR = "error"
    IDLE = "idle"

@dataclass
class OllamaActivity:
    """Ollama activity tracking"""
    model: str
    prompt: str
    start_time: float
    status: OllamaStatus
    response_time: Optional[float] = None
    tokens_generated: int = 0
    error_message: Optional[str] = None

class OllamaMonitor:
    """Real-time Ollama monitoring system"""
    
    def __init__(self, parent_frame, log_manager):
        self.parent_frame = parent_frame
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        
        # Monitoring state
        self.current_activity: Optional[OllamaActivity] = None
        self.status = OllamaStatus.OFFLINE
        self.last_check = 0
        self.monitoring_active = False
        
        # Callbacks
        self.status_callbacks = []
        
        # Create monitor UI
        self.create_monitor_ui()
        
        # Start monitoring thread
        self.start_monitoring()
    
    def create_monitor_ui(self):
        """Create the monitoring UI components"""
        # Main monitor frame
        self.monitor_frame = ttk.LabelFrame(
            self.parent_frame, 
            text="🤖 Ollama Activity Monitor",
            padding="10"
        )
        self.monitor_frame.pack(fill="x", padx=5, pady=5)
        
        # Status indicator row
        status_frame = ttk.Frame(self.monitor_frame)
        status_frame.pack(fill="x", pady=(0, 5))
        
        # Status LED indicator
        self.status_canvas = tk.Canvas(status_frame, width=20, height=20, highlightthickness=0)
        self.status_canvas.pack(side="left", padx=(0, 10))
        
        # Status text
        self.status_label = ttk.Label(status_frame, text="🔍 Checking Ollama...", font=("Consolas", 10))
        self.status_label.pack(side="left")
        
        # Model info
        self.model_label = ttk.Label(status_frame, text="", font=("Consolas", 9), foreground="gray")
        self.model_label.pack(side="right")
        
        # Progress bar for active operations
        self.progress_frame = ttk.Frame(self.monitor_frame)
        self.progress_frame.pack(fill="x", pady=5)
        
        self.progress_bar = ttk.Progressbar(
            self.progress_frame, 
            mode='indeterminate',
            length=300
        )
        self.progress_bar.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        # Response time label
        self.time_label = ttk.Label(self.progress_frame, text="", font=("Consolas", 9))
        self.time_label.pack(side="right")
        
        # Activity details
        self.activity_frame = ttk.Frame(self.monitor_frame)
        self.activity_frame.pack(fill="x", pady=5)
        
        self.activity_label = ttk.Label(
            self.activity_frame, 
            text="💤 Idle - No active operations",
            font=("Consolas", 9),
            foreground="gray"
        )
        self.activity_label.pack(side="left")
        
        # Quick actions
        actions_frame = ttk.Frame(self.monitor_frame)
        actions_frame.pack(fill="x", pady=(5, 0))
        
        ttk.Button(
            actions_frame,
            text="🔄 Refresh",
            command=self.force_refresh,
            width=12
        ).pack(side="left", padx=(0, 5))
        
        ttk.Button(
            actions_frame,
            text="📊 Models",
            command=self.show_models,
            width=12
        ).pack(side="left", padx=(0, 5))
        
        ttk.Button(
            actions_frame,
            text="🧪 Test",
            command=self.test_connection,
            width=12
        ).pack(side="left")
        
        # Initially hide progress bar
        self.progress_bar.pack_forget()
    
    def update_status_indicator(self, status: OllamaStatus):
        """Update the visual status indicator"""
        self.status_canvas.delete("all")
        
        # Color mapping
        colors = {
            OllamaStatus.OFFLINE: "#ff4444",      # Red
            OllamaStatus.ONLINE: "#44ff44",       # Green  
            OllamaStatus.PROCESSING: "#ffaa00",   # Orange
            OllamaStatus.ERROR: "#ff0000",        # Bright Red
            OllamaStatus.IDLE: "#4444ff"          # Blue
        }
        
        color = colors.get(status, "#888888")
        
        # Draw status LED
        self.status_canvas.create_oval(2, 2, 18, 18, fill=color, outline="white", width=2)
        
        # Add pulsing effect for processing
        if status == OllamaStatus.PROCESSING:
            self.pulse_indicator()
    
    def pulse_indicator(self):
        """Create pulsing effect for processing status"""
        if self.status == OllamaStatus.PROCESSING:
            # Animate the indicator
            self.status_canvas.delete("all")
            self.status_canvas.create_oval(2, 2, 18, 18, fill="#ffaa00", outline="white", width=2)
            self.parent_frame.after(500, lambda: self.pulse_indicator_dim())
    
    def pulse_indicator_dim(self):
        """Dim phase of pulse animation"""
        if self.status == OllamaStatus.PROCESSING:
            self.status_canvas.delete("all")
            self.status_canvas.create_oval(2, 2, 18, 18, fill="#cc7700", outline="white", width=2)
            self.parent_frame.after(500, self.pulse_indicator)
    
    def start_monitoring(self):
        """Start the monitoring thread"""
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitoring_thread.start()
        self.logger.info("Ollama monitoring started")
    
    def stop_monitoring(self):
        """Stop the monitoring thread"""
        self.monitoring_active = False
        self.logger.info("Ollama monitoring stopped")
    
    def monitor_loop(self):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                self.check_ollama_status()
                time.sleep(2)  # Check every 2 seconds
            except Exception as e:
                self.logger.error(f"Monitor loop error: {e}")
                time.sleep(5)
    
    def check_ollama_status(self):
        """Check Ollama server status"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=3)
            
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                
                if self.current_activity:
                    new_status = OllamaStatus.PROCESSING
                else:
                    new_status = OllamaStatus.IDLE
                
                # Update UI in main thread
                self.parent_frame.after(0, lambda: self.update_status_ui(new_status, len(models)))
                
            else:
                self.parent_frame.after(0, lambda: self.update_status_ui(OllamaStatus.ERROR, 0))
                
        except Exception as e:
            self.parent_frame.after(0, lambda: self.update_status_ui(OllamaStatus.OFFLINE, 0))
    
    def update_status_ui(self, status: OllamaStatus, model_count: int):
        """Update the UI with current status"""
        self.status = status
        self.update_status_indicator(status)
        
        # Status messages
        status_messages = {
            OllamaStatus.OFFLINE: "❌ Ollama Offline",
            OllamaStatus.ONLINE: f"✅ Ollama Online ({model_count} models)",
            OllamaStatus.PROCESSING: "🔄 Processing...",
            OllamaStatus.ERROR: "⚠️ Connection Error",
            OllamaStatus.IDLE: f"💤 Idle ({model_count} models ready)"
        }
        
        self.status_label.config(text=status_messages.get(status, "Unknown"))
        
        # Update model info
        if model_count > 0:
            self.model_label.config(text=f"{model_count} models")
        else:
            self.model_label.config(text="")
    
    def start_activity(self, model: str, prompt: str):
        """Start tracking an Ollama activity"""
        self.current_activity = OllamaActivity(
            model=model,
            prompt=prompt[:50] + "..." if len(prompt) > 50 else prompt,
            start_time=time.time(),
            status=OllamaStatus.PROCESSING
        )
        
        # Update UI
        self.parent_frame.after(0, self.update_activity_ui)
        self.parent_frame.after(0, self.show_progress)
        
        self.logger.info(f"Started activity: {model} - {prompt[:30]}...")
    
    def end_activity(self, success: bool = True, error: str = None):
        """End the current activity"""
        if self.current_activity:
            self.current_activity.response_time = time.time() - self.current_activity.start_time
            
            if not success and error:
                self.current_activity.error_message = error
                self.current_activity.status = OllamaStatus.ERROR
            
            # Update UI
            self.parent_frame.after(0, self.hide_progress)
            self.parent_frame.after(0, self.update_final_activity_ui)
            
            self.logger.info(f"Ended activity: {self.current_activity.response_time:.2f}s")
            
            # Clear activity after 5 seconds
            self.parent_frame.after(5000, self.clear_activity)
    
    def update_activity_ui(self):
        """Update activity display"""
        if self.current_activity:
            elapsed = time.time() - self.current_activity.start_time
            self.activity_label.config(
                text=f"🔄 {self.current_activity.model}: {self.current_activity.prompt}",
                foreground="orange"
            )
            self.time_label.config(text=f"{elapsed:.1f}s")
    
    def update_final_activity_ui(self):
        """Update UI when activity completes"""
        if self.current_activity and self.current_activity.response_time:
            if self.current_activity.error_message:
                self.activity_label.config(
                    text=f"❌ Error: {self.current_activity.error_message}",
                    foreground="red"
                )
            else:
                self.activity_label.config(
                    text=f"✅ Completed: {self.current_activity.model} ({self.current_activity.response_time:.2f}s)",
                    foreground="green"
                )
            self.time_label.config(text=f"{self.current_activity.response_time:.2f}s")
    
    def clear_activity(self):
        """Clear the current activity"""
        self.current_activity = None
        self.activity_label.config(
            text="💤 Idle - No active operations",
            foreground="gray"
        )
        self.time_label.config(text="")
    
    def show_progress(self):
        """Show progress bar"""
        self.progress_bar.pack(side="left", fill="x", expand=True, padx=(0, 10))
        self.progress_bar.start(10)  # Animation speed
    
    def hide_progress(self):
        """Hide progress bar"""
        self.progress_bar.stop()
        self.progress_bar.pack_forget()
    
    def force_refresh(self):
        """Force refresh status"""
        self.check_ollama_status()
    
    def show_models(self):
        """Show available models"""
        try:
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                data = response.json()
                models = data.get('models', [])
                
                # Create popup window
                popup = tk.Toplevel(self.parent_frame)
                popup.title("🤖 Available Ollama Models")
                popup.geometry("500x400")
                popup.configure(bg="#1a1a1a")
                
                # Models list
                text_widget = tk.Text(popup, bg="#2a2a2a", fg="white", font=("Consolas", 10))
                text_widget.pack(fill="both", expand=True, padx=10, pady=10)
                
                text_widget.insert("1.0", "🤖 AVAILABLE OLLAMA MODELS\n")
                text_widget.insert("end", "=" * 40 + "\n\n")
                
                for i, model in enumerate(models, 1):
                    name = model.get('name', 'Unknown')
                    size_gb = round(model.get('size', 0) / (1024**3), 1)
                    modified = model.get('modified_at', 'Unknown')
                    
                    text_widget.insert("end", f"{i}. {name}\n")
                    text_widget.insert("end", f"   Size: {size_gb} GB\n")
                    text_widget.insert("end", f"   Modified: {modified[:10]}\n\n")
                
                text_widget.config(state="disabled")
                
        except Exception as e:
            self.logger.error(f"Failed to show models: {e}")
    
    def test_connection(self):
        """Test Ollama connection"""
        self.start_activity("test", "Connection test")
        
        def test_thread():
            try:
                response = requests.get("http://localhost:11434/api/tags", timeout=5)
                if response.status_code == 200:
                    time.sleep(1)  # Simulate processing
                    self.end_activity(success=True)
                else:
                    self.end_activity(success=False, error=f"HTTP {response.status_code}")
            except Exception as e:
                self.end_activity(success=False, error=str(e))
        
        threading.Thread(target=test_thread, daemon=True).start()
    
    def add_status_callback(self, callback: Callable[[OllamaStatus], None]):
        """Add callback for status changes"""
        self.status_callbacks.append(callback)
    
    def get_current_status(self) -> OllamaStatus:
        """Get current Ollama status"""
        return self.status
