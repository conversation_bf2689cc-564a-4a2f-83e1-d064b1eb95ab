@echo off
REM ============================================================================
REM Cybex Futuristic GUI Launcher
REM Next-Generation Graphical AI Terminal
REM ============================================================================

title Cybex Futuristic - AI Terminal GUI

REM Set colors for better visibility
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║  ◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤  ║
echo ║                                                                              ║
echo ║                    🚀 CYBEX FUTURISTIC GUI LAUNCHER                         ║
echo ║                   Next-Generation AI Terminal                               ║
echo ║                                                                              ║
echo ║  ◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣  ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ first.
    echo 💡 Download from: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python detected
echo.

REM Check if main file exists
if not exist "bin\cybex_futuristic.py" (
    echo ❌ bin\cybex_futuristic.py not found
    echo 💡 Make sure you're in the correct directory
    echo.
    pause
    exit /b 1
)

echo ✅ Cybex Futuristic GUI files found
echo.

REM Check for required modules
echo 🔍 Checking Python modules...
python -c "import tkinter, psutil, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Some Python modules may be missing
    echo 💡 The launcher will check and report missing modules
    echo.
)

REM Launch Cybex Futuristic GUI
echo 🚀 Starting Cybex Futuristic GUI...
echo 💡 This may take a few moments to load...
echo.

python bin\cybex_futuristic.py

REM Handle exit
echo.
echo 👋 Cybex Futuristic GUI session ended
echo.
pause
