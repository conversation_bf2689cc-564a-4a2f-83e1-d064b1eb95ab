@echo off
REM ============================================================================
REM Cybex Futuristic GUI Launcher
REM Next-Generation Graphical AI Terminal
REM ============================================================================

title Cybex Futuristic - AI Terminal GUI

REM Set colors for better visibility
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║  ◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤  ║
echo ║                                                                              ║
echo ║                    🚀 CYBEX FUTURISTIC GUI LAUNCHER                         ║
echo ║                   Next-Generation AI Terminal                               ║
echo ║                                                                              ║
echo ║  ◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣  ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ first.
    echo 💡 Download from: https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python detected
echo.

REM Check if main file exists
if not exist "bin\main.py" (
    echo ❌ bin\main.py not found
    echo 💡 Make sure you're in the correct directory
    echo.
    pause
    exit /b 1
)

echo ✅ CYBEX Enterprise files found
echo.

REM Check for required modules
echo 🔍 Checking Python modules...
python -c "import tkinter, psutil, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Some Python modules may be missing
    echo 💡 The launcher will check and report missing modules
    echo.
)

REM Launch CYBEX Enterprise with Ollama
echo 🚀 Starting CYBEX Enterprise with Ollama AI...
echo 💡 This may take a few moments to load...
echo.

python bin\main.py

REM Handle exit
echo.
echo 👋 CYBEX Enterprise session ended
echo.
pause
