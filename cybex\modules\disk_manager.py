"""
Disk Manager Module
Comprehensive disk management with SMART monitoring and cleanup automation
"""

import os
import re
import time
import shutil
import platform
import tempfile
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class DiskHealth(Enum):
    """Disk health status"""
    GOOD = "good"
    WARNING = "warning"
    CRITICAL = "critical"
    UNKNOWN = "unknown"


class CleanupCategory(Enum):
    """File cleanup categories"""
    TEMP_FILES = "temp_files"
    LOG_FILES = "log_files"
    CACHE_FILES = "cache_files"
    BROWSER_CACHE = "browser_cache"
    RECYCLE_BIN = "recycle_bin"
    OLD_DOWNLOADS = "old_downloads"
    SYSTEM_TEMP = "system_temp"


class DiskManager:
    """
    Comprehensive disk management with SMART monitoring and cleanup automation
    """

    def __init__(self, config_manager, log_manager, command_executor):
        """Initialize disk manager"""
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.command_executor = command_executor
        self.logger = log_manager.get_logger(__name__)

        # Disk management configuration
        self.disk_config = config_manager.get_section('disk_management')
        self.enabled = self.disk_config.get('enabled', True)
        self.smart_monitoring = self.disk_config.get('smart_monitoring', True)
        self.auto_cleanup = self.disk_config.get('auto_cleanup', False)
        self.cleanup_threshold = self.disk_config.get('cleanup_threshold', 85)
        self.temp_file_age = self.disk_config.get('temp_file_age', 7)
        self.log_file_age = self.disk_config.get('log_file_age', 30)

        # System type
        self.system_type = platform.system().lower()
        self.is_windows = self.system_type == 'windows'

        # Disk information cache
        self.disk_cache: Dict[str, Dict] = {}
        self.smart_cache: Dict[str, Dict] = {}
        self.last_scan_time = None

        # Cleanup patterns
        self.cleanup_patterns = self._init_cleanup_patterns()

        # Initialize disk information
        self._scan_disks()

    def _init_cleanup_patterns(self) -> Dict[CleanupCategory, Dict]:
        """Initialize cleanup patterns for different file types"""
        if self.is_windows:
            return {
                CleanupCategory.TEMP_FILES: {
                    'paths': [
                        r'C:\Windows\Temp',
                        r'C:\Users\<USER>\AppData\Local\Temp',
                        r'C:\Temp'
                    ],
                    'patterns': ['*.tmp', '*.temp', '*.log'],
                    'age_days': self.temp_file_age
                },
                CleanupCategory.LOG_FILES: {
                    'paths': [
                        r'C:\Windows\Logs',
                        r'C:\ProgramData\*\Logs',
                        r'C:\Users\<USER>\AppData\Local\*\Logs'
                    ],
                    'patterns': ['*.log', '*.log.*'],
                    'age_days': self.log_file_age
                },
                CleanupCategory.CACHE_FILES: {
                    'paths': [
                        r'C:\Users\<USER>\AppData\Local\*\Cache',
                        r'C:\ProgramData\*\Cache'
                    ],
                    'patterns': ['*'],
                    'age_days': 7
                },
                CleanupCategory.BROWSER_CACHE: {
                    'paths': [
                        r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\*\Cache',
                        r'C:\Users\<USER>\AppData\Local\Mozilla\Firefox\Profiles\*\cache2',
                        r'C:\Users\<USER>\AppData\Local\Microsoft\Edge\User Data\*\Cache'
                    ],
                    'patterns': ['*'],
                    'age_days': 3
                },
                CleanupCategory.RECYCLE_BIN: {
                    'paths': [r'C:\$Recycle.Bin'],
                    'patterns': ['*'],
                    'age_days': 0
                }
            }
        else:
            return {
                CleanupCategory.TEMP_FILES: {
                    'paths': ['/tmp', '/var/tmp', '/home/<USER>/tmp'],
                    'patterns': ['*.tmp', '*.temp', '*.log'],
                    'age_days': self.temp_file_age
                },
                CleanupCategory.LOG_FILES: {
                    'paths': ['/var/log', '/home/<USER>/logs', '/opt/*/logs'],
                    'patterns': ['*.log', '*.log.*'],
                    'age_days': self.log_file_age
                },
                CleanupCategory.CACHE_FILES: {
                    'paths': ['/home/<USER>/.cache', '/var/cache'],
                    'patterns': ['*'],
                    'age_days': 7
                },
                CleanupCategory.BROWSER_CACHE: {
                    'paths': [
                        '/home/<USER>/.cache/google-chrome',
                        '/home/<USER>/.cache/mozilla/firefox',
                        '/home/<USER>/.cache/chromium'
                    ],
                    'patterns': ['*'],
                    'age_days': 3
                }
            }

    def _scan_disks(self) -> None:
        """Scan and cache disk information"""
        if not PSUTIL_AVAILABLE:
            self.logger.warning("psutil not available, disk scanning limited")
            return

        try:
            self.disk_cache = {}

            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)

                    disk_info = {
                        'device': disk.device,
                        'mountpoint': disk.mountpoint,
                        'filesystem': disk.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': usage.percent,
                        'total_gb': round(usage.total / (1024**3), 2),
                        'used_gb': round(usage.used / (1024**3), 2),
                        'free_gb': round(usage.free / (1024**3), 2),
                        'last_scan': datetime.now().isoformat()
                    }

                    # Add SMART data if available
                    if self.smart_monitoring:
                        smart_data = self._get_smart_data(disk.device)
                        disk_info['smart'] = smart_data
                        disk_info['health'] = self._assess_disk_health(smart_data)

                    self.disk_cache[disk.device] = disk_info

                except Exception as e:
                    self.logger.warning(f"Failed to scan disk {disk.device}: {e}")

            self.last_scan_time = datetime.now()
            self.logger.info(f"Scanned {len(self.disk_cache)} disks")

        except Exception as e:
            self.logger.error(f"Failed to scan disks: {e}")

    def _get_smart_data(self, device: str) -> Dict[str, Any]:
        """Get SMART data for a disk device"""
        smart_data = {
            'available': False,
            'status': 'unknown',
            'temperature': None,
            'power_on_hours': None,
            'power_cycle_count': None,
            'reallocated_sectors': None,
            'pending_sectors': None,
            'uncorrectable_sectors': None,
            'attributes': {}
        }

        try:
            if self.is_windows:
                # Use wmic for Windows SMART data
                cmd = f'wmic diskdrive where "DeviceID=\\"{device.replace("\\", "\\\\")}\\"" get Status,Size,Model'
                success, output, _ = self.command_executor.execute_command(cmd, confirm_required=False)

                if success and output:
                    smart_data['available'] = True
                    if 'OK' in output:
                        smart_data['status'] = 'good'
                    elif 'Pred Fail' in output:
                        smart_data['status'] = 'warning'
                    else:
                        smart_data['status'] = 'unknown'
            else:
                # Use smartctl for Linux SMART data
                device_path = device.replace('/dev/', '')
                cmd = f'smartctl -a /dev/{device_path}'
                success, output, _ = self.command_executor.execute_command(cmd, confirm_required=False)

                if success and output:
                    smart_data['available'] = True
                    smart_data = self._parse_smartctl_output(output)

        except Exception as e:
            self.logger.warning(f"Failed to get SMART data for {device}: {e}")

        return smart_data

    def _parse_smartctl_output(self, output: str) -> Dict[str, Any]:
        """Parse smartctl output for SMART attributes"""
        smart_data = {
            'available': True,
            'status': 'unknown',
            'temperature': None,
            'power_on_hours': None,
            'power_cycle_count': None,
            'reallocated_sectors': None,
            'pending_sectors': None,
            'uncorrectable_sectors': None,
            'attributes': {}
        }

        try:
            lines = output.split('\n')

            # Parse overall health
            for line in lines:
                if 'SMART overall-health' in line:
                    if 'PASSED' in line:
                        smart_data['status'] = 'good'
                    elif 'FAILED' in line:
                        smart_data['status'] = 'critical'
                    break

            # Parse attributes
            in_attributes = False
            for line in lines:
                if 'ID# ATTRIBUTE_NAME' in line:
                    in_attributes = True
                    continue

                if in_attributes and line.strip():
                    parts = line.split()
                    if len(parts) >= 10:
                        attr_id = parts[0]
                        attr_name = parts[1]
                        raw_value = parts[9]

                        # Extract specific attributes
                        if attr_name == 'Temperature_Celsius':
                            try:
                                smart_data['temperature'] = int(raw_value.split()[0])
                            except:
                                pass
                        elif attr_name == 'Power_On_Hours':
                            try:
                                smart_data['power_on_hours'] = int(raw_value)
                            except:
                                pass
                        elif attr_name == 'Power_Cycle_Count':
                            try:
                                smart_data['power_cycle_count'] = int(raw_value)
                            except:
                                pass
                        elif attr_name == 'Reallocated_Sector_Ct':
                            try:
                                smart_data['reallocated_sectors'] = int(raw_value)
                            except:
                                pass
                        elif attr_name == 'Current_Pending_Sector':
                            try:
                                smart_data['pending_sectors'] = int(raw_value)
                            except:
                                pass
                        elif attr_name == 'Offline_Uncorrectable':
                            try:
                                smart_data['uncorrectable_sectors'] = int(raw_value)
                            except:
                                pass

                        smart_data['attributes'][attr_name] = {
                            'id': attr_id,
                            'raw_value': raw_value,
                            'value': parts[3] if len(parts) > 3 else '',
                            'worst': parts[4] if len(parts) > 4 else '',
                            'threshold': parts[5] if len(parts) > 5 else ''
                        }

        except Exception as e:
            self.logger.warning(f"Failed to parse smartctl output: {e}")

        return smart_data

    def _assess_disk_health(self, smart_data: Dict[str, Any]) -> DiskHealth:
        """Assess disk health based on SMART data"""
        if not smart_data.get('available', False):
            return DiskHealth.UNKNOWN

        # Check overall SMART status
        status = smart_data.get('status', 'unknown')
        if status == 'critical':
            return DiskHealth.CRITICAL
        elif status == 'warning':
            return DiskHealth.WARNING

        # Check specific attributes
        reallocated = smart_data.get('reallocated_sectors', 0)
        pending = smart_data.get('pending_sectors', 0)
        uncorrectable = smart_data.get('uncorrectable_sectors', 0)

        if reallocated > 100 or pending > 10 or uncorrectable > 0:
            return DiskHealth.CRITICAL
        elif reallocated > 10 or pending > 0:
            return DiskHealth.WARNING

        # Check temperature
        temperature = smart_data.get('temperature')
        if temperature and temperature > 60:
            return DiskHealth.WARNING
        elif temperature and temperature > 70:
            return DiskHealth.CRITICAL

        return DiskHealth.GOOD

    def get_disk_info(self, refresh: bool = False) -> Dict[str, Dict]:
        """Get disk information"""
        if refresh or not self.disk_cache or not self.last_scan_time:
            self._scan_disks()

        return self.disk_cache

    def get_disk_health_summary(self) -> Dict[str, Any]:
        """Get disk health summary"""
        disk_info = self.get_disk_info()

        summary = {
            'total_disks': len(disk_info),
            'healthy_disks': 0,
            'warning_disks': 0,
            'critical_disks': 0,
            'unknown_disks': 0,
            'total_space_gb': 0,
            'used_space_gb': 0,
            'free_space_gb': 0,
            'average_usage_percent': 0,
            'disks': []
        }

        total_usage = 0

        for device, info in disk_info.items():
            health = info.get('health', DiskHealth.UNKNOWN)

            if health == DiskHealth.GOOD:
                summary['healthy_disks'] += 1
            elif health == DiskHealth.WARNING:
                summary['warning_disks'] += 1
            elif health == DiskHealth.CRITICAL:
                summary['critical_disks'] += 1
            else:
                summary['unknown_disks'] += 1

            summary['total_space_gb'] += info.get('total_gb', 0)
            summary['used_space_gb'] += info.get('used_gb', 0)
            summary['free_space_gb'] += info.get('free_gb', 0)
            total_usage += info.get('percent', 0)

            summary['disks'].append({
                'device': device,
                'mountpoint': info.get('mountpoint', ''),
                'health': health.value if isinstance(health, DiskHealth) else str(health),
                'usage_percent': info.get('percent', 0),
                'free_gb': info.get('free_gb', 0),
                'total_gb': info.get('total_gb', 0)
            })

        if len(disk_info) > 0:
            summary['average_usage_percent'] = total_usage / len(disk_info)

        return summary

    def analyze_disk_usage(self, path: str, max_depth: int = 3) -> List[Dict[str, Any]]:
        """Analyze disk usage for a specific path"""
        if not os.path.exists(path):
            return []

        try:
            usage_data = []

            # Get immediate subdirectories
            for item in os.listdir(path):
                item_path = os.path.join(path, item)

                if os.path.isdir(item_path):
                    try:
                        size = self._get_directory_size(item_path)
                        usage_data.append({
                            'path': item_path,
                            'name': item,
                            'type': 'directory',
                            'size_bytes': size,
                            'size_mb': round(size / (1024**2), 2),
                            'size_gb': round(size / (1024**3), 2)
                        })
                    except Exception as e:
                        self.logger.warning(f"Failed to get size for {item_path}: {e}")
                elif os.path.isfile(item_path):
                    try:
                        size = os.path.getsize(item_path)
                        usage_data.append({
                            'path': item_path,
                            'name': item,
                            'type': 'file',
                            'size_bytes': size,
                            'size_mb': round(size / (1024**2), 2),
                            'size_gb': round(size / (1024**3), 2)
                        })
                    except Exception as e:
                        self.logger.warning(f"Failed to get size for {item_path}: {e}")

            # Sort by size (largest first)
            usage_data.sort(key=lambda x: x['size_bytes'], reverse=True)

            return usage_data

        except Exception as e:
            self.logger.error(f"Failed to analyze disk usage for {path}: {e}")
            return []

    def _get_directory_size(self, path: str) -> int:
        """Get total size of a directory"""
        total_size = 0

        try:
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        pass
        except Exception:
            pass

        return total_size

    def scan_for_cleanup(self, categories: Optional[List[CleanupCategory]] = None) -> Dict[CleanupCategory, Dict]:
        """Scan for files that can be cleaned up"""
        if not categories:
            categories = list(CleanupCategory)

        cleanup_data = {}

        for category in categories:
            if category not in self.cleanup_patterns:
                continue

            pattern_info = self.cleanup_patterns[category]
            category_data = {
                'files': [],
                'total_size': 0,
                'total_count': 0,
                'estimated_space_gb': 0
            }

            for path_pattern in pattern_info['paths']:
                try:
                    # Expand wildcards in path
                    expanded_paths = self._expand_path_pattern(path_pattern)

                    for expanded_path in expanded_paths:
                        if not os.path.exists(expanded_path):
                            continue

                        # Find files matching patterns
                        files = self._find_files_by_pattern(
                            expanded_path,
                            pattern_info['patterns'],
                            pattern_info['age_days']
                        )

                        for file_info in files:
                            category_data['files'].append(file_info)
                            category_data['total_size'] += file_info['size']
                            category_data['total_count'] += 1

                except Exception as e:
                    self.logger.warning(f"Failed to scan {path_pattern}: {e}")

            category_data['estimated_space_gb'] = round(category_data['total_size'] / (1024**3), 2)
            cleanup_data[category] = category_data

        return cleanup_data

    def _expand_path_pattern(self, path_pattern: str) -> List[str]:
        """Expand path patterns with wildcards"""
        import glob

        try:
            if '*' in path_pattern:
                return glob.glob(path_pattern)
            else:
                return [path_pattern] if os.path.exists(path_pattern) else []
        except Exception:
            return []

    def _find_files_by_pattern(self, base_path: str, patterns: List[str], age_days: int) -> List[Dict]:
        """Find files matching patterns and age criteria"""
        import fnmatch

        files = []
        cutoff_time = time.time() - (age_days * 24 * 3600)

        try:
            for root, dirs, filenames in os.walk(base_path):
                # Skip hidden directories
                dirs[:] = [d for d in dirs if not d.startswith('.')]

                for filename in filenames:
                    filepath = os.path.join(root, filename)

                    # Check if file matches any pattern
                    matches_pattern = any(fnmatch.fnmatch(filename, pattern) for pattern in patterns)

                    if matches_pattern:
                        try:
                            stat = os.stat(filepath)

                            # Check age criteria
                            if age_days == 0 or stat.st_mtime < cutoff_time:
                                files.append({
                                    'path': filepath,
                                    'name': filename,
                                    'size': stat.st_size,
                                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                                    'age_days': (time.time() - stat.st_mtime) / (24 * 3600)
                                })
                        except (OSError, FileNotFoundError):
                            pass

        except Exception as e:
            self.logger.warning(f"Failed to scan {base_path}: {e}")

        return files

    def cleanup_files(self, categories: List[CleanupCategory],
                     dry_run: bool = True, max_size_gb: Optional[float] = None) -> Dict[str, Any]:
        """Clean up files in specified categories"""
        cleanup_result = {
            'dry_run': dry_run,
            'categories_processed': [],
            'files_removed': 0,
            'space_freed_gb': 0,
            'errors': [],
            'details': {}
        }

        # Scan for cleanup candidates
        cleanup_data = self.scan_for_cleanup(categories)

        total_freed = 0
        max_size_bytes = max_size_gb * (1024**3) if max_size_gb else float('inf')

        for category, data in cleanup_data.items():
            if total_freed >= max_size_bytes:
                break

            category_result = {
                'files_removed': 0,
                'space_freed': 0,
                'errors': []
            }

            # Sort files by age (oldest first)
            files = sorted(data['files'], key=lambda f: f['age_days'], reverse=True)

            for file_info in files:
                if total_freed >= max_size_bytes:
                    break

                try:
                    if not dry_run:
                        os.remove(file_info['path'])

                    category_result['files_removed'] += 1
                    category_result['space_freed'] += file_info['size']
                    total_freed += file_info['size']

                    self.logger.debug(f"{'Would remove' if dry_run else 'Removed'}: {file_info['path']}")

                except Exception as e:
                    error_msg = f"Failed to remove {file_info['path']}: {e}"
                    category_result['errors'].append(error_msg)
                    self.logger.warning(error_msg)

            cleanup_result['categories_processed'].append(category.value)
            cleanup_result['files_removed'] += category_result['files_removed']
            cleanup_result['space_freed_gb'] += round(category_result['space_freed'] / (1024**3), 2)
            cleanup_result['errors'].extend(category_result['errors'])
            cleanup_result['details'][category.value] = category_result

        if not dry_run:
            self.logger.info(f"Cleanup completed: {cleanup_result['files_removed']} files, "
                           f"{cleanup_result['space_freed_gb']:.2f} GB freed")

        return cleanup_result

    def auto_cleanup_if_needed(self) -> Optional[Dict[str, Any]]:
        """Perform automatic cleanup if disk usage exceeds threshold"""
        if not self.auto_cleanup:
            return None

        disk_info = self.get_disk_info()

        # Check if any disk exceeds threshold
        needs_cleanup = False
        for device, info in disk_info.items():
            if info.get('percent', 0) > self.cleanup_threshold:
                needs_cleanup = True
                break

        if not needs_cleanup:
            return None

        self.logger.info("Disk usage exceeds threshold, starting automatic cleanup")

        # Perform cleanup with safe categories
        safe_categories = [
            CleanupCategory.TEMP_FILES,
            CleanupCategory.LOG_FILES,
            CleanupCategory.BROWSER_CACHE
        ]

        return self.cleanup_files(safe_categories, dry_run=False, max_size_gb=5.0)

    def defragment_disk(self, drive: str) -> Dict[str, Any]:
        """Defragment a disk (Windows only)"""
        if not self.is_windows:
            return {'success': False, 'error': 'Defragmentation only supported on Windows'}

        try:
            # Check if drive needs defragmentation
            analyze_cmd = f'defrag {drive} /A'
            success, output, _ = self.command_executor.execute_command(analyze_cmd, confirm_required=False)

            if not success:
                return {'success': False, 'error': f'Failed to analyze drive: {output}'}

            # Parse analysis results
            fragmentation_percent = self._parse_defrag_analysis(output)

            result = {
                'drive': drive,
                'fragmentation_percent': fragmentation_percent,
                'needs_defrag': fragmentation_percent > 10,
                'analysis_output': output
            }

            # Perform defragmentation if needed
            if fragmentation_percent > 10:
                self.logger.info(f"Drive {drive} is {fragmentation_percent}% fragmented, starting defragmentation")

                defrag_cmd = f'defrag {drive} /O'
                success, defrag_output, _ = self.command_executor.execute_command(
                    defrag_cmd,
                    timeout=3600,  # 1 hour timeout
                    confirm_required=False
                )

                result['defrag_performed'] = True
                result['defrag_success'] = success
                result['defrag_output'] = defrag_output

                if success:
                    self.logger.info(f"Defragmentation of drive {drive} completed successfully")
                else:
                    self.logger.error(f"Defragmentation of drive {drive} failed: {defrag_output}")
            else:
                result['defrag_performed'] = False
                result['reason'] = 'Fragmentation level acceptable'

            return result

        except Exception as e:
            self.logger.error(f"Failed to defragment drive {drive}: {e}")
            return {'success': False, 'error': str(e)}

    def _parse_defrag_analysis(self, output: str) -> float:
        """Parse defragmentation analysis output"""
        try:
            # Look for fragmentation percentage in output
            for line in output.split('\n'):
                if 'fragmented' in line.lower() and '%' in line:
                    # Extract percentage using regex
                    import re
                    match = re.search(r'(\d+(?:\.\d+)?)%', line)
                    if match:
                        return float(match.group(1))
            return 0.0
        except:
            return 0.0

    def check_disk_errors(self, drive: str) -> Dict[str, Any]:
        """Check disk for errors"""
        try:
            if self.is_windows:
                # Use chkdsk for Windows
                cmd = f'chkdsk {drive} /f /r'
                success, output, _ = self.command_executor.execute_command(cmd, confirm_required=False)
            else:
                # Use fsck for Linux
                cmd = f'fsck -n {drive}'
                success, output, _ = self.command_executor.execute_command(cmd, confirm_required=False)

            return {
                'drive': drive,
                'check_performed': True,
                'success': success,
                'output': output,
                'errors_found': 'error' in output.lower() or 'bad' in output.lower()
            }

        except Exception as e:
            self.logger.error(f"Failed to check disk {drive}: {e}")
            return {
                'drive': drive,
                'check_performed': False,
                'error': str(e)
            }

    def get_disk_recommendations(self) -> List[Dict[str, Any]]:
        """Get disk optimization recommendations"""
        recommendations = []
        disk_info = self.get_disk_info()

        for device, info in disk_info.items():
            device_recommendations = []

            # Usage recommendations
            usage_percent = info.get('percent', 0)
            if usage_percent > 90:
                device_recommendations.append({
                    'type': 'critical',
                    'category': 'space',
                    'message': f"Critical: Disk usage at {usage_percent:.1f}%",
                    'action': 'Free up space immediately',
                    'priority': 'high'
                })
            elif usage_percent > 80:
                device_recommendations.append({
                    'type': 'warning',
                    'category': 'space',
                    'message': f"Warning: Disk usage at {usage_percent:.1f}%",
                    'action': 'Consider cleaning up files',
                    'priority': 'medium'
                })

            # Health recommendations
            health = info.get('health', DiskHealth.UNKNOWN)
            if health == DiskHealth.CRITICAL:
                device_recommendations.append({
                    'type': 'critical',
                    'category': 'health',
                    'message': 'Critical disk health issues detected',
                    'action': 'Backup data and replace disk immediately',
                    'priority': 'critical'
                })
            elif health == DiskHealth.WARNING:
                device_recommendations.append({
                    'type': 'warning',
                    'category': 'health',
                    'message': 'Disk health warning detected',
                    'action': 'Monitor closely and consider replacement',
                    'priority': 'high'
                })

            # SMART recommendations
            smart_data = info.get('smart', {})
            if smart_data.get('available'):
                temperature = smart_data.get('temperature')
                if temperature and temperature > 60:
                    device_recommendations.append({
                        'type': 'warning',
                        'category': 'temperature',
                        'message': f'High disk temperature: {temperature}°C',
                        'action': 'Check cooling and ventilation',
                        'priority': 'medium'
                    })

                reallocated = smart_data.get('reallocated_sectors', 0)
                if reallocated > 0:
                    device_recommendations.append({
                        'type': 'warning',
                        'category': 'sectors',
                        'message': f'{reallocated} reallocated sectors detected',
                        'action': 'Monitor disk health closely',
                        'priority': 'medium'
                    })

            if device_recommendations:
                recommendations.append({
                    'device': device,
                    'mountpoint': info.get('mountpoint', ''),
                    'recommendations': device_recommendations
                })

        return recommendations

    def optimize_disk_performance(self, device: str) -> Dict[str, Any]:
        """Optimize disk performance"""
        optimization_result = {
            'device': device,
            'optimizations_performed': [],
            'success': True,
            'errors': []
        }

        try:
            # Get disk info
            disk_info = self.disk_cache.get(device, {})

            # Cleanup temporary files
            cleanup_result = self.cleanup_files([
                CleanupCategory.TEMP_FILES,
                CleanupCategory.LOG_FILES
            ], dry_run=False, max_size_gb=2.0)

            if cleanup_result['files_removed'] > 0:
                optimization_result['optimizations_performed'].append({
                    'type': 'cleanup',
                    'description': f"Cleaned {cleanup_result['files_removed']} files",
                    'space_freed_gb': cleanup_result['space_freed_gb']
                })

            # Defragmentation (Windows only)
            if self.is_windows and device.endswith(':'):
                defrag_result = self.defragment_disk(device)
                if defrag_result.get('defrag_performed'):
                    optimization_result['optimizations_performed'].append({
                        'type': 'defragmentation',
                        'description': f"Defragmented drive {device}",
                        'success': defrag_result.get('defrag_success', False)
                    })

            # Check for errors
            error_check = self.check_disk_errors(device)
            if error_check.get('check_performed'):
                optimization_result['optimizations_performed'].append({
                    'type': 'error_check',
                    'description': f"Checked drive {device} for errors",
                    'errors_found': error_check.get('errors_found', False)
                })

            return optimization_result

        except Exception as e:
            optimization_result['success'] = False
            optimization_result['errors'].append(str(e))
            self.logger.error(f"Failed to optimize disk {device}: {e}")
            return optimization_result