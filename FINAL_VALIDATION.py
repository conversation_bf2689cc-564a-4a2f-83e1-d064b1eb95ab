#!/usr/bin/env python3
"""
CYBEX ENTERPRISE - FINAL VALIDATION
Validazione finale della struttura professionale e funzionalità enterprise
"""

import sys
import os
from pathlib import Path

def validate_directory_structure():
    """Valida la struttura directory professionale"""
    print("📁 Validating Professional Directory Structure")
    print("=" * 50)
    
    required_dirs = [
        "bin", "scripts", "assets", "temp", "cybex", 
        "logs", "snapshots", "examples"
    ]
    
    required_files = [
        "bin/cybex_futuristic.py",
        "bin/cybex_warp.py", 
        "bin/main.py",
        "scripts/CYBEX_LAUNCHER.bat",
        "scripts/cybex_futuristic.bat",
        "assets/logo.png",
        "CYBEX_LAUNCHER.bat",
        "README_ENTERPRISE.md",
        "CYBEX_CONFIG.md",
        "CYBEX_IMPROVEMENTS.md"
    ]
    
    score = 0
    total = len(required_dirs) + len(required_files)
    
    # Check directories
    for dir_name in required_dirs:
        if Path(dir_name).exists():
            print(f"  ✅ {dir_name}/")
            score += 1
        else:
            print(f"  ❌ {dir_name}/")
    
    # Check files
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
            score += 1
        else:
            print(f"  ❌ {file_path}")
    
    print(f"\n📊 Structure Score: {score}/{total} ({score/total*100:.1f}%)")
    return score >= total * 0.9  # 90% threshold


def validate_enterprise_tools():
    """Valida i tool enterprise"""
    print(f"\n🏢 Validating Enterprise Tools")
    print("=" * 35)
    
    try:
        # Add cybex to path
        sys.path.insert(0, str(Path.cwd()))
        
        from cybex.modules.agent_tools import AgentTools
        
        # Initialize agent tools
        agent_tools = AgentTools()
        all_tools = agent_tools.get_available_tools()
        
        print(f"✅ AgentTools initialized")
        print(f"✅ Total tools: {len(all_tools)}")
        
        # Check enterprise tools
        enterprise_tools = [
            "security_audit", "performance_analysis", "network_security_scan",
            "enterprise_health_check", "system_hardening", "backup_analysis"
        ]
        
        found_enterprise = 0
        for tool in enterprise_tools:
            if tool in all_tools:
                print(f"  ✅ {tool}")
                found_enterprise += 1
            else:
                print(f"  ❌ {tool}")
        
        print(f"\n📊 Enterprise Tools: {found_enterprise}/6 ({found_enterprise/6*100:.1f}%)")
        return found_enterprise >= 6
        
    except Exception as e:
        print(f"❌ Error validating enterprise tools: {e}")
        return False


def validate_gui_functionality():
    """Valida funzionalità GUI"""
    print(f"\n🎨 Validating GUI Functionality")
    print("=" * 35)
    
    try:
        # Check if GUI can be imported
        from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI
        print("✅ GUI module imported successfully")
        
        # Check logo path
        logo_path = Path("assets/logo.png")
        if logo_path.exists():
            print("✅ Logo asset available")
            logo_ok = True
        else:
            print("❌ Logo asset missing")
            logo_ok = False
        
        # Check PIL availability
        try:
            from PIL import Image, ImageTk
            print("✅ PIL/Pillow available for image processing")
            pil_ok = True
        except ImportError:
            print("❌ PIL/Pillow not available")
            pil_ok = False
        
        return logo_ok and pil_ok
        
    except Exception as e:
        print(f"❌ Error validating GUI: {e}")
        return False


def validate_launch_system():
    """Valida sistema di lancio"""
    print(f"\n🚀 Validating Launch System")
    print("=" * 30)
    
    launchers = [
        "CYBEX_LAUNCHER.bat",
        "scripts/cybex_futuristic.bat",
        "scripts/cybex_warp.bat"
    ]
    
    score = 0
    for launcher in launchers:
        if Path(launcher).exists():
            print(f"  ✅ {launcher}")
            score += 1
        else:
            print(f"  ❌ {launcher}")
    
    print(f"\n📊 Launch System: {score}/{len(launchers)} ({score/len(launchers)*100:.1f}%)")
    return score >= len(launchers)


def validate_documentation():
    """Valida documentazione"""
    print(f"\n📚 Validating Documentation")
    print("=" * 30)
    
    docs = [
        "README_ENTERPRISE.md",
        "CYBEX_CONFIG.md", 
        "CYBEX_IMPROVEMENTS.md",
        "README.md",
        "LICENSE"
    ]
    
    score = 0
    for doc in docs:
        if Path(doc).exists():
            print(f"  ✅ {doc}")
            score += 1
        else:
            print(f"  ❌ {doc}")
    
    print(f"\n📊 Documentation: {score}/{len(docs)} ({score/len(docs)*100:.1f}%)")
    return score >= len(docs) * 0.8  # 80% threshold


def generate_final_report(results):
    """Genera report finale"""
    print(f"\n" + "="*80)
    print("🏢 CYBEX ENTERPRISE - FINAL VALIDATION REPORT")
    print("="*80)
    
    total_score = sum(results.values())
    max_score = len(results)
    percentage = (total_score / max_score) * 100
    
    print(f"\n📊 VALIDATION RESULTS:")
    for category, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  • {category}: {status}")
    
    print(f"\n🎯 OVERALL SCORE: {total_score}/{max_score} ({percentage:.1f}%)")
    
    if percentage >= 90:
        grade = "🏆 ENTERPRISE GRADE"
        status = "PRODUCTION READY"
        color = "🟢"
    elif percentage >= 80:
        grade = "💼 PROFESSIONAL GRADE"
        status = "NEAR PRODUCTION READY"
        color = "🟡"
    elif percentage >= 70:
        grade = "🔧 DEVELOPMENT GRADE"
        status = "NEEDS IMPROVEMENTS"
        color = "🟠"
    else:
        grade = "⚠️ BASIC GRADE"
        status = "MAJOR ISSUES"
        color = "🔴"
    
    print(f"\n{color} GRADE: {grade}")
    print(f"{color} STATUS: {status}")
    
    if percentage >= 90:
        print(f"\n🎉 CONGRATULATIONS!")
        print(f"CYBEX ENTERPRISE has achieved EXCELLENCE-GRADE status!")
        print(f"\n🚀 READY FOR ENTERPRISE DEPLOYMENT:")
        print(f"  • Launch: CYBEX_LAUNCHER.bat")
        print(f"  • All enterprise tools operational")
        print(f"  • Professional structure implemented")
        print(f"  • Comprehensive documentation available")
        print(f"  • Enhanced AI integration active")
        print(f"  • Advanced security architecture deployed")
        print(f"  • Performance optimization enabled")
        print(f"  • Context-aware operations functional")

        print(f"\n💼 ENTERPRISE FEATURES AVAILABLE:")
        print(f"  • 🔒 Security Audit & Hardening")
        print(f"  • ⚡ Performance Monitoring")
        print(f"  • 🌐 Network Security Scanning")
        print(f"  • 🏥 Enterprise Health Checks")
        print(f"  • 💾 Backup Analysis")
        print(f"  • 🤖 AI-Powered Interface")
        print(f"  • 🧠 Enhanced Natural Language Processing")
        print(f"  • 🛡️ Security-First Architecture")
        print(f"  • ⚡ Performance Optimization")
        print(f"  • 📊 Advanced Context Management")
        print(f"  • 🔍 Intelligent Caching")
        print(f"  • 📈 Predictive Analysis")
        
    else:
        print(f"\n💡 RECOMMENDATIONS:")
        if not results.get("Directory Structure", True):
            print(f"  • Fix directory structure issues")
        if not results.get("Enterprise Tools", True):
            print(f"  • Resolve enterprise tools integration")
        if not results.get("GUI Functionality", True):
            print(f"  • Fix GUI and asset issues")
        if not results.get("Launch System", True):
            print(f"  • Repair launch system")
        if not results.get("Documentation", True):
            print(f"  • Complete documentation")
    
    return percentage >= 90


def main():
    """Esegui validazione finale"""
    print("🎯 CYBEX ENTERPRISE - FINAL VALIDATION")
    print("=" * 60)
    print("Validating professional structure and enterprise functionality...")
    
    # Run all validations
    results = {
        "Directory Structure": validate_directory_structure(),
        "Enterprise Tools": validate_enterprise_tools(),
        "GUI Functionality": validate_gui_functionality(),
        "Launch System": validate_launch_system(),
        "Documentation": validate_documentation()
    }
    
    # Generate final report
    success = generate_final_report(results)
    
    print("\n" + "="*80)
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
