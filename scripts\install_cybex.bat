@echo off
REM ============================================================================
REM Cybex Enterprise - Installation Script
REM Automated setup for Cybex Enterprise
REM by AGTECHdesigne
REM ============================================================================

title Cybex Enterprise - Installation

REM Set colors for output
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║   ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗                                  ║
echo ║  ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝                                  ║
echo ║  ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝                                   ║
echo ║  ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗                                   ║
echo ║  ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗                                  ║
echo ║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝                                  ║
echo ║                                                                              ║
echo ║                      Enterprise Edition                                      ║
echo ║                        by AGTECHdesigne                                      ║
echo ║                                                                              ║
echo ║                    🚀 INSTALLATION WIZARD                                   ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Checking system requirements...
echo.

REM Check Python
echo [1/5] Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    echo After installing Python, run this script again.
    pause
    exit /b 1
) else (
    echo ✅ Python is installed:
    python --version
)
echo.

REM Check pip
echo [2/5] Checking pip (Python package manager)...
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip is not available
    echo Please reinstall Python with pip included
    pause
    exit /b 1
) else (
    echo ✅ pip is available
)
echo.

REM Install dependencies
echo [3/5] Installing Python dependencies...
echo.

REM Create requirements.txt if it doesn't exist
if not exist "requirements.txt" (
    echo Creating requirements.txt...
    echo pyyaml>=6.0 > requirements.txt
    echo requests>=2.25.0 >> requirements.txt
    echo psutil>=5.8.0 >> requirements.txt
    echo colorama>=0.4.4 >> requirements.txt
    echo ✅ requirements.txt created
)

echo Installing dependencies from requirements.txt...
pip install -r requirements.txt

if errorlevel 1 (
    echo ❌ Failed to install some dependencies
    echo.
    echo Trying to install core packages individually...
    pip install pyyaml
    pip install requests
    pip install psutil
    pip install colorama
)

echo ✅ Python dependencies installed
echo.

REM Check Ollama
echo [4/5] Checking Ollama installation...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not detected
    echo.
    echo Ollama is required for AI features. To install:
    echo   1. Visit https://ollama.ai
    echo   2. Download and install Ollama
    echo   3. Run: ollama pull gemma2:7b
    echo   4. Start Ollama service
    echo.
    echo Cybex can run without Ollama, but AI features will be limited.
    echo.
    set /p install_ollama="Would you like to open the Ollama website? (Y/N): "
    if /i "!install_ollama!"=="Y" (
        start https://ollama.ai
    )
) else (
    echo ✅ Ollama server is running
    
    REM Get model count
    for /f %%i in ('curl -s http://localhost:11434/api/tags ^| python -c "import sys,json; data=json.load(sys.stdin); print(len(data.get('models', [])))" 2^>nul') do set model_count=%%i
    if defined model_count (
        echo ✅ Found !model_count! Ollama models
    )
)
echo.

REM Verify installation
echo [5/5] Verifying Cybex installation...
echo.

if not exist "main_enterprise.py" (
    echo ❌ main_enterprise.py not found
    echo Please ensure all Cybex files are in the current directory
    pause
    exit /b 1
)

if not exist "cybex" (
    echo ❌ cybex directory not found
    echo Please ensure the complete Cybex package is extracted
    pause
    exit /b 1
)

echo ✅ Cybex Enterprise files verified
echo.

REM Create desktop shortcut (optional)
set /p create_shortcut="Create desktop shortcut? (Y/N): "
if /i "%create_shortcut%"=="Y" (
    echo Creating desktop shortcut...
    
    set "shortcut_path=%USERPROFILE%\Desktop\Cybex Enterprise.lnk"
    set "target_path=%CD%\cybex_launcher.bat"
    set "icon_path=%CD%\cybex_launcher.bat"
    
    powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%shortcut_path%'); $Shortcut.TargetPath = '%target_path%'; $Shortcut.WorkingDirectory = '%CD%'; $Shortcut.Description = 'Cybex Enterprise by AGTECHdesigne'; $Shortcut.Save()"
    
    if exist "%shortcut_path%" (
        echo ✅ Desktop shortcut created
    ) else (
        echo ⚠️  Could not create desktop shortcut
    )
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║                    🎉 INSTALLATION COMPLETED!                               ║
echo ║                                                                              ║
echo ║  Cybex Enterprise is now ready to use!                                      ║
echo ║                                                                              ║
echo ║  🚀 To start Cybex Enterprise:                                              ║
echo ║     • Double-click: cybex_launcher.bat                                      ║
echo ║     • Or run: start_cybex.bat                                               ║
echo ║     • Or run: python main_enterprise.py                                     ║
echo ║                                                                              ║
echo ║  🎯 Features available:                                                     ║
echo ║     • Natural Language Processing in Italian                                ║
echo ║     • 9 Quick Action Functions                                              ║
echo ║     • Dynamic AI Model Selection                                            ║
echo ║     • Real-time System Monitoring                                           ║
echo ║     • Enterprise-grade Interface                                            ║
echo ║                                                                              ║
echo ║  💬 Try saying: "dammi la situazione memoria del computer"                  ║
echo ║                                                                              ║
echo ║                        by AGTECHdesigne                                     ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

set /p start_now="Start Cybex Enterprise now? (Y/N): "
if /i "%start_now%"=="Y" (
    echo.
    echo 🚀 Starting Cybex Enterprise...
    call cybex_launcher.bat
) else (
    echo.
    echo Thank you for installing Cybex Enterprise!
    echo You can start it anytime using the launcher files.
)

echo.
pause
