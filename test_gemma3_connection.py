#!/usr/bin/env python3
"""
Test diretto per verificare la connessione con Gemma 3
"""

import requests
import json
import sys
import time

def test_ollama_connection():
    """Test connessione base Ollama"""
    try:
        print("🔍 Testing Ollama connection...")
        response = requests.get("http://localhost:11434/api/tags", timeout=10)
        
        if response.status_code == 200:
            models = response.json()
            print("✅ Ollama server is running")
            print(f"📦 Found {len(models['models'])} models:")
            
            gemma_models = []
            for model in models['models']:
                name = model['name']
                size_gb = model['size'] // (1024**3)
                print(f"   • {name} ({size_gb}GB)")
                
                if 'gemma3' in name.lower():
                    gemma_models.append(name)
            
            print(f"\n🤖 Found {len(gemma_models)} Gemma 3 models:")
            for model in gemma_models:
                print(f"   ✅ {model}")
            
            return gemma_models
        else:
            print(f"❌ Ollama server error: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Connection error: {e}")
        return []

def test_gemma3_generation(model_name):
    """Test generazione con Gemma 3"""
    try:
        print(f"\n🧠 Testing {model_name} generation...")
        
        payload = {
            "model": model_name,
            "prompt": "Hello! Please respond with a simple greeting to test if you're working correctly.",
            "stream": False,
            "options": {
                "temperature": 0.7,
                "max_tokens": 100
            }
        }
        
        print("📤 Sending request...")
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:11434/api/generate",
            json=payload,
            timeout=60
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response.status_code == 200:
            result = response.json()
            
            if 'response' in result:
                print(f"✅ {model_name} is working!")
                print(f"⏱️ Response time: {response_time:.2f}s")
                print(f"💬 Response: {result['response']}")
                
                # Check for additional info
                if 'done' in result and result['done']:
                    print("✅ Generation completed successfully")
                
                if 'total_duration' in result:
                    total_ms = result['total_duration'] / 1000000
                    print(f"📊 Total duration: {total_ms:.0f}ms")
                
                if 'load_duration' in result:
                    load_ms = result['load_duration'] / 1000000
                    print(f"⚡ Load duration: {load_ms:.0f}ms")
                
                return True
            else:
                print(f"❌ No response in result: {result}")
                return False
        else:
            print(f"❌ Generation failed: {response.status_code}")
            print(f"Error: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"⏰ Timeout after 60 seconds - {model_name} might be slow to load")
        return False
    except Exception as e:
        print(f"❌ Generation error: {e}")
        return False

def test_cybex_ollama_interface():
    """Test interfaccia Ollama di CYBEX"""
    try:
        print("\n🔧 Testing CYBEX Ollama interface...")
        
        # Import CYBEX modules
        sys.path.insert(0, '.')
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.core.cybex_core import SystemConfig, LogManager
        
        # Initialize
        config = SystemConfig()
        log_manager = LogManager(config)
        ollama = OllamaInterface(log_manager)
        
        print("✅ CYBEX Ollama interface initialized")
        
        # Test connection
        if ollama.is_connected():
            print("✅ CYBEX connected to Ollama")
            
            # Get models
            models_info = ollama.get_available_models()
            if models_info.get('success'):
                models = models_info['models']
                print(f"✅ CYBEX found {len(models)} models")
                
                # Find Gemma 3 models
                gemma_models = [m for m in models if 'gemma3' in m.get('name', '').lower()]
                print(f"🤖 CYBEX found {len(gemma_models)} Gemma 3 models")
                
                # Test switching to Gemma 3
                if gemma_models:
                    test_model = gemma_models[0]['name']
                    print(f"🔄 Testing switch to {test_model}...")
                    
                    if ollama.switch_model(test_model):
                        print(f"✅ Successfully switched to {test_model}")
                        
                        # Test generation
                        print("🧠 Testing generation through CYBEX...")
                        response = ollama.generate_response("Hello! Are you working correctly?")
                        
                        if response and response.get('success'):
                            print("✅ CYBEX generation successful!")
                            print(f"💬 Response: {response.get('response', 'No response')}")
                            return True
                        else:
                            print(f"❌ CYBEX generation failed: {response.get('error', 'Unknown error')}")
                            return False
                    else:
                        print(f"❌ Failed to switch to {test_model}")
                        return False
                else:
                    print("❌ No Gemma 3 models found in CYBEX")
                    return False
            else:
                print(f"❌ CYBEX failed to get models: {models_info.get('error')}")
                return False
        else:
            print("❌ CYBEX not connected to Ollama")
            return False
            
    except Exception as e:
        print(f"❌ CYBEX interface error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🌅 CYBEX GEMMA 3 CONNECTION TEST")
    print("=" * 50)
    
    # Test 1: Basic Ollama connection
    gemma_models = test_ollama_connection()
    
    if not gemma_models:
        print("❌ No Gemma 3 models found. Exiting.")
        return
    
    # Test 2: Direct generation test
    success_count = 0
    for model in gemma_models:
        if test_gemma3_generation(model):
            success_count += 1
    
    print(f"\n📊 Direct test results: {success_count}/{len(gemma_models)} Gemma 3 models working")
    
    # Test 3: CYBEX interface test
    cybex_success = test_cybex_ollama_interface()
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"   • Ollama server: {'✅ Working' if gemma_models else '❌ Not working'}")
    print(f"   • Gemma 3 models: {success_count}/{len(gemma_models)} working")
    print(f"   • CYBEX interface: {'✅ Working' if cybex_success else '❌ Not working'}")
    
    if success_count > 0 and cybex_success:
        print("\n🎉 All systems operational! Gemma 3 should work in CYBEX.")
    else:
        print("\n⚠️ Issues detected. Check the errors above.")

if __name__ == "__main__":
    main()
