@echo off
echo ========================================
echo CYBEX ENTERPRISE - OLLAMA SETUP
echo ========================================
echo.

echo 🤖 Setting up Ollama AI Integration...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found! Please install Python first.
    pause
    exit /b 1
)

echo ✅ Python found
echo.

REM Install Ollama requirements
echo 📦 Installing Ollama integration requirements...
pip install -r requirements_ollama.txt

if errorlevel 1 (
    echo ❌ Failed to install Ollama requirements
    echo.
    echo 💡 Try running as administrator or check your internet connection
    pause
    exit /b 1
)

echo.
echo ✅ Ollama requirements installed successfully!
echo.

REM Check if Ollama is installed
echo 🔍 Checking for Ollama installation...
ollama --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama not found on system
    echo.
    echo 📥 OLLAMA INSTALLATION REQUIRED:
    echo.
    echo 1. Download Ollama from: https://ollama.ai/
    echo 2. Install Ollama following the instructions
    echo 3. Run this setup script again
    echo.
    echo 💡 Alternative: Use Windows Subsystem for Linux (WSL)
    echo    curl https://ollama.ai/install.sh ^| sh
    echo.
    pause
    exit /b 1
) else (
    echo ✅ Ollama found and installed
)

echo.
echo 🚀 Testing Ollama connection...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Ollama server not running
    echo.
    echo 🔧 STARTING OLLAMA SERVER:
    echo.
    echo Please run in another terminal:
    echo   ollama serve
    echo.
    echo Then test the connection with:
    echo   ollama list
    echo.
) else (
    echo ✅ Ollama server is running
    echo.
    echo 📋 Available models:
    curl -s http://localhost:11434/api/tags | python -m json.tool 2>nul || echo "   (Use 'ollama list' to see models)"
)

echo.
echo 🎯 RECOMMENDED MODELS TO DOWNLOAD:
echo.
echo For general use:
echo   ollama pull llama2
echo   ollama pull mistral
echo.
echo For coding:
echo   ollama pull codellama
echo   ollama pull deepseek-coder
echo.
echo For lightweight use:
echo   ollama pull llama2:7b
echo   ollama pull phi
echo.

echo ========================================
echo 🎉 OLLAMA SETUP COMPLETE!
echo ========================================
echo.
echo 🚀 CYBEX Enterprise now has AI capabilities:
echo    • Local AI model execution
echo    • Privacy-focused (no external API calls)
echo    • Multiple model support
echo    • Chat and text generation
echo.
echo 💡 Test with commands like:
echo    "Chiedi a Ollama come ottimizzare questo codice"
echo    "Stato connessione Ollama"
echo    "Scarica modello llama2"
echo.
echo 📖 NEXT STEPS:
echo 1. Start Ollama server: ollama serve
echo 2. Download a model: ollama pull llama2
echo 3. Launch CYBEX: CYBEX_LAUNCHER.bat
echo 4. Try: "Chiedi a Ollama ciao come stai?"
echo.
pause
