#!/usr/bin/env python3
"""
Cybex Basic Usage Examples
Demonstrates how to use Cybex programmatically
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from cybex.core.cybex_core import CybexCore
from cybex.modules.command_executor import CommandExecutor
from cybex.modules.ollama_interface import OllamaInterface


def example_basic_setup():
    """Example: Basic Cybex setup"""
    print("🚀 Example: Basic Cybex Setup")
    
    # Initialize core
    core = CybexCore()
    
    # Start session
    if core.start_session():
        print("✅ Cybex session started successfully")
        
        # Get system info
        info = core.get_system_info()
        print(f"📊 System: {info['platform']}")
        print(f"🐍 Python: {info['python_version']}")
        print(f"🎯 Mode: {info['mode']}")
        
        # End session
        core.end_session()
        print("✅ Session ended")
    else:
        print("❌ Failed to start session")


def example_command_execution():
    """Example: Safe command execution"""
    print("\n🔧 Example: Command Execution")
    
    # Initialize components
    core = CybexCore()
    executor = CommandExecutor(
        core.config_manager,
        core.security_manager,
        core.log_manager
    )
    
    # Test commands
    test_commands = [
        "hostname",
        "echo Hello Cybex",
        "dir" if core.system_type == "windows" else "ls -la"
    ]
    
    for command in test_commands:
        print(f"\n🔍 Testing command: {command}")
        
        # Check security
        is_safe, reason = core.security_manager.validate_command(command)
        print(f"🛡️  Security check: {'✅ Safe' if is_safe else '❌ Blocked'} - {reason}")
        
        if is_safe:
            # Execute command
            success, output, metadata = executor.execute_command(
                command, confirm_required=False
            )
            
            if success:
                print(f"✅ Success: {output[:100]}...")
            else:
                print(f"❌ Failed: {output}")


def example_security_validation():
    """Example: Security validation"""
    print("\n🛡️  Example: Security Validation")
    
    core = CybexCore()
    security = core.security_manager
    
    # Test various commands
    test_commands = [
        ("ls -la", "safe command"),
        ("mkdir test", "system altering"),
        ("rm -rf /tmp/test", "destructive"),
        ("sudo rm -rf /", "blocked"),
        ("shutdown -h now", "critical")
    ]
    
    for command, description in test_commands:
        level = security.get_security_level(command)
        is_destructive = security.is_destructive_operation(command)
        is_critical = security.is_critical_operation(command)
        
        print(f"\n📝 Command: {command}")
        print(f"   Description: {description}")
        print(f"   Security Level: {level}")
        print(f"   Destructive: {'Yes' if is_destructive else 'No'}")
        print(f"   Critical: {'Yes' if is_critical else 'No'}")


def example_configuration():
    """Example: Configuration management"""
    print("\n⚙️  Example: Configuration Management")
    
    core = CybexCore()
    config = core.config_manager
    
    # Show current configuration
    print("📋 Current Configuration:")
    print(f"   Mode: {config.get('mode')}")
    print(f"   Confirm Level: {config.get('confirm_level')}")
    print(f"   Ollama Model: {config.get('ollama.model')}")
    print(f"   Log Level: {config.get('logging.level')}")
    
    # Show security settings
    security_config = config.get_section('security')
    print(f"\n🔒 Security Settings:")
    print(f"   Sandbox Enabled: {security_config.get('enable_sandbox')}")
    print(f"   Max Command Length: {security_config.get('max_command_length')}")
    print(f"   Blocked Commands: {len(security_config.get('blocked_commands', []))}")


def example_logging():
    """Example: Logging system"""
    print("\n📝 Example: Logging System")
    
    core = CybexCore()
    log_manager = core.log_manager
    
    # Log some events
    log_manager.log_system_event("Example started", "Running basic usage examples")
    log_manager.log_command_execution("echo test", "test", True, 0.1)
    log_manager.log_security_event("TEST", "Example security event", "INFO")
    
    # Get log statistics
    stats = log_manager.get_log_stats()
    print(f"📊 Log Statistics:")
    if stats.get('exists'):
        print(f"   File Size: {stats.get('size_mb', 0):.2f} MB")
        print(f"   Lines: {stats.get('lines', 0)}")
        print(f"   Last Modified: {stats.get('modified', 'Unknown')}")
    else:
        print("   No log file found")
    
    # Show recent logs
    recent_logs = log_manager.get_recent_logs(5)
    if recent_logs:
        print(f"\n📋 Recent Log Entries:")
        for log in recent_logs[-3:]:  # Show last 3
            print(f"   {log}")


def example_ollama_interface():
    """Example: Ollama interface (requires Ollama server)"""
    print("\n🤖 Example: Ollama Interface")
    
    core = CybexCore()
    
    try:
        ollama = OllamaInterface(core.config_manager, core.log_manager)
        
        # Check server availability
        if ollama.is_server_available():
            print("✅ Ollama server is available")
            print(f"🎯 Current model: {ollama.model}")
            
            # Get available models
            models = ollama.get_available_models()
            print(f"📦 Available models: {len(models)}")
            for model in models[:3]:  # Show first 3
                print(f"   - {model}")
            
            # Example interaction (commented out to avoid actual API call)
            # response = ollama.generate_response("Hello, what can you help me with?")
            # if response.success:
            #     print(f"🤖 Response: {response.content[:100]}...")
            
        else:
            print("❌ Ollama server not available")
            print("   Please start Ollama with: ollama serve")
            
    except Exception as e:
        print(f"❌ Ollama interface error: {e}")


def main():
    """Run all examples"""
    print("🎯 Cybex Basic Usage Examples")
    print("=" * 50)
    
    try:
        example_basic_setup()
        example_command_execution()
        example_security_validation()
        example_configuration()
        example_logging()
        example_ollama_interface()
        
        print("\n🎉 All examples completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error running examples: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
