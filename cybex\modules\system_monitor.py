"""
System Monitor Module
Comprehensive system monitoring with real-time metrics and performance analytics
"""

import os
import time
import platform
import threading
import json
import sqlite3
from typing import Dict, List, Optional, Any, Callable, Tuple
from datetime import datetime, timedelta
from pathlib import Path
from enum import Enum
import logging

try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False

try:
    import py_cpuinfo
    CPUINFO_AVAILABLE = True
except ImportError:
    CPUINFO_AVAILABLE = False

try:
    import GPUtil
    GPUTIL_AVAILABLE = True
except ImportError:
    GPUTIL_AVAILABLE = False


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"


class SystemAlert:
    """System alert notification"""
    def __init__(self, component: str, message: str, level: AlertLevel, 
                value: float, threshold: float):
        self.component = component
        self.message = message
        self.level = level
        self.value = value
        self.threshold = threshold
        self.timestamp = datetime.now()
        self.id = f"{component}_{int(time.time())}"
        self.acknowledged = False


class SystemMonitor:
    """
    Comprehensive system monitoring with real-time metrics and analytics
    """
    
    def __init__(self, config_manager, log_manager):
        """Initialize system monitor"""
        self.config_manager = config_manager
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__)
        
        # Monitoring configuration
        self.monitoring_config = config_manager.get_section('monitoring')
        self.enabled = self.monitoring_config.get('enabled', True)
        self.interval = self.monitoring_config.get('interval', 30)
        self.cpu_threshold = self.monitoring_config.get('cpu_threshold', 80)
        self.memory_threshold = self.monitoring_config.get('memory_threshold', 85)
        self.disk_threshold = self.monitoring_config.get('disk_threshold', 90)
        self.network_threshold = self.monitoring_config.get('network_threshold', 80)
        self.alert_enabled = self.monitoring_config.get('alert_enabled', True)
        self.performance_history = self.monitoring_config.get('performance_history', 24)
        
        # System type
        self.system_type = platform.system().lower()
        self.is_windows = self.system_type == 'windows'
        
        # Database settings
        self.db_config = config_manager.get_section('database')
        self.db_enabled = self.db_config.get('enabled', True)
        self.db_path = Path(self.db_config.get('path', 'cybex/data/cybex.db'))
        
        # Ensure database directory exists
        if self.db_enabled:
            self.db_path.parent.mkdir(parents=True, exist_ok=True)
            self._init_database()
        
        # Current system state
        self.current_state: Dict[str, Any] = {}
        
        # Alert history
        self.alerts: List[SystemAlert] = []
        self.alert_callbacks: List[Callable] = []
        
        # Monitoring thread
        self.monitor_thread = None
        self.stop_monitoring = threading.Event()
        
        # Initialize system info
        self._init_system_info()
        
        # Start monitoring if enabled
        if self.enabled:
            self.start_monitoring()
    
    def _init_system_info(self) -> None:
        """Initialize static system information"""
        self.system_info = {
            'hostname': platform.node(),
            'system': platform.system(),
            'release': platform.release(),
            'version': platform.version(),
            'architecture': platform.machine(),
            'processor': platform.processor(),
            'boot_time': datetime.fromtimestamp(psutil.boot_time()).isoformat() if PSUTIL_AVAILABLE else None
        }
        
        # Add CPU info if available
        if CPUINFO_AVAILABLE:
            try:
                cpu_info = py_cpuinfo.get_cpu_info()
                self.system_info['cpu'] = {
                    'brand': cpu_info.get('brand_raw', 'Unknown'),
                    'cores_logical': cpu_info.get('count', 0),
                    'cores_physical': psutil.cpu_count(logical=False) if PSUTIL_AVAILABLE else 0,
                    'frequency': cpu_info.get('hz_advertised_friendly', 'Unknown'),
                    'architecture': cpu_info.get('arch', 'Unknown'),
                    'bits': cpu_info.get('bits', 0)
                }
            except:
                self.system_info['cpu'] = {'brand': 'Unknown'}
        
        # Add memory info if available
        if PSUTIL_AVAILABLE:
            mem = psutil.virtual_memory()
            self.system_info['memory'] = {
                'total': mem.total,
                'total_gb': round(mem.total / (1024**3), 2)
            }
        
        # Add disk info if available
        if PSUTIL_AVAILABLE:
            disks = []
            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)
                    disks.append({
                        'device': disk.device,
                        'mountpoint': disk.mountpoint,
                        'filesystem': disk.fstype,
                        'total': usage.total,
                        'total_gb': round(usage.total / (1024**3), 2)
                    })
                except:
                    pass
            self.system_info['disks'] = disks
        
        # Add GPU info if available
        if GPUTIL_AVAILABLE:
            try:
                gpus = GPUtil.getGPUs()
                self.system_info['gpus'] = [{
                    'name': gpu.name,
                    'memory_total': gpu.memoryTotal,
                    'driver': gpu.driver
                } for gpu in gpus]
            except:
                self.system_info['gpus'] = []
        
        # Add network info if available
        if PSUTIL_AVAILABLE:
            try:
                network_info = {}
                for interface, addresses in psutil.net_if_addrs().items():
                    network_info[interface] = []
                    for addr in addresses:
                        if addr.family == 2:  # IPv4
                            network_info[interface].append({
                                'address': addr.address,
                                'netmask': addr.netmask,
                                'family': 'IPv4'
                            })
                        elif addr.family == 23:  # IPv6
                            network_info[interface].append({
                                'address': addr.address,
                                'netmask': addr.netmask,
                                'family': 'IPv6'
                            })
                self.system_info['network'] = network_info
            except:
                self.system_info['network'] = {}
    
    def _init_database(self) -> None:
        """Initialize database for metrics storage"""
        if not self.db_enabled:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            AGtechdesigne = conn.AGtechdesigne()
            
            # Create metrics table
            AGtechdesigne.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TEXT NOT NULL,
                cpu_percent REAL,
                memory_percent REAL,
                disk_percent REAL,
                network_sent INTEGER,
                network_recv INTEGER,
                temperature REAL,
                processes INTEGER,
                metric_data TEXT
            )
            ''')
            
            # Create alerts table
            AGtechdesigne.execute('''
            CREATE TABLE IF NOT EXISTS system_alerts (
                id TEXT PRIMARY KEY,
                timestamp TEXT NOT NULL,
                component TEXT NOT NULL,
                message TEXT NOT NULL,
                level TEXT NOT NULL,
                value REAL,
                threshold REAL,
                acknowledged INTEGER DEFAULT 0
            )
            ''')
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database: {e}")
    
    def start_monitoring(self) -> bool:
        """Start system monitoring thread"""
        if not self.enabled or not PSUTIL_AVAILABLE:
            return False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            return True
        
        self.stop_monitoring.clear()
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            daemon=True
        )
        self.monitor_thread.start()
        
        self.logger.info("System monitoring started")
        return True
    
    def stop_monitoring(self) -> bool:
        """Stop system monitoring thread"""
        if not self.monitor_thread or not self.monitor_thread.is_alive():
            return True
        
        self.stop_monitoring.set()
        self.monitor_thread.join(timeout=5)
        
        self.logger.info("System monitoring stopped")
        return not self.monitor_thread.is_alive()
    
    def _monitoring_loop(self) -> None:
        """Main monitoring loop"""
        last_network = self._get_network_counters()
        
        while not self.stop_monitoring.is_set():
            try:
                # Collect metrics
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                
                # Disk usage (average across all disks)
                disk_percent = 0
                disk_count = 0
                for disk in psutil.disk_partitions():
                    try:
                        usage = psutil.disk_usage(disk.mountpoint)
                        disk_percent += usage.percent
                        disk_count += 1
                    except:
                        pass
                
                if disk_count > 0:
                    disk_percent /= disk_count
                
                # Network usage
                current_network = self._get_network_counters()
                network_sent = current_network['sent'] - last_network['sent']
                network_recv = current_network['recv'] - last_network['recv']
                last_network = current_network
                
                # Temperature (if available)
                temperature = self._get_temperature()
                
                # Process count
                process_count = len(psutil.pids())
                
                # Update current state
                self.current_state = {
                    'timestamp': datetime.now().isoformat(),
                    'cpu': {
                        'percent': cpu_percent,
                        'count': psutil.cpu_count(),
                        'frequency': psutil.cpu_freq().current if psutil.cpu_freq() else 0
                    },
                    'memory': {
                        'percent': memory_percent,
                        'used': memory.used,
                        'available': memory.available,
                        'total': memory.total
                    },
                    'disk': {
                        'percent': disk_percent
                    },
                    'network': {
                        'sent_bytes': network_sent,
                        'recv_bytes': network_recv
                    },
                    'temperature': temperature,
                    'processes': process_count
                }
                
                # Check for alerts
                self._check_alerts()
                
                # Store metrics in database
                self._store_metrics()
                
                # Wait for next interval
                self.stop_monitoring.wait(self.interval)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(self.interval)
    
    def _get_network_counters(self) -> Dict[str, int]:
        """Get network I/O counters"""
        try:
            counters = psutil.net_io_counters()
            return {
                'sent': counters.bytes_sent,
                'recv': counters.bytes_recv
            }
        except:
            return {'sent': 0, 'recv': 0}
    
    def _get_temperature(self) -> float:
        """Get system temperature if available"""
        try:
            temps = psutil.sensors_temperatures()
            if not temps:
                return 0.0
            
            # Get average temperature across all sensors
            all_temps = []
            for name, entries in temps.items():
                for entry in entries:
                    if entry.current > 0:
                        all_temps.append(entry.current)
            
            if all_temps:
                return sum(all_temps) / len(all_temps)
            return 0.0
        except:
            return 0.0
    
    def _check_alerts(self) -> None:
        """Check for alert conditions"""
        if not self.alert_enabled:
            return
        
        alerts = []
        
        # CPU alert
        cpu_percent = self.current_state.get('cpu', {}).get('percent', 0)
        if cpu_percent > self.cpu_threshold:
            level = AlertLevel.WARNING if cpu_percent < self.cpu_threshold + 10 else AlertLevel.CRITICAL
            alerts.append(SystemAlert(
                'cpu',
                f"CPU usage is high: {cpu_percent:.1f}%",
                level,
                cpu_percent,
                self.cpu_threshold
            ))
        
        # Memory alert
        memory_percent = self.current_state.get('memory', {}).get('percent', 0)
        if memory_percent > self.memory_threshold:
            level = AlertLevel.WARNING if memory_percent < self.memory_threshold + 10 else AlertLevel.CRITICAL
            alerts.append(SystemAlert(
                'memory',
                f"Memory usage is high: {memory_percent:.1f}%",
                level,
                memory_percent,
                self.memory_threshold
            ))
        
        # Disk alert
        disk_percent = self.current_state.get('disk', {}).get('percent', 0)
        if disk_percent > self.disk_threshold:
            level = AlertLevel.WARNING if disk_percent < self.disk_threshold + 5 else AlertLevel.CRITICAL
            alerts.append(SystemAlert(
                'disk',
                f"Disk usage is high: {disk_percent:.1f}%",
                level,
                disk_percent,
                self.disk_threshold
            ))
        
        # Process alerts if any found
        for alert in alerts:
            self.alerts.append(alert)
            self._store_alert(alert)
            
            # Notify callbacks
            for callback in self.alert_callbacks:
                try:
                    callback(alert)
                except Exception as e:
                    self.logger.error(f"Error in alert callback: {e}")
    
    def _store_metrics(self) -> None:
        """Store metrics in database"""
        if not self.db_enabled:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            AGtechdesigne = conn.AGtechdesigne()
            
            AGtechdesigne.execute('''
            INSERT INTO system_metrics (
                timestamp, cpu_percent, memory_percent, disk_percent,
                network_sent, network_recv, temperature, processes, metric_data
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.current_state.get('timestamp'),
                self.current_state.get('cpu', {}).get('percent', 0),
                self.current_state.get('memory', {}).get('percent', 0),
                self.current_state.get('disk', {}).get('percent', 0),
                self.current_state.get('network', {}).get('sent_bytes', 0),
                self.current_state.get('network', {}).get('recv_bytes', 0),
                self.current_state.get('temperature', 0),
                self.current_state.get('processes', 0),
                json.dumps(self.current_state)
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to store metrics: {e}")
    
    def _store_alert(self, alert: SystemAlert) -> None:
        """Store alert in database"""
        if not self.db_enabled:
            return
        
        try:
            conn = sqlite3.connect(self.db_path)
            AGtechdesigne = conn.AGtechdesigne()
            
            AGtechdesigne.execute('''
            INSERT OR REPLACE INTO system_alerts (
                id, timestamp, component, message, level, value, threshold, acknowledged
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                alert.id,
                alert.timestamp.isoformat(),
                alert.component,
                alert.message,
                alert.level.value,
                alert.value,
                alert.threshold,
                1 if alert.acknowledged else 0
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Failed to store alert: {e}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get static system information"""
        return self.system_info
    
    def get_current_state(self) -> Dict[str, Any]:
        """Get current system state"""
        return self.current_state
    
    def get_performance_history(self, hours: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get performance history for the specified period"""
        if not self.db_enabled:
            return []
        
        if hours is None:
            hours = self.performance_history
        
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row
            AGtechdesigne = conn.AGtechdesigne()
            
            # Calculate time threshold
            threshold = (datetime.now() - timedelta(hours=hours)).isoformat()
            
            AGtechdesigne.execute('''
            SELECT * FROM system_metrics
            WHERE timestamp > ?
            ORDER BY timestamp ASC
            ''', (threshold,))
            
            results = []
            for row in AGtechdesigne.fetchall():
                metric = dict(row)
                if 'metric_data' in metric and metric['metric_data']:
                    try:
                        metric['data'] = json.loads(metric['metric_data'])
                    except:
                        pass
                results.append(metric)
            
            conn.close()
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to get performance history: {e}")
            return []
    
    def get_alerts(self, include_acknowledged: bool = False) -> List[SystemAlert]:
        """Get active system alerts"""
        if include_acknowledged:
            return self.alerts
        else:
            return [a for a in self.alerts if not a.acknowledged]
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.acknowledged = True
                self._store_alert(alert)
                return True
        return False
    
    def clear_alerts(self) -> None:
        """Clear all alerts"""
        self.alerts = []
        
        if self.db_enabled:
            try:
                conn = sqlite3.connect(self.db_path)
                AGtechdesigne = conn.AGtechdesigne()
                AGtechdesigne.execute('DELETE FROM system_alerts')
                conn.commit()
                conn.close()
            except Exception as e:
                self.logger.error(f"Failed to clear alerts from database: {e}")
    
    def register_alert_callback(self, callback: Callable) -> None:
        """Register callback for alerts"""
        if callback not in self.alert_callbacks:
            self.alert_callbacks.append(callback)
    
    def unregister_alert_callback(self, callback: Callable) -> None:
        """Unregister alert callback"""
        if callback in self.alert_callbacks:
            self.alert_callbacks.remove(callback)
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary statistics"""
        history = self.get_performance_history(hours=1)
        
        if not history:
            return {}
        
        # Calculate averages
        cpu_values = [h.get('cpu_percent', 0) for h in history]
        memory_values = [h.get('memory_percent', 0) for h in history]
        disk_values = [h.get('disk_percent', 0) for h in history]
        
        return {
            'cpu': {
                'current': self.current_state.get('cpu', {}).get('percent', 0),
                'avg_1h': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                'max_1h': max(cpu_values) if cpu_values else 0,
                'min_1h': min(cpu_values) if cpu_values else 0
            },
            'memory': {
                'current': self.current_state.get('memory', {}).get('percent', 0),
                'avg_1h': sum(memory_values) / len(memory_values) if memory_values else 0,
                'max_1h': max(memory_values) if memory_values else 0,
                'min_1h': min(memory_values) if memory_values else 0
            },
            'disk': {
                'current': self.current_state.get('disk', {}).get('percent', 0),
                'avg_1h': sum(disk_values) / len(disk_values) if disk_values else 0,
                'max_1h': max(disk_values) if disk_values else 0,
                'min_1h': min(disk_values) if disk_values else 0
            },
            'uptime': self._get_uptime_str(),
            'processes': self.current_state.get('processes', 0),
            'alerts': len([a for a in self.alerts if not a.acknowledged])
        }
    
    def _get_uptime_str(self) -> str:
        """Get system uptime as a formatted string"""
        if not PSUTIL_AVAILABLE:
            return "Unknown"
        
        try:
            uptime_seconds = time.time() - psutil.boot_time()
            days, remainder = divmod(uptime_seconds, 86400)
            hours, remainder = divmod(remainder, 3600)
            minutes, seconds = divmod(remainder, 60)
            
            if days > 0:
                return f"{int(days)}d {int(hours)}h {int(minutes)}m"
            elif hours > 0:
                return f"{int(hours)}h {int(minutes)}m"
            else:
                return f"{int(minutes)}m {int(seconds)}s"
        except:
            return "Unknown"
    
    def get_top_processes(self, count: int = 5) -> List[Dict[str, Any]]:
        """Get top processes by CPU and memory usage"""
        if not PSUTIL_AVAILABLE:
            return []
        
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'username', 'cpu_percent', 'memory_percent']):
                try:
                    pinfo = proc.info
                    pinfo['cpu_percent'] = proc.cpu_percent(interval=0.1)
                    processes.append(pinfo)
                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    pass
            
            # Sort by CPU usage
            processes.sort(key=lambda p: p.get('cpu_percent', 0), reverse=True)
            
            return processes[:count]
            
        except Exception as e:
            self.logger.error(f"Failed to get top processes: {e}")
            return []
    
    def get_disk_usage(self) -> List[Dict[str, Any]]:
        """Get detailed disk usage information"""
        if not PSUTIL_AVAILABLE:
            return []
        
        try:
            disk_info = []
            for disk in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(disk.mountpoint)
                    disk_info.append({
                        'device': disk.device,
                        'mountpoint': disk.mountpoint,
                        'filesystem': disk.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percent': usage.percent,
                        'total_gb': round(usage.total / (1024**3), 2),
                        'used_gb': round(usage.used / (1024**3), 2),
                        'free_gb': round(usage.free / (1024**3), 2)
                    })
                except:
                    pass
            
            return disk_info
            
        except Exception as e:
            self.logger.error(f"Failed to get disk usage: {e}")
            return []
    
    def get_network_usage(self) -> Dict[str, Any]:
        """Get detailed network usage information"""
        if not PSUTIL_AVAILABLE:
            return {}
        
        try:
            # Get network I/O counters
            net_io = psutil.net_io_counters(pernic=True)
            
            # Get network interfaces
            net_if = psutil.net_if_addrs()
            
            # Combine information
            network_info = {}
            for interface in net_if:
                if interface in net_io:
                    counters = net_io[interface]
                    addresses = []
                    
                    for addr in net_if[interface]:
                        if addr.family == 2:  # IPv4
                            addresses.append({
                                'address': addr.address,
                                'netmask': addr.netmask,
                                'family': 'IPv4'
                            })
                        elif addr.family == 23:  # IPv6
                            addresses.append({
                                'address': addr.address,
                                'netmask': addr.netmask,
                                'family': 'IPv6'
                            })
                    
                    network_info[interface] = {
                        'addresses': addresses,
                        'bytes_sent': counters.bytes_sent,
                        'bytes_recv': counters.bytes_recv,
                        'packets_sent': counters.packets_sent,
                        'packets_recv': counters.packets_recv,
                        'errin': counters.errin,
                        'errout': counters.errout,
                        'dropin': counters.dropin,
                        'dropout': counters.dropout
                    }
            
            return network_info
            
        except Exception as e:
            self.logger.error(f"Failed to get network usage: {e}")
            return {}
    
    def analyze_performance(self) -> Dict[str, Any]:
        """Analyze system performance and provide recommendations"""
        if not self.current_state:
            return {'status': 'No data available'}
        
        analysis = {
            'timestamp': datetime.now().isoformat(),
            'status': 'normal',
            'issues': [],
            'recommendations': []
        }
        
        # CPU analysis
        cpu_percent = self.current_state.get('cpu', {}).get('percent', 0)
        if cpu_percent > 90:
            analysis['status'] = 'critical'
            analysis['issues'].append(f"Critical CPU usage: {cpu_percent:.1f}%")
            analysis['recommendations'].append("Check for runaway processes and terminate if necessary")
        elif cpu_percent > 80:
            analysis['status'] = 'warning'
            analysis['issues'].append(f"High CPU usage: {cpu_percent:.1f}%")
            analysis['recommendations'].append("Consider closing unnecessary applications")
        
        # Memory analysis
        memory_percent = self.current_state.get('memory', {}).get('percent', 0)
        if memory_percent > 90:
            analysis['status'] = 'critical'
            analysis['issues'].append(f"Critical memory usage: {memory_percent:.1f}%")
            analysis['recommendations'].append("Check for memory leaks and close memory-intensive applications")
        elif memory_percent > 80:
            if analysis['status'] != 'critical':
                analysis['status'] = 'warning'
            analysis['issues'].append(f"High memory usage: {memory_percent:.1f}%")
            analysis['recommendations'].append("Consider adding more RAM or closing unnecessary applications")
        
        # Disk analysis
        disk_percent = self.current_state.get('disk', {}).get('percent', 0)
        if disk_percent > 90:
            analysis['status'] = 'critical'
            analysis['issues'].append(f"Critical disk usage: {disk_percent:.1f}%")
            analysis['recommendations'].append("Free up disk space by removing unnecessary files")
        elif disk_percent > 80:
            if analysis['status'] != 'critical':
                analysis['status'] = 'warning'
            analysis['issues'].append(f"High disk usage: {disk_percent:.1f}%")
            analysis['recommendations'].append("Consider cleaning temporary files and old logs")
        
        # Process analysis
        if PSUTIL_AVAILABLE:
            top_processes = self.get_top_processes(3)
            if top_processes:
                high_cpu_processes = [p for p in top_processes if p.get('cpu_percent', 0) > 50]
                if high_cpu_processes:
                    process_names = ", ".join([f"{p['name']} ({p['pid']})" for p in high_cpu_processes])
                    analysis['issues'].append(f"High CPU processes: {process_names}")
                    analysis['recommendations'].append("Investigate these processes for potential optimization")
        
        return analysis
