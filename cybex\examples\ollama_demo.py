#!/usr/bin/env python3
"""
CYBEX Enterprise Ollama Demo
Demonstrates Ollama AI integration capabilities
"""

import sys
from pathlib import Path
import time

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from cybex.modules.ollama_integration import OllamaIntegration
from cybex.modules.log_manager import LogManager


def demo_ollama_connection():
    """Demonstrate Ollama connection and status"""
    print("🤖 CYBEX ENTERPRISE - OLLAMA AI DEMO")
    print("=" * 60)
    
    # Initialize Ollama integration
    log_manager = LogManager()
    ollama = OllamaIntegration(log_manager=log_manager)
    
    # Check connection status
    print("\n🔌 Testing Ollama Connection...")
    status = ollama.get_connection_status()
    
    print(f"Connection: {'✅ Connected' if status['connected'] else '❌ Disconnected'}")
    print(f"Server URL: {status['base_url']}")
    print(f"Available Models: {status['models_count']}")
    
    if status['available_models']:
        print("\n📋 Available Models:")
        for i, model in enumerate(status['available_models'], 1):
            current = " ← Current" if model == status['current_model'] else ""
            print(f"  {i}. {model}{current}")
    else:
        print("\n⚠️  No models available")
        print("💡 Download a model with: ollama pull llama2")
        return False
    
    return status['connected']


def demo_ollama_chat(ollama):
    """Demonstrate Ollama chat functionality"""
    print("\n💬 OLLAMA CHAT DEMO")
    print("-" * 40)
    
    # Test messages
    test_messages = [
        "Hello! Can you introduce yourself?",
        "What are the main features of Python programming language?",
        "How can I optimize system performance?",
        "Explain what CYBEX Enterprise is based on our conversation."
    ]
    
    for i, message in enumerate(test_messages, 1):
        print(f"\n🧑 Test Message {i}: {message}")
        print("🤖 AI Response:")
        
        result = ollama.chat(message)
        
        if result['success']:
            response = result['response']
            print(f"   {response[:200]}{'...' if len(response) > 200 else ''}")
            print(f"   ⏱️ Response time: {result.get('total_duration', 0) / 1000000:.2f}ms")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        time.sleep(1)  # Brief pause between requests


def demo_ollama_generation(ollama):
    """Demonstrate Ollama text generation"""
    print("\n✨ OLLAMA TEXT GENERATION DEMO")
    print("-" * 40)
    
    # Test prompts
    test_prompts = [
        "Write a short poem about artificial intelligence:",
        "Create a Python function to calculate fibonacci numbers:",
        "Explain the benefits of using local AI models:",
        "List 5 cybersecurity best practices:"
    ]
    
    for i, prompt in enumerate(test_prompts, 1):
        print(f"\n📝 Prompt {i}: {prompt}")
        print("🤖 Generated Text:")
        
        result = ollama.generate(prompt)
        
        if result['success']:
            response = result['response']
            print(f"   {response[:300]}{'...' if len(response) > 300 else ''}")
        else:
            print(f"   ❌ Error: {result['error']}")
        
        time.sleep(1)


def demo_model_management(ollama):
    """Demonstrate model management"""
    print("\n🔧 MODEL MANAGEMENT DEMO")
    print("-" * 40)
    
    # Get current model info
    print("\n📊 Current Model Information:")
    result = ollama.get_model_info()
    
    if result['success']:
        model_info = result['model_info']
        print(f"   Model: {ollama.current_model}")
        print(f"   Info available: {'✅' if model_info else '❌'}")
    else:
        print(f"   ❌ Error: {result['error']}")
    
    # List available models
    status = ollama.get_connection_status()
    print(f"\n📋 Available Models ({status['models_count']}):")
    for model in status['available_models']:
        current = " ← Current" if model == status['current_model'] else ""
        print(f"   • {model}{current}")


def demo_chat_history(ollama):
    """Demonstrate chat history"""
    print("\n📚 CHAT HISTORY DEMO")
    print("-" * 40)
    
    history = ollama.get_chat_history()
    
    print(f"Chat History: {len(history)} messages")
    
    if history:
        print("\nRecent Messages:")
        for i, msg in enumerate(history[-5:], 1):  # Last 5 messages
            role_icon = "🧑" if msg['role'] == 'user' else "🤖"
            content = msg['content'][:100] + "..." if len(msg['content']) > 100 else msg['content']
            print(f"   {i}. {role_icon} {msg['role']}: {content}")
    else:
        print("   No chat history available")


def main():
    """Main demo function"""
    try:
        # Test connection
        if not demo_ollama_connection():
            print("\n❌ Cannot proceed without Ollama connection")
            print("\n🔧 SETUP INSTRUCTIONS:")
            print("1. Install Ollama: https://ollama.ai/")
            print("2. Start server: ollama serve")
            print("3. Download model: ollama pull llama2")
            print("4. Run this demo again")
            return
        
        # Initialize for demos
        log_manager = LogManager()
        ollama = OllamaIntegration(log_manager=log_manager)
        
        # Run demos
        demo_ollama_chat(ollama)
        demo_ollama_generation(ollama)
        demo_model_management(ollama)
        demo_chat_history(ollama)
        
        print("\n" + "=" * 60)
        print("🎉 OLLAMA DEMO COMPLETED!")
        print("=" * 60)
        print("\n🚀 CYBEX Enterprise Ollama Integration Features:")
        print("✅ Local AI model execution")
        print("✅ Privacy-focused (no external API calls)")
        print("✅ Multiple model support")
        print("✅ Chat with context awareness")
        print("✅ Text generation capabilities")
        print("✅ Model management (pull, switch, info)")
        print("✅ Performance monitoring")
        print("✅ Chat history tracking")
        
        print("\n💡 Try these natural language commands in CYBEX:")
        print('• "Chiedi a Ollama come ottimizzare il codice Python"')
        print('• "Stato connessione Ollama"')
        print('• "Scarica modello mistral"')
        print('• "Cambia modello a llama2"')
        print('• "Genera testo su intelligenza artificiale"')
        
        print("\n🎯 CYBEX + Ollama = Powerful Local AI Assistant!")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
