#!/usr/bin/env python3
"""
Simple SVP Test
Basic test of Cybex SVP initialization
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test basic imports"""
    print("🔍 Testing imports...")
    
    try:
        from cybex import __version__, SVP_PHASE
        print(f"✅ Cybex version: {__version__}")
        print(f"✅ SVP Phase enabled: {SVP_PHASE}")
        
        from cybex.core.cybex_core import Cybex<PERSON>ore
        print("✅ CybexCore imported")
        
        from cybex.modules.system_monitor import SystemMonitor
        print("✅ SystemMonitor imported")
        
        from cybex.modules.disk_manager import DiskManager
        print("✅ DiskManager imported")
        
        from cybex.modules.database_manager import DatabaseManager
        print("✅ DatabaseManager imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_basic_initialization():
    """Test basic initialization"""
    print("\n🚀 Testing basic initialization...")
    
    try:
        from cybex.core.cybex_core import Cybex<PERSON>ore
        
        # Initialize core
        core = CybexCore()
        print("✅ Core initialized")
        
        # Check basic properties
        print(f"✅ System type: {core.system_type}")
        print(f"✅ Mode: {core.mode.value}")
        print(f"✅ Confirmation level: {core.confirm_level.value}")
        
        # Check SVP modules
        if hasattr(core, 'system_monitor') and core.system_monitor:
            print("✅ System monitor available")
        else:
            print("⚠️  System monitor not available")
        
        if hasattr(core, 'database_manager') and core.database_manager:
            print("✅ Database manager available")
        else:
            print("⚠️  Database manager not available")
        
        return True
        
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_loading():
    """Test configuration loading"""
    print("\n⚙️  Testing configuration loading...")
    
    try:
        from cybex.modules.config_manager import ConfigManager
        
        config = ConfigManager()
        print("✅ Config manager initialized")
        
        # Test basic config access
        mode = config.get('mode')
        print(f"✅ Mode from config: {mode}")
        
        # Test SVP config sections
        monitoring = config.get_section('monitoring')
        print(f"✅ Monitoring config: enabled={monitoring.get('enabled', False)}")
        
        disk_mgmt = config.get_section('disk_management')
        print(f"✅ Disk management config: enabled={disk_mgmt.get('enabled', False)}")
        
        database = config.get_section('database')
        print(f"✅ Database config: enabled={database.get('enabled', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run simple SVP tests"""
    print("🎯 Cybex SVP Simple Test")
    print("=" * 30)
    
    success = True
    
    # Test imports
    if not test_imports():
        success = False
    
    # Test config loading
    if not test_config_loading():
        success = False
    
    # Test basic initialization
    if not test_basic_initialization():
        success = False
    
    if success:
        print("\n🎉 All basic tests passed!")
        print("SVP components are properly initialized.")
    else:
        print("\n❌ Some tests failed!")
        print("Check the error messages above.")


if __name__ == "__main__":
    main()
