# 🌐 CYBEX ENTERPRISE - WEB BROWSING GUIDE

## 🎯 **WEB BROWSING CAPABILITIES OVERVIEW**

CYBEX Enterprise ora include capacità avanzate di navigazione web che permettono all'agente AI di:
- **Cercare informazioni** sul web usando motori di ricerca
- **Recuperare contenuti** da pagine web specifiche
- **Analizzare siti web** per SEO, qualità e aspetti tecnici
- **Gestire cronologia** di navigazione e ricerche

---

## 🚀 **INSTALLAZIONE DIPENDENZE WEB**

### **Installazione Automatica:**
```bash
scripts\install_web_dependencies.bat
```

### **Installazione Manuale:**
```bash
pip install -r requirements_web.txt
```

### **Dipendenze Richieste:**
- **requests**: HTTP requests
- **beautifulsoup4**: HTML parsing
- **selenium**: Browser automation
- **html2text**: HTML to markdown conversion
- **lxml**: XML/HTML processing

---

## 🛠️ **STRUMENTI WEB DISPONIBILI**

### **1. 🔍 Web Search (`web_search`)**
Cerca informazioni sul web usando motori di ricerca.

**Parametri:**
- `query` (richiesto): Query di ricerca
- `engine` (opzionale): Motore di ricerca (google, bing, duckduckgo)
- `max_results` (opzionale): Numero massimo risultati (default: 10)

**Esempio:**
```python
result = agent_tools.execute_tool('web_search', {
    'query': 'artificial intelligence trends 2024',
    'engine': 'google',
    'max_results': 5
})
```

### **2. 📄 Fetch Webpage (`fetch_webpage`)**
Recupera e analizza il contenuto di una pagina web specifica.

**Parametri:**
- `url` (richiesto): URL della pagina web
- `use_browser` (opzionale): Usa browser per JavaScript (default: False)

**Esempio:**
```python
result = agent_tools.execute_tool('fetch_webpage', {
    'url': 'https://example.com',
    'use_browser': False
})
```

### **3. 📊 Analyze Webpage (`analyze_webpage`)**
Analizza una pagina web per SEO, qualità del contenuto e aspetti tecnici.

**Parametri:**
- `url` (richiesto): URL della pagina web
- `analysis_type` (opzionale): Tipo di analisi (general, seo, content, technical)

**Esempio:**
```python
result = agent_tools.execute_tool('analyze_webpage', {
    'url': 'https://example.com',
    'analysis_type': 'seo'
})
```

### **4. 📚 Browsing History (`browsing_history`)**
Recupera la cronologia di navigazione e ricerche.

**Parametri:** Nessuno

**Esempio:**
```python
result = agent_tools.execute_tool('browsing_history')
```

---

## 💬 **COMANDI LINGUAGGIO NATURALE**

### **🔍 Ricerca Web:**
- `"Cerca informazioni su intelligenza artificiale"`
- `"Search for python tutorials"`
- `"Trova notizie su tecnologia"`
- `"Ricerca machine learning"`

### **📄 Apertura Siti Web:**
- `"Apri sito google.com"`
- `"Open website https://github.com"`
- `"Vai su facebook.com"`
- `"Visita stackoverflow.com"`

### **📊 Analisi Siti Web:**
- `"Analizza sito web google.com per SEO"`
- `"Analyze website example.com for content quality"`
- `"Esamina pagina github.com aspetti tecnici"`
- `"Studia sito web per performance"`

### **📚 Cronologia:**
- `"Mostra cronologia navigazione"`
- `"Show browsing history"`
- `"Lista ricerche effettuate"`

---

## 🎯 **ESEMPI PRATICI**

### **Esempio 1: Ricerca e Analisi**
```
Utente: "Cerca informazioni su AI e poi analizza il primo risultato"

CYBEX:
1. Esegue web_search per "AI"
2. Recupera i risultati
3. Usa fetch_webpage sul primo risultato
4. Fornisce analisi completa
```

### **Esempio 2: Analisi SEO Completa**
```
Utente: "Analizza il sito web della mia azienda per SEO"

CYBEX:
1. Usa fetch_webpage per recuperare contenuto
2. Esegue analyze_webpage con tipo "seo"
3. Fornisce report SEO dettagliato con raccomandazioni
```

### **Esempio 3: Ricerca Competitiva**
```
Utente: "Cerca competitor nel settore AI e analizza i loro siti"

CYBEX:
1. Esegue web_search per "AI companies"
2. Recupera lista competitor
3. Analizza siti web principali
4. Fornisce report comparativo
```

---

## ⚙️ **CONFIGURAZIONE AVANZATA**

### **Browser Settings:**
```python
# Nel file web_browsing_agent.py
config = {
    'user_agent': 'CYBEX-Enterprise-Agent/1.0',
    'timeout': 30,
    'max_retries': 3,
    'enable_javascript': True,
    'enable_images': False,
    'max_page_size': 10 * 1024 * 1024  # 10MB
}
```

### **Search Engine Configuration:**
```python
search_engines = {
    'google': 'https://www.google.com/search?q={}',
    'bing': 'https://www.bing.com/search?q={}',
    'duckduckgo': 'https://duckduckgo.com/?q={}'
}
```

### **Cache Settings:**
```python
# Cache per migliorare performance
max_cache_size = 100  # Numero pagine in cache
cache_ttl = 3600      # TTL cache in secondi (1 ora)
```

---

## 🛡️ **SICUREZZA E PRIVACY**

### **Misure di Sicurezza:**
- ✅ **URL Validation**: Validazione URL prima del fetch
- ✅ **Content Size Limits**: Limite dimensione contenuto (10MB)
- ✅ **Timeout Protection**: Timeout per evitare hang
- ✅ **User Agent**: User agent identificativo
- ✅ **Security Headers**: Analisi header di sicurezza

### **Privacy:**
- ✅ **Local Processing**: Elaborazione locale dei contenuti
- ✅ **No Tracking**: Nessun tracking degli utenti
- ✅ **Cache Control**: Controllo cache locale
- ✅ **History Management**: Gestione cronologia locale

---

## 📊 **METRICHE E MONITORING**

### **Performance Metrics:**
- **Load Time**: Tempo caricamento pagine
- **Success Rate**: Tasso successo richieste
- **Cache Hit Rate**: Efficienza cache
- **Error Rate**: Tasso errori

### **Usage Statistics:**
- **Total Searches**: Ricerche totali effettuate
- **Pages Fetched**: Pagine recuperate
- **Analysis Performed**: Analisi eseguite
- **Cache Size**: Dimensione cache attuale

---

## 🔧 **TROUBLESHOOTING**

### **Problemi Comuni:**

#### **1. Chrome/Selenium Non Funziona**
```
Errore: WebDriver not found
Soluzione: Installare Google Chrome o aggiornare webdriver-manager
```

#### **2. Timeout su Siti Lenti**
```
Errore: Request timeout
Soluzione: Aumentare timeout in configurazione o usare use_browser=True
```

#### **3. Contenuto JavaScript Non Caricato**
```
Errore: Contenuto incompleto
Soluzione: Usare use_browser=True per siti JavaScript-heavy
```

#### **4. Blocco Anti-Bot**
```
Errore: Access denied / 403
Soluzione: Cambiare user agent o usare browser mode
```

---

## 🎉 **ESEMPI DI UTILIZZO AVANZATO**

### **Monitoraggio Competitivo:**
```python
# Monitora competitor automaticamente
competitors = ["competitor1.com", "competitor2.com"]
for site in competitors:
    analysis = agent_tools.execute_tool('analyze_webpage', {
        'url': site,
        'analysis_type': 'technical'
    })
    # Salva risultati per confronto
```

### **SEO Audit Automatico:**
```python
# Audit SEO completo
pages = ["homepage", "products", "about", "contact"]
for page in pages:
    seo_analysis = agent_tools.execute_tool('analyze_webpage', {
        'url': f"https://mysite.com/{page}",
        'analysis_type': 'seo'
    })
    # Genera report SEO
```

### **Content Research:**
```python
# Ricerca contenuti per topic
topics = ["AI trends", "machine learning", "automation"]
for topic in topics:
    results = agent_tools.execute_tool('web_search', {
        'query': topic,
        'max_results': 10
    })
    # Analizza risultati per content ideas
```

---

## 🚀 **PROSSIMI SVILUPPI**

### **Funzionalità Future:**
- **🔄 Auto-refresh**: Monitoraggio automatico cambiamenti
- **📱 Mobile Analysis**: Analisi versioni mobile
- **🌍 Multi-language**: Supporto ricerche multilingua
- **📈 Trend Analysis**: Analisi trend nel tempo
- **🤖 AI Content Generation**: Generazione contenuti basata su ricerche

---

## 📞 **SUPPORTO**

Per problemi o domande sulle funzionalità web:
1. **Controlla log**: `cybex/logs/` per errori dettagliati
2. **Testa connessione**: Verifica connettività internet
3. **Aggiorna dipendenze**: `pip install -r requirements_web.txt --upgrade`
4. **Reinstalla Chrome**: Per problemi Selenium

**CYBEX Enterprise Web Browsing è ora operativo! 🌐✨**
