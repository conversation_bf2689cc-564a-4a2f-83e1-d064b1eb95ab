"""
Agent Planner Module
Multi-step planning system with goal decomposition and dependency management
"""

import json
import time
import uuid
from typing import Dict, List, Optional, Tuple, Any
from enum import Enum
from dataclasses import dataclass, asdict
from datetime import datetime


class TaskStatus(Enum):
    """Task execution status"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    SKIPPED = "skipped"
    CANCELLED = "cancelled"


class TaskPriority(Enum):
    """Task priority levels"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class AgentTask:
    """Individual task in an agent plan"""
    id: str
    name: str
    description: str
    command: str
    expected_output: str
    dependencies: List[str]
    priority: TaskPriority
    timeout: int
    retry_count: int
    rollback_commands: List[str]
    validation_commands: List[str]
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    output: Optional[str] = None
    execution_time: float = 0.0


@dataclass
class AgentPlan:
    """Complete execution plan for agent mode"""
    id: str
    name: str
    description: str
    goal: str
    tasks: List[AgentTask]
    created_at: datetime
    estimated_duration: int
    risk_level: str
    requires_confirmation: bool
    rollback_plan: List[str]
    success_criteria: List[str]
    status: TaskStatus = TaskStatus.PENDING


class AgentPlanner:
    """
    Multi-step planning system for autonomous agent operations
    """
    
    def __init__(self, config_manager, security_manager, log_manager, ollama_interface):
        """Initialize agent planner"""
        self.config_manager = config_manager
        self.security_manager = security_manager
        self.log_manager = log_manager
        self.ollama_interface = ollama_interface
        self.logger = log_manager.get_logger(__name__)
        
        # Agent configuration
        self.agent_config = config_manager.get_section('agent')
        self.max_steps = self.agent_config.get('max_steps', 10)
        self.step_timeout = self.agent_config.get('step_timeout', 60)
        self.auto_confirm_safe = self.agent_config.get('auto_confirm_safe', False)
        self.plan_review_required = self.agent_config.get('plan_review_required', True)
        
        # Planning templates
        self.planning_templates = self._load_planning_templates()
        
        # Active plans
        self.active_plans: Dict[str, AgentPlan] = {}
        
    def _load_planning_templates(self) -> Dict[str, Dict]:
        """Load planning templates for common tasks"""
        return {
            'system_optimization': {
                'description': 'Optimize system performance',
                'tasks': [
                    'Check system resources',
                    'Identify performance bottlenecks',
                    'Clean temporary files',
                    'Optimize startup programs',
                    'Update system drivers',
                    'Verify improvements'
                ]
            },
            'security_audit': {
                'description': 'Perform security audit',
                'tasks': [
                    'Check system updates',
                    'Scan for malware',
                    'Review user accounts',
                    'Check firewall settings',
                    'Audit file permissions',
                    'Generate security report'
                ]
            },
            'disk_cleanup': {
                'description': 'Clean and optimize disk space',
                'tasks': [
                    'Analyze disk usage',
                    'Clean temporary files',
                    'Empty recycle bin',
                    'Clean browser cache',
                    'Remove old log files',
                    'Defragment if needed'
                ]
            },
            'system_maintenance': {
                'description': 'Perform routine system maintenance',
                'tasks': [
                    'Check system health',
                    'Update software',
                    'Clean temporary files',
                    'Check disk health',
                    'Backup important data',
                    'Restart services if needed'
                ]
            }
        }
    
    def create_plan_from_goal(self, goal: str, context: Optional[Dict] = None) -> AgentPlan:
        """
        Create execution plan from high-level goal using AI
        """
        self.logger.info(f"Creating plan for goal: {goal}")
        
        # Prepare context for AI planning
        planning_context = {
            'goal': goal,
            'system_info': context.get('system_info', '') if context else '',
            'available_templates': list(self.planning_templates.keys()),
            'max_steps': self.max_steps,
            'security_level': self.config_manager.get('confirm_level', 'strict')
        }
        
        # Generate plan using AI
        plan_response = self._generate_plan_with_ai(planning_context)
        
        if not plan_response:
            # Fallback to template-based planning
            plan_response = self._generate_plan_from_template(goal)
        
        # Create AgentPlan object
        plan = self._create_agent_plan(goal, plan_response)
        
        # Validate and optimize plan
        plan = self._validate_and_optimize_plan(plan)
        
        # Store active plan
        self.active_plans[plan.id] = plan
        
        self.logger.info(f"Created plan '{plan.name}' with {len(plan.tasks)} tasks")
        return plan
    
    def _generate_plan_with_ai(self, context: Dict) -> Optional[Dict]:
        """Generate execution plan using AI"""
        try:
            planning_prompt = f"""
Crea un piano di esecuzione dettagliato per il seguente obiettivo:

OBIETTIVO: {context['goal']}
SISTEMA: {context.get('system_info', 'Non specificato')}
MAX STEPS: {context['max_steps']}
LIVELLO SICUREZZA: {context['security_level']}

Genera un piano JSON con questa struttura:
{{
    "name": "Nome del piano",
    "description": "Descrizione dettagliata",
    "estimated_duration": 300,
    "risk_level": "low|medium|high",
    "requires_confirmation": true,
    "tasks": [
        {{
            "name": "Nome task",
            "description": "Descrizione task",
            "command": "comando da eseguire",
            "expected_output": "output atteso",
            "priority": "normal|high|critical",
            "timeout": 60,
            "retry_count": 2,
            "rollback_commands": ["comando rollback"],
            "validation_commands": ["comando validazione"]
        }}
    ],
    "success_criteria": ["criterio 1", "criterio 2"],
    "rollback_plan": ["step 1", "step 2"]
}}

Assicurati che:
1. I comandi siano sicuri e appropriati per il sistema
2. Ogni task abbia dipendenze chiare
3. Sia incluso un piano di rollback
4. I criteri di successo siano verificabili
"""
            
            response = self.ollama_interface.generate_response(planning_prompt, context)
            
            if response.success:
                # Try to extract JSON from response
                plan_json = self._extract_json_from_response(response.content)
                if plan_json:
                    return plan_json
                    
        except Exception as e:
            self.logger.error(f"AI planning failed: {e}")
        
        return None
    
    def _extract_json_from_response(self, response: str) -> Optional[Dict]:
        """Extract JSON from AI response"""
        try:
            # Look for JSON block in response
            start_markers = ['{', '```json\n{', '```\n{']
            end_markers = ['}', '}\n```', '}\n```']
            
            for start_marker, end_marker in zip(start_markers, end_markers):
                start_idx = response.find(start_marker)
                if start_idx != -1:
                    end_idx = response.rfind(end_marker)
                    if end_idx != -1:
                        json_str = response[start_idx:end_idx + len(end_marker.split('\n')[0])]
                        # Clean up the JSON string
                        json_str = json_str.replace('```json', '').replace('```', '').strip()
                        return json.loads(json_str)
            
            # Try to parse entire response as JSON
            return json.loads(response.strip())
            
        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse JSON from AI response: {e}")
            return None
    
    def _generate_plan_from_template(self, goal: str) -> Dict:
        """Generate plan from predefined templates"""
        # Simple keyword matching to select template
        goal_lower = goal.lower()
        
        if any(word in goal_lower for word in ['optimize', 'performance', 'speed', 'slow']):
            template_key = 'system_optimization'
        elif any(word in goal_lower for word in ['security', 'audit', 'secure', 'vulnerability']):
            template_key = 'security_audit'
        elif any(word in goal_lower for word in ['clean', 'disk', 'space', 'storage']):
            template_key = 'disk_cleanup'
        else:
            template_key = 'system_maintenance'
        
        template = self.planning_templates[template_key]
        
        # Convert template to plan format
        plan_data = {
            'name': f"{template['description']} Plan",
            'description': f"Automated plan for: {goal}",
            'estimated_duration': len(template['tasks']) * 60,
            'risk_level': 'medium',
            'requires_confirmation': True,
            'tasks': [],
            'success_criteria': ['All tasks completed successfully'],
            'rollback_plan': ['Review changes', 'Restore if needed']
        }
        
        # Convert template tasks to detailed tasks
        for i, task_name in enumerate(template['tasks']):
            task = {
                'name': task_name,
                'description': f"Execute: {task_name}",
                'command': f"# {task_name} - to be implemented",
                'expected_output': 'Success',
                'priority': 'normal',
                'timeout': 60,
                'retry_count': 2,
                'rollback_commands': [],
                'validation_commands': []
            }
            plan_data['tasks'].append(task)
        
        return plan_data
    
    def _create_agent_plan(self, goal: str, plan_data: Dict) -> AgentPlan:
        """Create AgentPlan object from plan data"""
        plan_id = str(uuid.uuid4())
        
        # Create tasks
        tasks = []
        for i, task_data in enumerate(plan_data.get('tasks', [])):
            task = AgentTask(
                id=str(uuid.uuid4()),
                name=task_data.get('name', f'Task {i+1}'),
                description=task_data.get('description', ''),
                command=task_data.get('command', ''),
                expected_output=task_data.get('expected_output', ''),
                dependencies=task_data.get('dependencies', []),
                priority=TaskPriority[task_data.get('priority', 'normal').upper()],
                timeout=task_data.get('timeout', self.step_timeout),
                retry_count=task_data.get('retry_count', 2),
                rollback_commands=task_data.get('rollback_commands', []),
                validation_commands=task_data.get('validation_commands', []),
                status=TaskStatus.PENDING,
                created_at=datetime.now()
            )
            tasks.append(task)
        
        # Create plan
        plan = AgentPlan(
            id=plan_id,
            name=plan_data.get('name', f'Plan for: {goal}'),
            description=plan_data.get('description', ''),
            goal=goal,
            tasks=tasks,
            created_at=datetime.now(),
            estimated_duration=plan_data.get('estimated_duration', len(tasks) * 60),
            risk_level=plan_data.get('risk_level', 'medium'),
            requires_confirmation=plan_data.get('requires_confirmation', True),
            rollback_plan=plan_data.get('rollback_plan', []),
            success_criteria=plan_data.get('success_criteria', [])
        )
        
        return plan
    
    def _validate_and_optimize_plan(self, plan: AgentPlan) -> AgentPlan:
        """Validate and optimize execution plan"""
        # Validate task commands
        for task in plan.tasks:
            if task.command and not task.command.startswith('#'):
                is_safe, reason = self.security_manager.validate_command(task.command)
                if not is_safe:
                    self.logger.warning(f"Task '{task.name}' has unsafe command: {reason}")
                    task.command = f"# BLOCKED: {task.command} - {reason}"
        
        # Sort tasks by priority and dependencies
        plan.tasks = self._sort_tasks_by_dependencies(plan.tasks)
        
        # Update risk level based on commands
        plan.risk_level = self._calculate_plan_risk(plan)
        
        return plan
    
    def _sort_tasks_by_dependencies(self, tasks: List[AgentTask]) -> List[AgentTask]:
        """Sort tasks considering dependencies and priority"""
        # Simple topological sort by priority for now
        # In a full implementation, this would handle complex dependencies
        return sorted(tasks, key=lambda t: (t.priority.value, t.created_at), reverse=True)
    
    def _calculate_plan_risk(self, plan: AgentPlan) -> str:
        """Calculate overall risk level for the plan"""
        risk_scores = {'safe': 0, 'moderate': 1, 'critical': 2, 'blocked': 3}
        total_risk = 0
        
        for task in plan.tasks:
            if task.command and not task.command.startswith('#'):
                security_level = self.security_manager.get_security_level(task.command)
                total_risk += risk_scores.get(security_level, 1)
        
        avg_risk = total_risk / len(plan.tasks) if plan.tasks else 0
        
        if avg_risk >= 2:
            return 'high'
        elif avg_risk >= 1:
            return 'medium'
        else:
            return 'low'
    
    def get_plan(self, plan_id: str) -> Optional[AgentPlan]:
        """Get plan by ID"""
        return self.active_plans.get(plan_id)
    
    def get_all_plans(self) -> List[AgentPlan]:
        """Get all active plans"""
        return list(self.active_plans.values())
    
    def cancel_plan(self, plan_id: str) -> bool:
        """Cancel an active plan"""
        if plan_id in self.active_plans:
            plan = self.active_plans[plan_id]
            plan.status = TaskStatus.CANCELLED
            
            # Cancel pending tasks
            for task in plan.tasks:
                if task.status == TaskStatus.PENDING:
                    task.status = TaskStatus.CANCELLED
            
            self.logger.info(f"Plan '{plan.name}' cancelled")
            return True
        
        return False
    
    def export_plan(self, plan_id: str) -> Optional[Dict]:
        """Export plan to dictionary format"""
        plan = self.get_plan(plan_id)
        if plan:
            return {
                'plan': asdict(plan),
                'tasks': [asdict(task) for task in plan.tasks]
            }
        return None
