#!/usr/bin/env python3
"""
Together.ai Interface for CYBEX Enterprise
Provides access to free AI models via Together.ai API
"""

import requests
import json
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class TogetherModel:
    """Together.ai model information"""
    id: str
    name: str
    provider: str
    type: str
    status: str
    description: str = ""

class TogetherAIInterface:
    """Interface for Together.ai free models"""
    
    def __init__(self, log_manager):
        self.logger = log_manager.get_logger(__name__)
        
        # Together.ai configuration
        self.api_key = "b4e1dd7a61cc0f0acf05f86b8d7fbe6c1648e6850f9fd2db5a32facb2f87c6de"
        self.base_url = "https://api.together.xyz/v1"
        self.timeout = 30
        
        # Available free models
        self.free_models = [
            TogetherModel(
                id="deepseek-ai/DeepSeek-R1-Distill-Llama-70B",
                name="DeepSeek R1 Distill Llama 70B Free",
                provider="DeepSeek",
                type="chat",
                status="Free"
            ),
            TogetherModel(
                id="deepseek-ai/deepseek-r1:free",
                name="Deepseek R1 0528",
                provider="Deepseek AI", 
                type="chat",
                status="Free"
            ),
            TogetherModel(
                id="arcee-ai/arcee-agent",
                name="AFM-4.5B-Preview",
                provider="Arcee AI",
                type="chat", 
                status="Free"
            ),
            TogetherModel(
                id="meta-llama/Llama-Vision-Free",
                name="Meta Llama Vision Free",
                provider="Meta",
                type="chat",
                status="Free"
            ),
            TogetherModel(
                id="LGAI-EXAONE/EXAONE-3.5-32B-Instruct",
                name="EXAONE 3.5 32B Instruct",
                provider="LG AI",
                type="chat",
                status="Free"
            ),
            TogetherModel(
                id="LGAI-EXAONE/EXAONE-Deep-32B",
                name="EXAONE Deep 32B", 
                provider="LG AI",
                type="chat",
                status="Free"
            ),
            TogetherModel(
                id="meta-llama/Meta-Llama-3.3-70B-Instruct-Turbo",
                name="Meta Llama 3.3 70B Instruct Turbo Free",
                provider="Meta",
                type="chat",
                status="Free"
            )
        ]
        
        # Current model
        self.current_model = self.free_models[0]  # Default to DeepSeek R1
        
        self.logger.info(f"Together.ai interface initialized with {len(self.free_models)} free models")
    
    def get_available_models(self) -> Dict[str, Any]:
        """Get list of available free models"""
        try:
            models_data = []
            for model in self.free_models:
                models_data.append({
                    'id': model.id,
                    'name': model.name,
                    'provider': model.provider,
                    'type': model.type,
                    'status': model.status,
                    'description': model.description
                })
            
            return {
                'success': True,
                'models': models_data,
                'count': len(models_data)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting models: {e}")
            return {
                'success': False,
                'error': str(e),
                'models': []
            }
    
    def switch_model(self, model_id: str) -> bool:
        """Switch to a different model"""
        try:
            # Find model by ID or name
            target_model = None
            for model in self.free_models:
                if model.id == model_id or model.name == model_id:
                    target_model = model
                    break
            
            if target_model:
                self.current_model = target_model
                self.logger.info(f"Switched to model: {target_model.name}")
                return True
            else:
                self.logger.error(f"Model not found: {model_id}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error switching model: {e}")
            return False
    
    def generate_response(self, prompt: str, **kwargs) -> Dict[str, Any]:
        """Generate response using Together.ai API"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Prepare request data
            data = {
                "model": self.current_model.id,
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                "max_tokens": kwargs.get('max_tokens', 1000),
                "temperature": kwargs.get('temperature', 0.7),
                "top_p": kwargs.get('top_p', 0.9),
                "stream": False
            }
            
            self.logger.info(f"Generating response with {self.current_model.name}")
            start_time = time.time()
            
            # Make API request
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=self.timeout
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                if 'choices' in result and len(result['choices']) > 0:
                    content = result['choices'][0]['message']['content']
                    
                    self.logger.info(f"Response generated successfully in {response_time:.2f}s")
                    
                    return {
                        'success': True,
                        'response': content,
                        'model': self.current_model.name,
                        'response_time': response_time,
                        'usage': result.get('usage', {}),
                        'metadata': {
                            'provider': 'Together.ai',
                            'model_id': self.current_model.id,
                            'timestamp': time.time()
                        }
                    }
                else:
                    return {
                        'success': False,
                        'error': 'No response content in API result',
                        'response': ''
                    }
            else:
                error_msg = f"API error: {response.status_code}"
                try:
                    error_detail = response.json()
                    error_msg += f" - {error_detail.get('error', {}).get('message', 'Unknown error')}"
                except:
                    error_msg += f" - {response.text}"
                
                self.logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'response': ''
                }
                
        except requests.exceptions.Timeout:
            error_msg = f"Request timeout after {self.timeout}s"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'response': ''
            }
        except Exception as e:
            error_msg = f"Generation error: {e}"
            self.logger.error(error_msg)
            return {
                'success': False,
                'error': error_msg,
                'response': ''
            }
    
    def test_connection(self) -> Dict[str, Any]:
        """Test connection to Together.ai API"""
        try:
            test_response = self.generate_response("Hello! Please respond with 'Connection test successful.'")
            
            if test_response.get('success'):
                return {
                    'success': True,
                    'message': 'Together.ai connection successful',
                    'model': self.current_model.name,
                    'response_time': test_response.get('response_time', 0)
                }
            else:
                return {
                    'success': False,
                    'error': test_response.get('error', 'Unknown error'),
                    'message': 'Together.ai connection failed'
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': 'Together.ai connection test failed'
            }
    
    def get_current_model(self) -> TogetherModel:
        """Get current active model"""
        return self.current_model
    
    def is_connected(self) -> bool:
        """Check if API is accessible"""
        try:
            test_result = self.test_connection()
            return test_result.get('success', False)
        except:
            return False
