#!/usr/bin/env python3
"""
Advanced System Tools for CYBEX Enterprise
Comprehensive system management and administration tools
"""

import os
import sys
import subprocess
import psutil
import shutil
import json
import time
import platform
import socket
import threading
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta
from pathlib import Path
import winreg
import wmi
import win32api
import win32con
import win32service
import win32serviceutil


@dataclass
class SystemInfo:
    """System information structure"""
    hostname: str
    os_name: str
    os_version: str
    architecture: str
    cpu_count: int
    memory_total: int
    disk_total: int
    uptime: float
    boot_time: float


class AdvancedSystemTools:
    """Advanced system management tools"""
    
    def __init__(self, log_manager=None):
        self.log_manager = log_manager
        self.logger = log_manager.get_logger(__name__) if log_manager else None
        
        # Initialize WMI connection for Windows
        try:
            self.wmi_conn = wmi.WMI()
        except Exception as e:
            if self.logger:
                self.logger.warning(f"Could not initialize WMI: {e}")
            self.wmi_conn = None
    
    def get_detailed_system_info(self) -> Dict[str, Any]:
        """Get comprehensive system information"""
        try:
            info = {
                'basic': {
                    'hostname': platform.node(),
                    'os_name': platform.system(),
                    'os_version': platform.version(),
                    'os_release': platform.release(),
                    'architecture': platform.architecture()[0],
                    'machine': platform.machine(),
                    'processor': platform.processor(),
                    'python_version': platform.python_version()
                },
                'hardware': self._get_hardware_info(),
                'memory': self._get_memory_info(),
                'storage': self._get_storage_info(),
                'network': self._get_network_info(),
                'processes': self._get_process_summary(),
                'services': self._get_services_info(),
                'environment': self._get_environment_info(),
                'performance': self._get_performance_metrics()
            }
            
            return info
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_hardware_info(self) -> Dict[str, Any]:
        """Get hardware information"""
        try:
            hardware = {
                'cpu': {
                    'physical_cores': psutil.cpu_count(logical=False),
                    'logical_cores': psutil.cpu_count(logical=True),
                    'max_frequency': psutil.cpu_freq().max if psutil.cpu_freq() else 0,
                    'current_frequency': psutil.cpu_freq().current if psutil.cpu_freq() else 0,
                    'usage_percent': psutil.cpu_percent(interval=1)
                },
                'memory': {
                    'total': psutil.virtual_memory().total,
                    'available': psutil.virtual_memory().available,
                    'used': psutil.virtual_memory().used,
                    'percentage': psutil.virtual_memory().percent
                },
                'swap': {
                    'total': psutil.swap_memory().total,
                    'used': psutil.swap_memory().used,
                    'free': psutil.swap_memory().free,
                    'percentage': psutil.swap_memory().percent
                }
            }
            
            # Add WMI hardware info if available
            if self.wmi_conn:
                try:
                    # CPU info
                    for cpu in self.wmi_conn.Win32_Processor():
                        hardware['cpu']['name'] = cpu.Name
                        hardware['cpu']['manufacturer'] = cpu.Manufacturer
                        hardware['cpu']['family'] = cpu.Family
                    
                    # Memory modules
                    memory_modules = []
                    for mem in self.wmi_conn.Win32_PhysicalMemory():
                        memory_modules.append({
                            'capacity': int(mem.Capacity) if mem.Capacity else 0,
                            'speed': mem.Speed,
                            'manufacturer': mem.Manufacturer,
                            'part_number': mem.PartNumber
                        })
                    hardware['memory']['modules'] = memory_modules
                    
                except Exception as e:
                    hardware['wmi_error'] = str(e)
            
            return hardware
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_memory_info(self) -> Dict[str, Any]:
        """Get detailed memory information"""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            return {
                'virtual': {
                    'total': memory.total,
                    'available': memory.available,
                    'used': memory.used,
                    'free': memory.free,
                    'percentage': memory.percent,
                    'active': getattr(memory, 'active', 0),
                    'inactive': getattr(memory, 'inactive', 0),
                    'buffers': getattr(memory, 'buffers', 0),
                    'cached': getattr(memory, 'cached', 0)
                },
                'swap': {
                    'total': swap.total,
                    'used': swap.used,
                    'free': swap.free,
                    'percentage': swap.percent,
                    'sin': swap.sin,
                    'sout': swap.sout
                }
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_storage_info(self) -> Dict[str, Any]:
        """Get storage information"""
        try:
            storage = {
                'disks': [],
                'partitions': [],
                'io_stats': {}
            }
            
            # Disk partitions
            for partition in psutil.disk_partitions():
                try:
                    usage = psutil.disk_usage(partition.mountpoint)
                    storage['partitions'].append({
                        'device': partition.device,
                        'mountpoint': partition.mountpoint,
                        'fstype': partition.fstype,
                        'total': usage.total,
                        'used': usage.used,
                        'free': usage.free,
                        'percentage': (usage.used / usage.total) * 100
                    })
                except PermissionError:
                    continue
            
            # Disk I/O stats
            try:
                io_stats = psutil.disk_io_counters(perdisk=True)
                for disk, stats in io_stats.items():
                    storage['io_stats'][disk] = {
                        'read_count': stats.read_count,
                        'write_count': stats.write_count,
                        'read_bytes': stats.read_bytes,
                        'write_bytes': stats.write_bytes,
                        'read_time': stats.read_time,
                        'write_time': stats.write_time
                    }
            except Exception:
                pass
            
            return storage
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_network_info(self) -> Dict[str, Any]:
        """Get network information"""
        try:
            network = {
                'interfaces': {},
                'connections': [],
                'io_stats': {},
                'routing_table': []
            }
            
            # Network interfaces
            for interface, addrs in psutil.net_if_addrs().items():
                network['interfaces'][interface] = []
                for addr in addrs:
                    network['interfaces'][interface].append({
                        'family': str(addr.family),
                        'address': addr.address,
                        'netmask': addr.netmask,
                        'broadcast': addr.broadcast
                    })
            
            # Network I/O stats
            try:
                io_stats = psutil.net_io_counters(pernic=True)
                for interface, stats in io_stats.items():
                    network['io_stats'][interface] = {
                        'bytes_sent': stats.bytes_sent,
                        'bytes_recv': stats.bytes_recv,
                        'packets_sent': stats.packets_sent,
                        'packets_recv': stats.packets_recv,
                        'errin': stats.errin,
                        'errout': stats.errout,
                        'dropin': stats.dropin,
                        'dropout': stats.dropout
                    }
            except Exception:
                pass
            
            # Active connections (limited to avoid performance issues)
            try:
                connections = psutil.net_connections(kind='inet')[:50]  # Limit to 50
                for conn in connections:
                    network['connections'].append({
                        'fd': conn.fd,
                        'family': str(conn.family),
                        'type': str(conn.type),
                        'laddr': f"{conn.laddr.ip}:{conn.laddr.port}" if conn.laddr else None,
                        'raddr': f"{conn.raddr.ip}:{conn.raddr.port}" if conn.raddr else None,
                        'status': conn.status,
                        'pid': conn.pid
                    })
            except Exception:
                pass
            
            return network
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_process_summary(self) -> Dict[str, Any]:
        """Get process summary information"""
        try:
            processes = {
                'total_count': len(psutil.pids()),
                'running': 0,
                'sleeping': 0,
                'zombie': 0,
                'stopped': 0,
                'top_cpu': [],
                'top_memory': []
            }
            
            # Get all processes
            all_processes = []
            for proc in psutil.process_iter(['pid', 'name', 'status', 'cpu_percent', 'memory_percent']):
                try:
                    pinfo = proc.info
                    all_processes.append(pinfo)
                    
                    # Count by status
                    status = pinfo['status']
                    if status == psutil.STATUS_RUNNING:
                        processes['running'] += 1
                    elif status == psutil.STATUS_SLEEPING:
                        processes['sleeping'] += 1
                    elif status == psutil.STATUS_ZOMBIE:
                        processes['zombie'] += 1
                    elif status == psutil.STATUS_STOPPED:
                        processes['stopped'] += 1
                        
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # Top CPU processes
            processes['top_cpu'] = sorted(
                all_processes, 
                key=lambda x: x['cpu_percent'] or 0, 
                reverse=True
            )[:10]
            
            # Top memory processes
            processes['top_memory'] = sorted(
                all_processes, 
                key=lambda x: x['memory_percent'] or 0, 
                reverse=True
            )[:10]
            
            return processes
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_services_info(self) -> Dict[str, Any]:
        """Get Windows services information"""
        try:
            services = {
                'total_count': 0,
                'running': 0,
                'stopped': 0,
                'services': []
            }
            
            if self.wmi_conn:
                for service in self.wmi_conn.Win32_Service():
                    services['total_count'] += 1
                    
                    if service.State == 'Running':
                        services['running'] += 1
                    else:
                        services['stopped'] += 1
                    
                    services['services'].append({
                        'name': service.Name,
                        'display_name': service.DisplayName,
                        'state': service.State,
                        'start_mode': service.StartMode,
                        'service_type': service.ServiceType,
                        'path_name': service.PathName
                    })
            
            return services
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_environment_info(self) -> Dict[str, Any]:
        """Get environment information"""
        try:
            return {
                'user': os.getenv('USERNAME', 'unknown'),
                'domain': os.getenv('USERDOMAIN', 'unknown'),
                'computer_name': os.getenv('COMPUTERNAME', 'unknown'),
                'temp_dir': os.getenv('TEMP', 'unknown'),
                'system_root': os.getenv('SYSTEMROOT', 'unknown'),
                'program_files': os.getenv('PROGRAMFILES', 'unknown'),
                'path_count': len(os.getenv('PATH', '').split(';')),
                'environment_variables': len(os.environ)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def _get_performance_metrics(self) -> Dict[str, Any]:
        """Get current performance metrics"""
        try:
            return {
                'cpu_percent': psutil.cpu_percent(interval=1),
                'memory_percent': psutil.virtual_memory().percent,
                'disk_io': psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else {},
                'network_io': psutil.net_io_counters()._asdict() if psutil.net_io_counters() else {},
                'boot_time': psutil.boot_time(),
                'uptime': time.time() - psutil.boot_time(),
                'load_average': os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def manage_service(self, service_name: str, action: str) -> Dict[str, Any]:
        """Manage Windows service"""
        try:
            if action.lower() == 'start':
                win32serviceutil.StartService(service_name)
                return {'success': True, 'message': f'Service {service_name} started'}
            elif action.lower() == 'stop':
                win32serviceutil.StopService(service_name)
                return {'success': True, 'message': f'Service {service_name} stopped'}
            elif action.lower() == 'restart':
                win32serviceutil.RestartService(service_name)
                return {'success': True, 'message': f'Service {service_name} restarted'}
            elif action.lower() == 'status':
                status = win32serviceutil.QueryServiceStatus(service_name)
                return {'success': True, 'status': status}
            else:
                return {'success': False, 'error': 'Invalid action. Use: start, stop, restart, status'}
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def manage_process(self, process_identifier: str, action: str) -> Dict[str, Any]:
        """Manage system process"""
        try:
            # Find process by name or PID
            if process_identifier.isdigit():
                pid = int(process_identifier)
                proc = psutil.Process(pid)
            else:
                # Find by name
                procs = [p for p in psutil.process_iter(['pid', 'name']) if p.info['name'].lower() == process_identifier.lower()]
                if not procs:
                    return {'success': False, 'error': f'Process {process_identifier} not found'}
                proc = procs[0]
            
            if action.lower() == 'kill':
                proc.kill()
                return {'success': True, 'message': f'Process {process_identifier} killed'}
            elif action.lower() == 'terminate':
                proc.terminate()
                return {'success': True, 'message': f'Process {process_identifier} terminated'}
            elif action.lower() == 'suspend':
                proc.suspend()
                return {'success': True, 'message': f'Process {process_identifier} suspended'}
            elif action.lower() == 'resume':
                proc.resume()
                return {'success': True, 'message': f'Process {process_identifier} resumed'}
            elif action.lower() == 'info':
                info = proc.as_dict(attrs=['pid', 'name', 'status', 'cpu_percent', 'memory_percent', 'create_time'])
                return {'success': True, 'info': info}
            else:
                return {'success': False, 'error': 'Invalid action. Use: kill, terminate, suspend, resume, info'}
                
        except psutil.NoSuchProcess:
            return {'success': False, 'error': f'Process {process_identifier} not found'}
        except psutil.AccessDenied:
            return {'success': False, 'error': f'Access denied to process {process_identifier}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_registry_info(self, hive: str, key_path: str) -> Dict[str, Any]:
        """Get Windows registry information"""
        try:
            # Map hive names to constants
            hive_map = {
                'HKEY_LOCAL_MACHINE': winreg.HKEY_LOCAL_MACHINE,
                'HKEY_CURRENT_USER': winreg.HKEY_CURRENT_USER,
                'HKEY_CLASSES_ROOT': winreg.HKEY_CLASSES_ROOT,
                'HKEY_USERS': winreg.HKEY_USERS,
                'HKEY_CURRENT_CONFIG': winreg.HKEY_CURRENT_CONFIG
            }
            
            if hive not in hive_map:
                return {'success': False, 'error': f'Invalid hive: {hive}'}
            
            with winreg.OpenKey(hive_map[hive], key_path) as key:
                values = {}
                subkeys = []
                
                # Get values
                try:
                    i = 0
                    while True:
                        name, value, type_id = winreg.EnumValue(key, i)
                        values[name] = {'value': value, 'type': type_id}
                        i += 1
                except WindowsError:
                    pass
                
                # Get subkeys
                try:
                    i = 0
                    while True:
                        subkey_name = winreg.EnumKey(key, i)
                        subkeys.append(subkey_name)
                        i += 1
                except WindowsError:
                    pass
                
                return {
                    'success': True,
                    'values': values,
                    'subkeys': subkeys,
                    'path': f"{hive}\\{key_path}"
                }
                
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def get_installed_software(self) -> Dict[str, Any]:
        """Get list of installed software"""
        try:
            software = []
            
            if self.wmi_conn:
                for product in self.wmi_conn.Win32_Product():
                    software.append({
                        'name': product.Name,
                        'version': product.Version,
                        'vendor': product.Vendor,
                        'install_date': product.InstallDate,
                        'install_location': product.InstallLocation
                    })
            
            return {
                'success': True,
                'software_count': len(software),
                'software': software
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}


def create_advanced_system_tools(log_manager=None) -> AdvancedSystemTools:
    """Factory function to create advanced system tools"""
    return AdvancedSystemTools(log_manager)
