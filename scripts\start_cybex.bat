@echo off
REM ============================================================================
REM Cybex Enterprise - Startup Script
REM Advanced Cybernetic Expert Agent by AGTECHdesigne
REM ============================================================================

title Cybex Enterprise - Startup

REM Set colors for output
color 0B

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║   ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗                                  ║
echo ║  ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝                                  ║
echo ║  ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝                                   ║
echo ║  ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗                                   ║
echo ║  ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗                                  ║
echo ║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝                                  ║
echo ║                                                                              ║
echo ║                      Enterprise Edition                                      ║
echo ║                        by AGTECHdesigne                                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
echo 🚀 Starting Cybex Enterprise...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERROR: Python is not installed or not in PATH
    echo.
    echo Please install Python 3.8+ from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    echo.
    pause
    exit /b 1
)

REM Display Python version
echo ✅ Python detected:
python --version
echo.

REM Check if we're in the correct directory
if not exist "main_enterprise.py" (
    echo ❌ ERROR: main_enterprise.py not found
    echo.
    echo Please make sure you're running this script from the Cybex directory
    echo Current directory: %CD%
    echo.
    pause
    exit /b 1
)

REM Check if Ollama is running
echo 🔍 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️  WARNING: Ollama server not detected at localhost:11434
    echo.
    echo Cybex can still run, but AI features will be limited.
    echo To enable full functionality:
    echo   1. Install Ollama from https://ollama.ai
    echo   2. Run: ollama pull gemma2:7b
    echo   3. Start Ollama service
    echo.
    set /p continue="Continue anyway? (Y/N): "
    if /i not "!continue!"=="Y" if /i not "!continue!"=="YES" (
        echo.
        echo Startup cancelled by user
        pause
        exit /b 0
    )
) else (
    echo ✅ Ollama server is running
)

echo.
echo 🎯 Launching Cybex Enterprise...
echo.

REM Launch Cybex Enterprise
python main_enterprise.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ Cybex Enterprise exited with an error
    echo.
    echo Troubleshooting tips:
    echo   • Check that all dependencies are installed: pip install -r requirements.txt
    echo   • Ensure Ollama is running if you want AI features
    echo   • Check the error messages above for specific issues
    echo.
) else (
    echo.
    echo ✅ Cybex Enterprise closed successfully
    echo.
    echo Thank you for using Cybex Enterprise by AGTECHdesigne
)

echo.
pause
