# Cybex MVP - <PERSON>uida Completa

## 🎯 Panoramica MVP

La fase MVP (Minimum Viable Product) di Cybex implementa le funzionalità core per un agente AI locale di amministrazione sistema con le seguenti caratteristiche:

### ✅ Funzionalità Implementate

1. **Core Engine** (`cybex_core.py`)
   - Gestione modalità Chat/Agent
   - Sistema di conferme di sicurezza
   - Logging delle operazioni
   - Gestione sessioni

2. **Security Manager** (`security_manager.py`)
   - Validazione comandi
   - Classificazione livelli di sicurezza
   - Blocco comandi pericolosi
   - Sanitizzazione output

3. **Command Executor** (`command_executor.py`)
   - Esecuzione sicura comandi sistema
   - Timeout e gestione errori
   - Cronologia comandi
   - Supporto cross-platform

4. **Ollama Interface** (`ollama_interface.py`)
   - Integrazione con Gemma 2 7B
   - Gestione conversazioni
   - Prompt engineering per Cybex
   - Gestione errori API

5. **Configuration Manager** (`config_manager.py`)
   - Caricamento/salvataggio configurazioni
   - Validazione parametri
   - Merge con defaults
   - Persistenza impostazioni

6. **Log Manager** (`log_manager.py`)
   - Logging strutturato
   - Rotazione file automatica
   - Livelli di log configurabili
   - Statistiche logging

7. **CLI Interface** (`ui_cli.py`)
   - Interfaccia a riga di comando
   - Comandi built-in
   - Colorazione output
   - Gestione input utente

## 🚀 Installazione e Setup

### Prerequisiti
- Python 3.11+
- Ollama installato
- Modello Gemma 2 7B

### Installazione Automatica
```bash
python install.py
```

### Installazione Manuale
```bash
# 1. Installa dipendenze
pip install -r requirements.txt

# 2. Installa Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 3. Scarica modello
ollama pull gemma2:7b

# 4. Avvia Ollama
ollama serve

# 5. Avvia Cybex
python main.py
```

## 🎮 Utilizzo

### Avvio Base
```bash
python main.py
```

### Comandi CLI Disponibili

#### Comandi Built-in
- `help` - Mostra aiuto
- `status` - Stato sistema e Cybex
- `mode [chat|agent]` - Cambia modalità
- `history` - Cronologia comandi
- `logs [n]` - Ultimi n log entries
- `config` - Configurazione corrente
- `clear` - Pulisce schermo
- `exit/quit` - Esce da Cybex

#### Esecuzione Comandi
- `!comando` - Esegue comando sistema direttamente
- `testo libero` - Invia richiesta all'AI

### Esempi di Utilizzo

#### Modalità Chat (Default)
```bash
cybex 💬 > Come posso vedere l'utilizzo della RAM?
cybex 💬 > !systeminfo
cybex 💬 > Mostrami i processi attivi
```

#### Modalità Agent
```bash
cybex 💬 > mode agent
cybex 🤖 > Ottimizza le performance del sistema
cybex 🤖 > Pulisci i file temporanei
```

## ⚙️ Configurazione

### File Principale: `cybex/config/cybex_config.yaml`

```yaml
# Modalità operativa
mode: "chat"  # "chat" o "agent"

# Livello conferme
confirm_level: "strict"  # "strict", "moderate", "minimal"

# Comandi critici (sempre confermati)
critical_ops:
  - "rm"
  - "del"
  - "format"
  - "fdisk"

# Configurazione Ollama
ollama:
  model: "gemma2:7b"
  host: "localhost"
  port: 11434
  timeout: 30
  max_tokens: 3000

# Logging
logging:
  level: "INFO"
  file_path: "logs/cybex.log"
  max_file_size: "10MB"
  backup_count: 5

# Sicurezza
security:
  enable_sandbox: true
  max_command_length: 1000
  blocked_commands:
    - "sudo rm -rf /"
    - "format c:"
  allowed_directories:
    - "/home"
    - "/tmp"
    - "C:\\Users"
```

## 🛡️ Sicurezza

### Livelli di Sicurezza

1. **Safe** (Verde)
   - Comandi di lettura (ls, dir, cat, type)
   - Info sistema (systeminfo, hostname)
   - Comandi rete non invasivi (ping)

2. **Moderate** (Giallo)
   - Creazione file/directory
   - Copia/spostamento file
   - Modifica permessi base

3. **Critical** (Rosso)
   - Eliminazione file/directory
   - Formattazione dischi
   - Stop/start servizi
   - Shutdown/reboot

4. **Blocked** (Nero)
   - Comandi esplicitamente vietati
   - Pattern pericolosi riconosciuti
   - Comandi troppo lunghi

### Controlli Implementati

- **Validazione Sintassi**: Controllo pattern pericolosi
- **Whitelist/Blacklist**: Comandi permessi/vietati
- **Sandbox**: Limitazione accesso directory
- **Timeout**: Limite tempo esecuzione
- **Sanitizzazione**: Rimozione info sensibili dai log
- **Conferme**: Richiesta conferma per operazioni critiche

## 📊 Logging e Monitoraggio

### Struttura Log
```
2024-01-18 10:30:15 - cybex.core - INFO - Cybex session started in chat mode
2024-01-18 10:30:20 - cybex.commands - INFO - [SUCCESS] Command: ls -la (took 0.05s)
2024-01-18 10:30:25 - cybex.security - WARNING - [BLOCKED] Command: rm -rf / - Blocked command pattern detected
```

### Tipi di Log
- **INFO**: Operazioni normali
- **WARNING**: Eventi di sicurezza
- **ERROR**: Errori esecuzione
- **DEBUG**: Informazioni dettagliate

### Rotazione Automatica
- Dimensione massima: 10MB (configurabile)
- Backup files: 5 (configurabile)
- Compressione automatica

## 🧪 Testing

### Esecuzione Test
```bash
# Test singolo modulo
python -m pytest cybex/tests/test_security_manager.py -v

# Tutti i test
python -m pytest cybex/tests/ -v

# Con coverage
python -m pytest cybex/tests/ --cov=cybex --cov-report=html
```

### Test Implementati
- **SecurityManager**: Validazione comandi, classificazione sicurezza
- **ConfigManager**: Caricamento/salvataggio configurazioni
- **CommandExecutor**: Esecuzione comandi sicura

## 🔧 Troubleshooting

### Problemi Comuni

#### Ollama non disponibile
```bash
# Verifica stato
curl http://localhost:11434/api/tags

# Avvia servizio
ollama serve

# Verifica modello
ollama list
```

#### Errori di permessi
```bash
# Linux: aggiungi utente a gruppi
sudo usermod -a -G sudo $USER

# Windows: esegui come amministratore
```

#### Dipendenze mancanti
```bash
# Reinstalla requirements
pip install --force-reinstall -r requirements.txt
```

#### Log non visibili
```bash
# Verifica directory logs
ls -la logs/

# Controlla permessi
chmod 755 logs/
```

## 📈 Prossimi Passi (SVP)

La fase SVP implementerà:

1. **Modalità Agent Completa**
   - Planning multi-step
   - Esecuzione autonoma
   - Rollback automatico

2. **Monitoraggio Sistema Avanzato**
   - CPU/RAM/Disk monitoring
   - Performance metrics
   - Alert automatici

3. **Gestione Dischi**
   - SMART check
   - Pulizia automatica
   - Analisi partizioni

4. **Scripting Avanzato**
   - PowerShell integration
   - Bash scripting
   - Template comandi

5. **Persistenza Dati**
   - Database sessioni
   - Profili utente
   - Preferenze personalizzate

## 🤝 Contribuire

Per contribuire al progetto:

1. Fork del repository
2. Crea branch feature (`git checkout -b feature/AmazingFeature`)
3. Commit modifiche (`git commit -m 'Add AmazingFeature'`)
4. Push al branch (`git push origin feature/AmazingFeature`)
5. Apri Pull Request

### Linee Guida
- Segui PEP 8 per lo stile Python
- Aggiungi test per nuove funzionalità
- Documenta le modifiche
- Mantieni compatibilità backward

## 📞 Supporto

- **Issues**: GitHub Issues per bug report
- **Discussions**: GitHub Discussions per domande
- **Wiki**: Documentazione estesa
- **Email**: <EMAIL> per supporto diretto
