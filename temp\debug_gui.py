#!/usr/bin/env python3
"""
Debug GUI - Identifica problemi specifici
"""

import sys
import traceback
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_gui_import():
    """Test import GUI components"""
    print("🔍 Testing GUI imports...")
    
    try:
        from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI
        print("✅ CybexFuturisticGUI imported")
        return True
    except Exception as e:
        print(f"❌ Import error: {e}")
        traceback.print_exc()
        return False

def test_gui_creation():
    """Test GUI creation"""
    print("\n🎨 Testing GUI creation...")
    
    try:
        from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI
        
        print("Creating GUI instance...")
        app = CybexFuturisticGUI()
        print("✅ GUI instance created")
        
        print("Testing window setup...")
        if hasattr(app, 'root') and app.root:
            print("✅ Root window exists")
            
            # Test window properties
            print(f"Window title: {app.root.title()}")
            print(f"Window geometry: {app.root.geometry()}")
            
            # Don't run mainloop, just test creation
            app.root.destroy()
            print("✅ GUI creation test passed")
            return True
        else:
            print("❌ Root window not created")
            return False
            
    except Exception as e:
        print(f"❌ GUI creation error: {e}")
        traceback.print_exc()
        return False

def test_terminal_component():
    """Test terminal component"""
    print("\n💻 Testing terminal component...")
    
    try:
        import tkinter as tk
        from cybex.interfaces.ui_futuristic_gui import FuturisticTerminal
        
        # Create test root
        root = tk.Tk()
        root.withdraw()
        
        print("Creating terminal component...")
        terminal = FuturisticTerminal(root)
        print("✅ Terminal component created")
        
        # Test terminal methods
        if hasattr(terminal, 'add_output'):
            terminal.add_output("Test output", "info")
            print("✅ add_output method works")
        
        if hasattr(terminal, 'execute_command'):
            print("✅ execute_command method exists")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Terminal component error: {e}")
        traceback.print_exc()
        return False

def test_cybex_integration():
    """Test Cybex components integration"""
    print("\n🤖 Testing Cybex integration...")
    
    try:
        from cybex.core.cybex_core import CybexCore
        from cybex.modules.ollama_interface import OllamaInterface
        from cybex.modules.agent_tools import AgentTools
        
        print("Testing Cybex Core...")
        core = CybexCore()
        print("✅ Cybex Core initialized")
        
        print("Testing Ollama Interface...")
        ollama = OllamaInterface(core.config_manager, core.log_manager)
        print("✅ Ollama Interface initialized")
        
        print("Testing Agent Tools...")
        tools = AgentTools(core.log_manager)
        print("✅ Agent Tools initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Cybex integration error: {e}")
        traceback.print_exc()
        return False

def main():
    """Run debug tests"""
    print("🔧 Cybex Futuristic GUI - Debug Session")
    print("=" * 60)
    
    tests = [
        ("GUI Import", test_gui_import),
        ("GUI Creation", test_gui_creation),
        ("Terminal Component", test_terminal_component),
        ("Cybex Integration", test_cybex_integration)
    ]
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            success = test_func()
            if success:
                print(f"✅ {test_name}: SUCCESS")
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: EXCEPTION - {e}")
            traceback.print_exc()
    
    print(f"\n{'='*60}")
    print("🎯 Debug session completed")

if __name__ == "__main__":
    main()
