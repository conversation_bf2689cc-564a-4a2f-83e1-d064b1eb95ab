#!/usr/bin/env python3
"""
Test Continuous Chat
Test della chat continua e salvataggio sessioni
"""

import sys
import json
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_chat_history_functionality():
    """Test chat history and saving functionality"""
    print("💬 Testing Chat History Functionality")
    print("=" * 50)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        # Initialize UI
        ui = CybexEnterpriseUI()
        
        print("✅ Enterprise UI initialized")
        
        # Test initial state
        print(f"Initial chat history: {len(ui.chat_history)} messages")
        print(f"In chat mode: {ui.in_chat_mode}")
        
        # Simulate adding chat messages
        print("\nSimulating chat messages...")
        
        # Add some test messages to chat history
        test_messages = [
            {
                'timestamp': time.time() - 300,
                'user': 'Ciao, chi sei?',
                'assistant': '<PERSON><PERSON><PERSON>! Sono Cybex, un agente AI...',
                'model': 'gemma3:4b',
                'response_time': 25.06
            },
            {
                'timestamp': time.time() - 200,
                'user': 'Come funzioni?',
                'assistant': 'Funziono attraverso modelli di linguaggio...',
                'model': 'gemma3:4b',
                'response_time': 18.32
            },
            {
                'timestamp': time.time() - 100,
                'user': 'Puoi aiutarmi con Python?',
                'assistant': 'Certamente! Posso aiutarti con Python...',
                'model': 'gemma3:4b',
                'response_time': 22.15
            }
        ]
        
        ui.chat_history.extend(test_messages)
        
        print(f"✅ Added {len(test_messages)} test messages")
        print(f"Chat history now has: {len(ui.chat_history)} messages")
        
        # Test save functionality
        print("\nTesting save functionality...")
        ui._save_current_chat()
        
        # Check if file was created
        chat_dir = Path("cybex/data/chats")
        if chat_dir.exists():
            chat_files = list(chat_dir.glob("chat_*.json"))
            if chat_files:
                latest_file = max(chat_files, key=lambda f: f.stat().st_mtime)
                print(f"✅ Chat file created: {latest_file.name}")
                
                # Verify file content
                with open(latest_file, 'r', encoding='utf-8') as f:
                    saved_data = json.load(f)
                
                print(f"✅ Saved data contains:")
                print(f"   • Messages: {saved_data['total_messages']}")
                print(f"   • Models used: {', '.join(saved_data['models_used'])}")
                print(f"   • Session duration: {saved_data['session_start']} to {saved_data['session_end']}")
                
                return True
            else:
                print("❌ No chat files found")
                return False
        else:
            print("❌ Chat directory not created")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_chat_mode_functionality():
    """Test chat mode state management"""
    print(f"\n🔄 Testing Chat Mode State Management")
    print("=" * 50)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        
        # Test initial state
        print(f"Initial chat mode: {ui.in_chat_mode}")
        
        # Simulate entering chat mode
        ui.in_chat_mode = True
        print(f"After entering chat mode: {ui.in_chat_mode}")
        
        # Test exit chat mode
        ui._exit_chat_mode()
        print(f"After exiting chat mode: {ui.in_chat_mode}")
        
        # Test chat help
        print("\nTesting chat help display...")
        ui._show_chat_help()
        
        # Test clear history
        ui.chat_history = [{'test': 'message'}]
        print(f"Before clear: {len(ui.chat_history)} messages")
        ui._clear_chat_history()
        print(f"After clear: {len(ui.chat_history)} messages")
        
        print("✅ Chat mode functionality working")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_chat_commands():
    """Test chat commands parsing"""
    print(f"\n⌨️  Testing Chat Commands")
    print("=" * 35)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        ui.in_chat_mode = True
        
        # Test commands that should be recognized
        chat_commands = [
            '/exit',
            '/quit', 
            '/back',
            '/menu',
            '/save',
            '/clear',
            '/help'
        ]
        
        print("Testing chat command recognition:")
        
        for cmd in chat_commands:
            # Simulate the command check logic
            if cmd in ['/exit', '/quit', '/back', '/menu']:
                print(f"  {cmd:<10} → Exit chat mode")
            elif cmd == '/save':
                print(f"  {cmd:<10} → Save chat session")
            elif cmd == '/clear':
                print(f"  {cmd:<10} → Clear chat history")
            elif cmd == '/help':
                print(f"  {cmd:<10} → Show chat help")
        
        print("✅ Chat commands recognized correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def show_chat_features_summary():
    """Show summary of new chat features"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    summary = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                        {SunriseColors.ACCENT}💬 NEW CHAT FEATURES{SunriseColors.SUCCESS}                                   ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🔄 CONTINUOUS CHAT:{SunriseColors.SUCCESS}                                                            ║
║   • No more "Press Enter to continue"                                        ║
║   • Chat flows naturally like a conversation                                 ║
║   • Type messages continuously without interruption                          ║
║                                                                               ║
║ {SunriseColors.ACCENT}💾 AUTOMATIC CHAT SAVING:{SunriseColors.SUCCESS}                                                     ║
║   • Chat history automatically tracked                                       ║
║   • Save prompt when exiting with chat history                               ║
║   • Optional description for saved sessions                                  ║
║   • JSON format with timestamps and metadata                                 ║
║                                                                               ║
║ {SunriseColors.ACCENT}⌨️  CHAT COMMANDS:{SunriseColors.SUCCESS}                                                             ║
║   • /exit, /quit, /back, /menu - Return to main menu                         ║
║   • /save - Manually save current session                                    ║
║   • /clear - Clear chat history                                              ║
║   • /help - Show chat help                                                   ║
║                                                                               ║
║ {SunriseColors.ACCENT}📁 CHAT STORAGE:{SunriseColors.SUCCESS}                                                              ║
║   • Location: cybex/data/chats/                                              ║
║   • Format: chat_YYYYMMDD_HHMMSS.json                                        ║
║   • Includes: messages, models used, timestamps, metadata                    ║
║                                                                               ║
║ {SunriseColors.ACCENT}🎯 USAGE:{SunriseColors.SUCCESS}                                                                     ║
║   1. Start Cybex: python main_enterprise.py                                  ║
║   2. Type any message (not 1-9) to start chat                                ║
║   3. Continue chatting naturally                                             ║
║   4. Use /exit to return to menu                                             ║
║   5. Choose to save when exiting                                             ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(summary)


def main():
    """Run continuous chat tests"""
    print("🎯 Cybex Enterprise - Continuous Chat Test")
    print("=" * 60)
    
    tests = [
        ("Chat History Functionality", test_chat_history_functionality),
        ("Chat Mode State Management", test_chat_mode_functionality),
        ("Chat Commands", test_chat_commands)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        show_chat_features_summary()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Continuous chat is working perfectly!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}You can now chat naturally without interruptions!{SunriseColors.RESET}")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
