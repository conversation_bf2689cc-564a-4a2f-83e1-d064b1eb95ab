@echo off
REM ============================================================================
REM CYBEX ENTERPRISE LAUNCHER
REM Professional AI Terminal System
REM ============================================================================

title CYBEX ENTERPRISE - Professional AI Terminal

REM Set professional colors
color 0F

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║  ████████╗██╗  ██╗██████╗ ███████╗██╗  ██╗    ███████╗███╗   ██╗████████╗  ║
echo ║  ██╔═════╝╚██╗██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝████╗  ██║╚══██╔══╝  ║
echo ║  ██║       ╚███╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██╔██╗ ██║   ██║     ║
echo ║  ██║       ██╔██╗ ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║╚██╗██║   ██║     ║
echo ║  ╚██████╗██╔╝ ██╗██████╔╝███████╗██╔╝ ██╗    ███████╗██║ ╚████║   ██║     ║
echo ║   ╚═════╝╚═╝  ╚═╝╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚══════╝╚═╝  ╚═══╝   ╚═╝     ║
echo ║                                                                              ║
echo ║                    🏢 ENTERPRISE AI TERMINAL SYSTEM                         ║
echo ║                   Professional Grade • Security Focused                     ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check system requirements
echo 🔍 Checking system requirements...
echo.

REM Check Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found
    echo 💡 Install Python 3.8+ from https://python.org
    echo.
    pause
    exit /b 1
)

echo ✅ Python detected
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo    Version: %PYTHON_VERSION%

REM Check directory structure
if not exist "bin\cybex_futuristic.py" (
    echo ❌ CYBEX core files not found
    echo 💡 Ensure you're in the CYBEX root directory
    echo.
    pause
    exit /b 1
)

echo ✅ CYBEX core files found

REM Check assets
if not exist "assets\logo.png" (
    echo ⚠️  Logo asset missing - will use fallback
) else (
    echo ✅ Assets loaded
)

REM Check required Python modules
echo.
echo 🔍 Checking Python dependencies...
python -c "import tkinter, psutil, requests, json, pathlib" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Some Python modules may be missing
    echo 💡 Run: pip install -r requirements.txt
    echo.
) else (
    echo ✅ Core dependencies available
)

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║  🚀 LAUNCHING CYBEX ENTERPRISE                                               ║
echo ║                                                                              ║
echo ║  Features Available:                                                         ║
echo ║  • Futuristic AI Interface                                                   ║
echo ║  • Enterprise Security Tools                                                 ║
echo ║  • Real-time System Monitoring                                               ║
echo ║  • Professional Branding                                                     ║
echo ║  • Natural Language Commands                                                 ║
echo ║                                                                              ║
echo ║  💼 Enterprise Level: PROFESSIONAL                                           ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

echo 🚀 Starting CYBEX Enterprise...
echo 💡 Loading may take a few moments...
echo.

REM Launch CYBEX Enterprise
call scripts\cybex_futuristic.bat

REM Handle exit
echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║  👋 CYBEX ENTERPRISE SESSION ENDED                                           ║
echo ║                                                                              ║
echo ║  Thank you for using CYBEX Enterprise                                        ║
echo ║  Professional AI Terminal System                                             ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.
pause
