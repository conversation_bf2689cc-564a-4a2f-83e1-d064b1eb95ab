#!/usr/bin/env python3
"""
Test Agent Tools
Test del sistema di tool avanzati per l'agente
"""

import sys
import time
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_agent_tools_initialization():
    """Test agent tools initialization"""
    print("🛠️  Testing Agent Tools Initialization")
    print("=" * 50)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        from cybex.modules.operation_monitor import OperationMonitor
        
        # Initialize agent tools
        agent_tools = AgentTools()
        
        print("✅ Agent tools initialized")
        
        # Check available tools
        available_tools = agent_tools.get_available_tools()
        print(f"✅ Found {len(available_tools)} available tools:")
        
        for tool_name, tool_info in available_tools.items():
            print(f"  • {tool_name}: {tool_info['description']}")
        
        # Test operation monitor
        monitor = OperationMonitor()
        print("✅ Operation monitor initialized")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_system_command_tool():
    """Test system command execution tool"""
    print(f"\n💻 Testing System Command Tool")
    print("=" * 40)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test safe command
        print("Testing safe command: 'echo Hello World'")
        
        execution_id = agent_tools.execute_tool(
            "execute_command",
            {"command": "echo Hello World", "shell": "cmd"}
        )
        
        print(f"✅ Tool execution started: {execution_id}")
        
        # Wait for completion
        max_wait = 10
        wait_time = 0
        
        while wait_time < max_wait:
            execution = agent_tools.get_execution_status(execution_id)
            
            if execution and execution.status.value in ['completed', 'failed']:
                break
            
            time.sleep(0.5)
            wait_time += 0.5
        
        # Check result
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ Command executed successfully")
                print(f"📄 Output: {result.output.strip()}")
                print(f"⏱️  Execution time: {result.execution_time:.2f}s")
                return True
            else:
                print(f"❌ Command failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_file_operations_tool():
    """Test file operations tool"""
    print(f"\n📁 Testing File Operations Tool")
    print("=" * 40)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test directory listing
        print("Testing directory listing for current directory")
        
        execution_id = agent_tools.execute_tool(
            "list_directory",
            {"path": ".", "detailed": True}
        )
        
        # Wait for completion
        time.sleep(2)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ Directory listing successful")
                print(f"📊 Found {result.metadata.get('item_count', 0)} items")
                
                # Show first few lines
                lines = result.output.split('\n')[:5]
                for line in lines:
                    if line.strip():
                        print(f"  {line}")
                
                if len(result.output.split('\n')) > 5:
                    print("  ...")
                
                return True
            else:
                print(f"❌ Directory listing failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_system_info_tool():
    """Test system info tool"""
    print(f"\n🖥️  Testing System Info Tool")
    print("=" * 35)
    
    try:
        from cybex.modules.agent_tools import AgentTools
        
        agent_tools = AgentTools()
        
        # Test system info
        print("Testing system info (CPU category)")
        
        execution_id = agent_tools.execute_tool(
            "get_system_info",
            {"category": "cpu"}
        )
        
        # Wait for completion
        time.sleep(2)
        
        execution = agent_tools.get_execution_status(execution_id)
        if execution and execution.result:
            result = execution.result
            
            if result.success:
                print(f"✅ System info retrieved successfully")
                
                # Parse JSON output
                import json
                try:
                    info = json.loads(result.output)
                    cpu_info = info.get('cpu', {})
                    
                    print(f"🔧 Physical cores: {cpu_info.get('physical_cores', 'N/A')}")
                    print(f"🔧 Logical cores: {cpu_info.get('logical_cores', 'N/A')}")
                    print(f"🔧 CPU usage: {cpu_info.get('usage_percent', 'N/A')}%")
                    
                except json.JSONDecodeError:
                    print(f"📄 Raw output: {result.output[:100]}...")
                
                return True
            else:
                print(f"❌ System info failed: {result.error}")
                return False
        else:
            print("❌ No result available")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_operation_monitor():
    """Test operation monitor"""
    print(f"\n📊 Testing Operation Monitor")
    print("=" * 35)
    
    try:
        from cybex.modules.operation_monitor import OperationMonitor, OperationType, OperationStatus
        
        monitor = OperationMonitor()
        
        # Start test operation
        op_id = monitor.start_operation(
            "Test Operation",
            OperationType.SYSTEM_ANALYSIS,
            "Testing operation monitoring"
        )
        
        print(f"✅ Started operation: {op_id}")
        
        # Update operation
        monitor.update_operation(op_id, progress=25.0, details="Processing...")
        print("✅ Updated operation progress to 25%")
        
        monitor.update_operation(op_id, progress=75.0, details="Almost done...")
        print("✅ Updated operation progress to 75%")
        
        # Complete operation
        monitor.complete_operation(op_id, result="Operation completed successfully")
        print("✅ Completed operation")
        
        # Check stats
        stats = monitor.get_stats()
        print(f"📊 Total operations: {stats['total_operations']}")
        print(f"📊 Successful operations: {stats['successful_operations']}")
        
        # Test display summary
        summary = monitor.get_display_summary()
        print(f"📋 Active operations: {summary['active_count']}")
        print(f"📋 Recent operations: {len(summary['recent_operations'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_enterprise_ui_integration():
    """Test Enterprise UI integration"""
    print(f"\n🎯 Testing Enterprise UI Integration")
    print("=" * 45)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        # Initialize UI (this should initialize agent tools)
        ui = CybexEnterpriseUI()
        
        print("✅ Enterprise UI initialized")
        
        # Check if agent tools are available
        if hasattr(ui, 'agent_tools') and ui.agent_tools:
            print("✅ Agent tools integrated")
            
            tools = ui.agent_tools.get_available_tools()
            print(f"✅ {len(tools)} tools available in UI")
        else:
            print("❌ Agent tools not integrated")
            return False
        
        # Check if operation monitor is available
        if hasattr(ui, 'operation_monitor') and ui.operation_monitor:
            print("✅ Operation monitor integrated")
        else:
            print("❌ Operation monitor not integrated")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_agent_tools_summary():
    """Show summary of agent tools features"""
    from cybex.interfaces.ui_cli_enterprise import SunriseColors
    
    summary = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                        {SunriseColors.ACCENT}🛠️  AGENT TOOLS FEATURES{SunriseColors.SUCCESS}                                ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🔧 AVAILABLE TOOLS:{SunriseColors.SUCCESS}                                                             ║
║   • execute_command    - Execute system commands (CMD/PowerShell)            ║
║   • list_directory     - List directory contents with details                ║
║   • get_system_info    - Get comprehensive system information                ║
║   • list_processes     - List running processes with filtering               ║
║   • network_scan       - Scan network connections and ports                  ║
║                                                                               ║
║ {SunriseColors.ACCENT}📊 OPERATION MONITORING:{SunriseColors.SUCCESS}                                                       ║
║   • Real-time operation tracking                                             ║
║   • Progress monitoring with visual indicators                               ║
║   • Execution history and statistics                                         ║
║   • Split-screen view in chat mode                                           ║
║                                                                               ║
║ {SunriseColors.ACCENT}💬 CHAT INTEGRATION:{SunriseColors.SUCCESS}                                                           ║
║   • /ops, /operations  - Toggle operations panel                             ║
║   • /tools             - Show available tools                                ║
║   • /tool <name>       - Execute tool directly                               ║
║   • AI can use tools automatically in responses                              ║
║                                                                               ║
║ {SunriseColors.ACCENT}🔒 SECURITY FEATURES:{SunriseColors.SUCCESS}                                                          ║
║   • Command whitelist/blacklist                                              ║
║   • Safe execution environment                                               ║
║   • Timeout protection                                                       ║
║   • Error handling and logging                                               ║
║                                                                               ║
║ {SunriseColors.ACCENT}🚀 USAGE EXAMPLES:{SunriseColors.SUCCESS}                                                             ║
║   • "Execute 'dir' command"                                                  ║
║   • "Show me system information"                                             ║
║   • "List running processes"                                                 ║
║   • "Check network connections"                                              ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
    print(summary)


def main():
    """Run agent tools tests"""
    print("🎯 Cybex Enterprise - Agent Tools Test")
    print("=" * 60)
    
    tests = [
        ("Agent Tools Initialization", test_agent_tools_initialization),
        ("System Command Tool", test_system_command_tool),
        ("File Operations Tool", test_file_operations_tool),
        ("System Info Tool", test_system_info_tool),
        ("Operation Monitor", test_operation_monitor),
        ("Enterprise UI Integration", test_enterprise_ui_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*15} {test_name} {'='*15}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed >= 4:  # At least 4 out of 6 tests should pass
        show_agent_tools_summary()
        
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Agent tools system is working!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}Your agent now has advanced tool capabilities!{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Try it now:{SunriseColors.RESET}")
        print(f"  1. python main_enterprise.py")
        print(f"  2. Start chatting and use /ops to see operations")
        print(f"  3. Use /tools to see available tools")
        print(f"  4. Try: /tool execute_command command='systeminfo'")
    else:
        print(f"⚠️  Some tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
