#!/usr/bin/env python3
"""
Test Dynamic Model Detection
Test del rilevamento dinamico dei modelli Ollama
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def test_ollama_connection():
    """Test connection to Ollama API"""
    print("🔌 Testing Ollama Connection")
    print("=" * 40)
    
    try:
        import requests
        
        print("Connecting to Ollama API...")
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        
        if response.status_code == 200:
            print("✅ Ollama API is accessible")
            
            data = response.json()
            models = data.get('models', [])
            
            print(f"📊 Found {len(models)} models:")
            
            for i, model in enumerate(models, 1):
                name = model.get('name', 'Unknown')
                size = model.get('size', 0)
                modified = model.get('modified_at', 'Unknown')
                
                # Convert size to human readable
                if size > 1024**3:  # GB
                    size_str = f"{size / (1024**3):.1f} GB"
                elif size > 1024**2:  # MB
                    size_str = f"{size / (1024**2):.1f} MB"
                else:
                    size_str = f"{size} bytes"
                
                print(f"  {i:2}. {name:<25} ({size_str})")
            
            return models
        else:
            print(f"❌ Ollama API returned status {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error connecting to Ollama: {e}")
        return []


def test_dynamic_model_loading():
    """Test dynamic model loading in Enterprise UI"""
    print(f"\n🤖 Testing Dynamic Model Loading")
    print("=" * 40)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI, SunriseColors
        
        print("Initializing Enterprise UI...")
        
        # Create UI instance (this will load models automatically)
        ui = CybexEnterpriseUI()
        
        print(f"✅ Enterprise UI initialized")
        print(f"📋 Models loaded: {len(ui.available_models)}")
        
        if ui.available_models:
            print(f"\n🎯 Available Models in Cybex:")
            for i, model in enumerate(ui.available_models, 1):
                print(f"  {i:2}. {model}")
        else:
            print(f"⚠️  No models loaded")
        
        return ui.available_models
        
    except Exception as e:
        print(f"❌ Error testing dynamic model loading: {e}")
        import traceback
        traceback.print_exc()
        return []


def test_model_refresh():
    """Test model refresh functionality"""
    print(f"\n🔄 Testing Model Refresh")
    print("=" * 30)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import CybexEnterpriseUI
        
        ui = CybexEnterpriseUI()
        
        initial_count = len(ui.available_models)
        print(f"Initial models: {initial_count}")
        
        # Test refresh
        print("Testing refresh functionality...")
        ui._refresh_models()
        
        after_refresh_count = len(ui.available_models)
        print(f"After refresh: {after_refresh_count}")
        
        if after_refresh_count >= initial_count:
            print("✅ Model refresh working")
            return True
        else:
            print("⚠️  Model count decreased after refresh")
            return False
            
    except Exception as e:
        print(f"❌ Error testing model refresh: {e}")
        return False


def compare_models():
    """Compare Ollama models with Cybex models"""
    print(f"\n📊 Comparing Model Lists")
    print("=" * 35)
    
    # Get models from Ollama API
    ollama_models = test_ollama_connection()
    ollama_names = [model.get('name', '') for model in ollama_models]
    
    # Get models from Cybex
    cybex_models = test_dynamic_model_loading()
    
    print(f"\n🔍 Comparison Results:")
    print(f"  Ollama API: {len(ollama_names)} models")
    print(f"  Cybex UI:   {len(cybex_models)} models")
    
    # Check if all Ollama models are in Cybex
    missing_in_cybex = []
    for model in ollama_names:
        if model and model not in cybex_models:
            missing_in_cybex.append(model)
    
    if missing_in_cybex:
        print(f"\n⚠️  Models in Ollama but not in Cybex:")
        for model in missing_in_cybex:
            print(f"    • {model}")
    else:
        print(f"\n✅ All Ollama models are available in Cybex")
    
    # Show your specific models
    your_models = [
        "devstral:24b",
        "gemma3:27b", 
        "gemma3:latest",
        "gemma3:4b",
        "AGtech:latest",
        "SocialAI:latest",
        "deepseek-ideas:latest",
        "deepseek-r1:1.5b",
        "deepseek-r1:latest",
        "deepseek-r1:8b"
    ]
    
    print(f"\n🎯 Your Specific Models Status:")
    for model in your_models:
        if model in cybex_models:
            print(f"  ✅ {model}")
        else:
            print(f"  ❌ {model} (not found)")
    
    return len(missing_in_cybex) == 0


def main():
    """Run dynamic model detection tests"""
    print("🎯 Cybex Enterprise - Dynamic Model Detection Test")
    print("=" * 60)
    
    tests = [
        ("Ollama Connection", lambda: len(test_ollama_connection()) > 0),
        ("Dynamic Model Loading", lambda: len(test_dynamic_model_loading()) > 0),
        ("Model Refresh", test_model_refresh),
        ("Model Comparison", compare_models)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                print(f"❌ {test_name}: FAILED")
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Dynamic model detection is working perfectly!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}All your Ollama models should now be available in Cybex Enterprise{SunriseColors.RESET}")
        print(f"\n{SunriseColors.ACCENT}🚀 Launch Cybex Enterprise:{SunriseColors.RESET}")
        print(f"   python main_enterprise.py")
        print(f"\n{SunriseColors.ACCENT}⚙️  Access Configuration:{SunriseColors.RESET}")
        print(f"   Press [C] in the main menu")
        print(f"   Use [R] to refresh models")
        print(f"   Use [A] to see all model details")
    else:
        print(f"⚠️  {total - passed} tests failed. Check the errors above.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
