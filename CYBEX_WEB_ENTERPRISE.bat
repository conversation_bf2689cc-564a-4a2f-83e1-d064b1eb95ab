@echo off
title CYBEX Enterprise - Web Interface
color 0B

echo.
echo    ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ██╗    ██╗███████╗██████╗ 
echo   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██║    ██║██╔════╝██╔══██╗
echo   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     ██║ █╗ ██║█████╗  ██████╔╝
echo   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██║███╗██║██╔══╝  ██╔══██╗
echo   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ╚███╔███╔╝███████╗██████╔╝
echo    ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝     ╚══╝╚══╝ ╚══════╝╚═════╝ 
echo.
echo           🚀 CYBEX ENTERPRISE - FUTURISTIC WEB INTERFACE
echo              Advanced AI Assistant with Modern UI
echo.

REM Check if we're in the correct directory
if not exist "cybex" (
    echo ❌ Error: cybex directory not found
    echo 💡 Please run this script from the CYBEX root directory
    pause
    exit /b 1
)

REM Check Python installation
echo 🐍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found in PATH
    echo 💡 Please install Python 3.8+ or add it to PATH
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% found

REM Check and install required web modules
echo 🔧 Checking web interface requirements...
python -c "import fastapi, uvicorn, websockets" >nul 2>&1
if errorlevel 1 (
    echo ❌ Missing web interface modules
    echo 💡 Installing FastAPI, Uvicorn, and WebSockets...
    pip install fastapi uvicorn websockets python-multipart
    if errorlevel 1 (
        echo ❌ Failed to install web modules
        pause
        exit /b 1
    )
)
echo ✅ Web interface modules available

REM Check Ollama server
echo 🤖 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Ollama server not responding
    echo 💡 AI features will be limited without Ollama
    echo 💡 To enable AI: Start Ollama server first
) else (
    echo ✅ Ollama server is running
    
    REM Get model count
    for /f %%i in ('curl -s http://localhost:11434/api/tags ^| python -c "import sys,json; data=json.load(sys.stdin); print(len(data.get('models',[])))" 2^>nul') do set MODEL_COUNT=%%i
    if defined MODEL_COUNT (
        echo 🤖 %MODEL_COUNT% AI models available
    )
)

echo.
echo 🌐 Starting CYBEX Enterprise Web Server...
echo 💡 Features enabled:
echo    • Modern Web Interface (React-like)
echo    • Real-time WebSocket Communication
echo    • Monaco Code Editor (VSCode)
echo    • 52+ Enterprise Tools
echo    • Performance Monitoring
echo    • Responsive Design
echo.
echo 🔗 Web Interface will open at: http://127.0.0.1:8080
echo 💡 Press Ctrl+C to stop the server
echo.

REM Start the web server
python cybex\interfaces\web_server.py

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ CYBEX Web Server exited with error
    echo 💡 Check the error messages above
    pause
) else (
    echo.
    echo ✅ CYBEX Web Server stopped successfully
    echo 👋 Thank you for using CYBEX Enterprise!
)

echo.
pause
