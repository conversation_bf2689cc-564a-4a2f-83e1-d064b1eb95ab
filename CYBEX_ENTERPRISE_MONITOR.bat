@echo off
title CYBEX ENTERPRISE - Ollama Monitor Edition
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                    🚀 CYBEX ENTERPRISE LAUNCHER                             ║
echo ║                   Advanced AI with Ollama Monitor                           ║
echo ║                        AGTECHdesigne - 2025                                 ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if we're in the correct directory
if not exist "cybex" (
    echo ❌ Error: cybex directory not found
    echo 💡 Please run this script from the CYBEX root directory
    pause
    exit /b 1
)

REM Check Python installation
echo 🐍 Checking Python installation...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found in PATH
    echo 💡 Please install Python 3.8+ or add it to PATH
    pause
    exit /b 1
)

REM Get Python version
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ Python %PYTHON_VERSION% found

REM Check required modules
echo 🔧 Checking required modules...
python -c "import tkinter, requests, psutil" >nul 2>&1
if errorlevel 1 (
    echo ❌ Missing required modules
    echo 💡 Installing required modules...
    pip install requests psutil
    if errorlevel 1 (
        echo ❌ Failed to install modules
        pause
        exit /b 1
    )
)
echo ✅ All modules available

REM Check Ollama server
echo 🤖 Checking Ollama server...
curl -s http://localhost:11434/api/tags >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Ollama server not responding
    echo 💡 Starting without Ollama integration...
    echo 💡 To use AI features, start Ollama server first
) else (
    echo ✅ Ollama server is running
    
    REM Get model count
    for /f %%i in ('curl -s http://localhost:11434/api/tags ^| python -c "import sys,json; data=json.load(sys.stdin); print(len(data.get('models',[])))"') do set MODEL_COUNT=%%i
    echo 🤖 %MODEL_COUNT% models available
)

echo.
echo 🎨 Starting CYBEX Enterprise GUI with Ollama Monitor...
echo 💡 Features enabled:
echo    • 52 Enterprise Tools
echo    • Real-time Ollama Monitor
echo    • Visual Activity Tracking
echo    • Response Time Monitoring
echo    • Model Switch Detection
echo.

REM Launch CYBEX Enterprise
python -c "from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI; app = CybexFuturisticGUI(); app.run()"

REM Check exit code
if errorlevel 1 (
    echo.
    echo ❌ CYBEX Enterprise exited with error
    echo 💡 Check the error messages above
    pause
) else (
    echo.
    echo ✅ CYBEX Enterprise closed successfully
    echo 👋 Thank you for using CYBEX Enterprise!
)

echo.
pause
