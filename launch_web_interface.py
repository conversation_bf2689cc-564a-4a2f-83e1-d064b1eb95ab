#!/usr/bin/env python3
"""
CYBEX Enterprise Web Interface Launcher
Advanced launcher with auto-browser opening and system checks
"""

import sys
import os
import time
import webbrowser
import subprocess
import threading
from pathlib import Path

def print_banner():
    """Print CYBEX banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 CYBEX ENTERPRISE WEB INTERFACE                       ║
║                      Futuristic AI Assistant UI                             ║
║                        Next-Gen Web Experience                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {sys.version.split()[0]} found")
    
    # Check required modules
    required_modules = [
        'fastapi',
        'uvicorn', 
        'websockets'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module} available")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module} missing")
    
    if missing_modules:
        print(f"\n💡 Installing missing modules: {', '.join(missing_modules)}")
        try:
            subprocess.check_call([
                sys.executable, '-m', 'pip', 'install'
            ] + missing_modules)
            print("✅ Modules installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install modules")
            return False
    
    return True

def check_ollama():
    """Check Ollama server status"""
    print("\n🤖 Checking Ollama server...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=3)
        if response.status_code == 200:
            data = response.json()
            model_count = len(data.get('models', []))
            print(f"✅ Ollama server running with {model_count} models")
            return True
        else:
            print("⚠️ Ollama server responding but with errors")
            return False
    except Exception:
        print("⚠️ Ollama server not available")
        print("💡 AI features will be limited")
        print("💡 Start Ollama server for full functionality")
        return False

def wait_for_server(host="127.0.0.1", port=8080, timeout=30):
    """Wait for web server to start"""
    import socket
    
    print(f"⏳ Waiting for server to start on {host}:{port}...")
    
    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex((host, port))
            sock.close()
            
            if result == 0:
                print("✅ Server is ready!")
                return True
                
        except Exception:
            pass
        
        time.sleep(0.5)
    
    print("❌ Server failed to start within timeout")
    return False

def open_browser(url, delay=2):
    """Open browser after delay"""
    def delayed_open():
        time.sleep(delay)
        print(f"🌐 Opening browser: {url}")
        try:
            webbrowser.open(url)
        except Exception as e:
            print(f"⚠️ Could not open browser automatically: {e}")
            print(f"💡 Please open manually: {url}")
    
    thread = threading.Thread(target=delayed_open, daemon=True)
    thread.start()

def main():
    """Main launcher function"""
    print_banner()
    
    # Check if we're in the right directory
    if not Path("cybex").exists():
        print("❌ Error: cybex directory not found")
        print("💡 Please run this script from the CYBEX root directory")
        input("Press Enter to exit...")
        return 1
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Requirements check failed")
        input("Press Enter to exit...")
        return 1
    
    # Check Ollama
    ollama_available = check_ollama()
    
    # Setup environment
    os.environ['PYTHONPATH'] = str(Path.cwd())
    
    print("\n🚀 Starting CYBEX Enterprise Web Server...")
    print("💡 Features:")
    print("   • Modern React-like Web Interface")
    print("   • Real-time WebSocket Communication") 
    print("   • Monaco Code Editor (VSCode)")
    print("   • 52+ Enterprise Tools")
    print("   • Performance Monitoring")
    print("   • Responsive Futuristic Design")
    
    if ollama_available:
        print("   • AI Chat with Dynamic Timeouts")
        print("   • Model Performance Analytics")
    
    # Server configuration
    host = "127.0.0.1"
    port = 8080
    url = f"http://{host}:{port}"
    
    print(f"\n🔗 Server will start at: {url}")
    print("💡 Browser will open automatically")
    print("💡 Press Ctrl+C to stop the server")
    
    # Start browser opening thread
    open_browser(url, delay=3)
    
    try:
        # Import and start server
        sys.path.insert(0, str(Path.cwd()))
        from cybex.interfaces.web_server import CybexWebServer
        
        server = CybexWebServer(host=host, port=port)
        server.run()
        
    except KeyboardInterrupt:
        print("\n\n🛑 Server stopped by user")
        print("✅ CYBEX Enterprise Web Interface closed")
        return 0
        
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("💡 Check the error details above")
        input("Press Enter to exit...")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
