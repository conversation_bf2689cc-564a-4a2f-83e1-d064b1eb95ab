# Web Browsing Requirements for CYBEX Enterprise
# Install with: pip install -r requirements_web.txt

# Core web browsing
requests>=2.31.0
beautifulsoup4>=4.12.0
html2text>=2020.1.16
lxml>=4.9.0

# Selenium for JavaScript-heavy sites
selenium>=4.15.0
webdriver-manager>=4.0.0

# URL parsing and validation
urllib3>=2.0.0
validators>=0.22.0

# Content analysis
readability>=0.3.1
textstat>=0.7.3

# Optional: Advanced parsing
newspaper3k>=0.2.8
python-dateutil>=2.8.2

# Security and headers
certifi>=2023.7.22
cryptography>=41.0.0

# Performance and caching
cachetools>=5.3.0
diskcache>=5.6.0
