#!/usr/bin/env python3
"""
Cybex Core - Enterprise System Core
Main system orchestrator with enterprise features
"""

import os
import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

@dataclass
class SystemConfig:
    """System configuration"""
    debug: bool = False
    log_level: str = "INFO"
    data_dir: str = "data"
    logs_dir: str = "logs"
    config_dir: str = "config"

class LogManager:
    """Enterprise logging manager"""
    
    def __init__(self, config: SystemConfig):
        self.config = config
        self.loggers = {}
        self._setup_logging()
    
    def _setup_logging(self):
        """Setup enterprise logging"""
        log_dir = Path(self.config.logs_dir)
        log_dir.mkdir(exist_ok=True)
        
        # Configure root logger
        logging.basicConfig(
            level=getattr(logging, self.config.log_level),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_dir / 'cybex.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get logger instance"""
        if name not in self.loggers:
            self.loggers[name] = logging.getLogger(name)
        return self.loggers[name]

class ConfigManager:
    """Enterprise configuration manager"""
    
    def __init__(self, config_dir: str = "config"):
        self.config_dir = Path(config_dir)
        self.config_dir.mkdir(exist_ok=True)
        self.config = {}
        self._load_default_config()
    
    def _load_default_config(self):
        """Load default configuration"""
        self.config = {
            'ollama': {
                'host': 'localhost',
                'port': 11434,
                'model': 'gemma3:4b',
                'timeout': 180
            },
            'gui': {
                'theme': 'futuristic',
                'width': 1400,
                'height': 900
            },
            'enterprise': {
                'company': 'CYBEX Enterprise',
                'version': '2.0.0'
            }
        }
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Get configuration section"""
        return self.config.get(section, {})
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value"""
        return self.config.get(key, default)

class CybexCore:
    """Main Cybex Enterprise Core System"""
    
    def __init__(self):
        self.config = SystemConfig()
        self.log_manager = LogManager(self.config)
        self.config_manager = ConfigManager()
        self.logger = self.log_manager.get_logger(__name__)
        
        self.logger.info("Cybex Core initialized successfully")
    
    def get_system_info(self) -> Dict[str, Any]:
        """Get system information"""
        return {
            'version': '2.0.0',
            'platform': sys.platform,
            'python_version': sys.version,
            'working_directory': os.getcwd()
        }
    
    def shutdown(self):
        """Graceful shutdown"""
        self.logger.info("Cybex Core shutting down")
