"""
Cybex Core Module
Main logic and decision-making engine for the Cybernetic Expert Agent
"""

import os
import platform
import logging
from typing import Dict, List, Optional, Tuple, Any, Callable
from enum import Enum

from ..modules.config_manager import ConfigManager
from ..modules.security_manager import SecurityManager
from ..modules.log_manager import LogManager
from ..modules.system_monitor import SystemMonitor
from ..modules.disk_manager import DiskManager
from ..modules.database_manager import DatabaseManager
from ..modules.system_snapshot import SystemSnapshot
from ..modules.agent_planner import AgentPlanner
from ..modules.agent_executor import AgentExecutor


class OperationMode(Enum):
    """Operating modes for Cybex"""
    CHAT = "chat"
    AGENT = "agent"


class ConfirmationLevel(Enum):
    """Confirmation levels for operations"""
    STRICT = "strict"
    MODERATE = "moderate"
    MINIMAL = "minimal"


class CybexCore:
    """
    Core engine for Cybex AI agent
    Handles decision making, mode switching, and operation coordination
    """
    
    def __init__(self, config_path: str = "cybex/config/cybex_config.yaml"):
        """Initialize Cybex core with configuration"""
        self.config_manager = ConfigManager(config_path)
        self.security_manager = SecurityManager(self.config_manager)
        self.log_manager = LogManager(self.config_manager)
        
        # Initialize core properties
        self.mode = OperationMode(self.config_manager.get('mode', 'chat'))
        self.confirm_level = ConfirmationLevel(self.config_manager.get('confirm_level', 'strict'))
        self.system_type = self._detect_system()
        
        # Initialize logger
        self.logger = self.log_manager.get_logger(__name__)
        self.logger.info("Cybex Core initialized successfully")
        
        # Command history and session state
        self.command_history: List[Dict] = []
        self.session_active = False
        self.session_id = None

        # Initialize SVP components if enabled
        self.system_monitor = None
        self.disk_manager = None
        self.database_manager = None
        self.system_snapshot = None
        self.agent_planner = None
        self.agent_executor = None

        # Initialize SVP modules
        self._init_svp_modules()

    def _init_svp_modules(self) -> None:
        """Initialize SVP (Strategic Viable Product) modules"""
        try:
            # Check if SVP features are enabled
            from cybex import SVP_PHASE
            if not SVP_PHASE:
                return

            # Initialize database manager
            db_config = self.config_manager.get_section('database')
            if db_config.get('enabled', True):
                self.database_manager = DatabaseManager(self.config_manager, self.log_manager)
                self.logger.info("Database manager initialized")

            # Initialize system monitor
            monitoring_config = self.config_manager.get_section('monitoring')
            if monitoring_config.get('enabled', True):
                self.system_monitor = SystemMonitor(self.config_manager, self.log_manager)
                self.logger.info("System monitor initialized")

            # Initialize disk manager
            disk_config = self.config_manager.get_section('disk_management')
            if disk_config.get('enabled', True):
                # We'll need command_executor for disk_manager, so we'll initialize it later
                pass

            # Initialize system snapshot
            snapshot_config = self.config_manager.get_section('snapshots')
            if snapshot_config.get('enabled', True):
                # We'll need command_executor for snapshots, so we'll initialize it later
                pass

        except Exception as e:
            self.logger.error(f"Failed to initialize SVP modules: {e}")

    def _init_agent_modules(self, command_executor, ollama_interface) -> None:
        """Initialize agent-specific modules (called after command_executor is available)"""
        try:
            from cybex import SVP_PHASE
            if not SVP_PHASE:
                return

            # Initialize disk manager
            disk_config = self.config_manager.get_section('disk_management')
            if disk_config.get('enabled', True):
                self.disk_manager = DiskManager(
                    self.config_manager,
                    self.log_manager,
                    command_executor
                )
                self.logger.info("Disk manager initialized")

            # Initialize system snapshot
            snapshot_config = self.config_manager.get_section('snapshots')
            if snapshot_config.get('enabled', True):
                self.system_snapshot = SystemSnapshot(
                    self.config_manager,
                    self.log_manager,
                    command_executor
                )
                self.logger.info("System snapshot initialized")

            # Initialize agent planner
            self.agent_planner = AgentPlanner(
                self.config_manager,
                self.security_manager,
                self.log_manager,
                ollama_interface
            )
            self.logger.info("Agent planner initialized")

            # Initialize agent executor
            self.agent_executor = AgentExecutor(
                self.config_manager,
                self.security_manager,
                self.log_manager,
                command_executor,
                self.agent_planner
            )
            self.logger.info("Agent executor initialized")

        except Exception as e:
            self.logger.error(f"Failed to initialize agent modules: {e}")
        
    def _detect_system(self) -> str:
        """Detect the operating system"""
        system_config = self.config_manager.get('system', 'auto')
        
        if system_config != 'auto':
            return system_config
            
        system = platform.system().lower()
        if system == 'windows':
            return 'windows'
        elif system in ['linux', 'darwin']:
            return 'linux'
        else:
            self.logger.warning(f"Unknown system type: {system}, defaulting to linux")
            return 'linux'
    
    def start_session(self, username: str = "") -> bool:
        """Start a new Cybex session"""
        try:
            # Generate session ID
            import uuid
            self.session_id = str(uuid.uuid4())
            self.session_active = True

            # Log session start
            self.logger.info(f"Cybex session {self.session_id} started in {self.mode.value} mode")
            self._log_system_info()

            # Record session in database if available
            if self.database_manager:
                self.database_manager.create_session(
                    self.session_id,
                    username=username,
                    mode=self.mode.value
                )

                # Update user last login if username provided
                if username:
                    self.database_manager.update_last_login(username)

            # Create system snapshot if enabled
            if self.system_snapshot and self.config_manager.get('snapshots.auto_snapshot_before_agent', False):
                self.system_snapshot.create_snapshot(
                    f"session_start_{self.session_id[:8]}",
                    f"Automatic snapshot at session start ({self.mode.value} mode)"
                )

            return True
        except Exception as e:
            self.logger.error(f"Failed to start session: {e}")
            return False
    
    def end_session(self) -> bool:
        """End the current Cybex session"""
        try:
            self.session_active = False
            self.logger.info(f"Cybex session {self.session_id} ended")

            # Record session end in database if available
            if self.database_manager and self.session_id:
                self.database_manager.end_session(
                    self.session_id,
                    commands_executed=len(self.command_history),
                    session_data={
                        'mode': self.mode.value,
                        'system_type': self.system_type,
                        'total_commands': len(self.command_history)
                    }
                )

            # Stop system monitoring if active
            if self.system_monitor:
                # Don't stop monitoring completely, just note session end
                pass

            self._save_session_data()
            return True
        except Exception as e:
            self.logger.error(f"Failed to end session: {e}")
            return False
    
    def switch_mode(self, new_mode: str) -> bool:
        """Switch between chat and agent modes"""
        try:
            old_mode = self.mode
            self.mode = OperationMode(new_mode)
            self.config_manager.set('mode', new_mode)
            self.logger.info(f"Mode switched from {old_mode.value} to {self.mode.value}")
            return True
        except ValueError:
            self.logger.error(f"Invalid mode: {new_mode}")
            return False
    
    def is_operation_safe(self, command: str) -> Tuple[bool, str]:
        """
        Determine if an operation is safe to execute
        Returns (is_safe, reason)
        """
        return self.security_manager.validate_command(command)
    
    def requires_confirmation(self, command: str) -> bool:
        """Check if a command requires user confirmation"""
        if self.confirm_level == ConfirmationLevel.STRICT:
            return self.security_manager.is_system_altering(command)
        elif self.confirm_level == ConfirmationLevel.MODERATE:
            return self.security_manager.is_critical_operation(command)
        elif self.confirm_level == ConfirmationLevel.MINIMAL:
            return self.security_manager.is_destructive_operation(command)
        return False
    
    def log_command(self, command: str, result: str, success: bool) -> None:
        """Log command execution details"""
        log_entry = {
            'command': command,
            'result': result,
            'success': success,
            'timestamp': self.log_manager.get_timestamp(),
            'mode': self.mode.value
        }
        self.command_history.append(log_entry)
        
        if success:
            self.logger.info(f"Command executed: {command}")
        else:
            self.logger.error(f"Command failed: {command} - {result}")
    
    def get_system_info(self) -> Dict:
        """Get basic system information"""
        return {
            'system': self.system_type,
            'platform': platform.platform(),
            'python_version': platform.python_version(),
            'mode': self.mode.value,
            'confirm_level': self.confirm_level.value,
            'session_active': self.session_active
        }
    
    def _log_system_info(self) -> None:
        """Log system information at session start"""
        info = self.get_system_info()
        self.logger.info(f"System: {info['platform']}")
        self.logger.info(f"Python: {info['python_version']}")
        self.logger.info(f"Mode: {info['mode']}")
    
    def _save_session_data(self) -> None:
        """Save session data for persistence"""
        try:
            # Session data is now saved in database automatically
            self.logger.debug("Session data saved")
        except Exception as e:
            self.logger.error(f"Failed to save session data: {e}")

    # SVP Feature Access Methods
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status including SVP features"""
        status = self.get_system_info()

        # Add SVP status information
        if self.system_monitor:
            status['monitoring'] = {
                'enabled': True,
                'current_state': self.system_monitor.get_current_state(),
                'performance_summary': self.system_monitor.get_performance_summary(),
                'alerts': len(self.system_monitor.get_alerts())
            }

        if self.disk_manager:
            status['disk_health'] = self.disk_manager.get_disk_health_summary()

        if self.database_manager:
            status['database'] = self.database_manager.get_database_stats()

        if self.agent_executor:
            status['agent'] = {
                'metrics': self.agent_executor.get_metrics(),
                'active_executions': len(self.agent_executor.active_executions)
            }

        return status

    def create_agent_plan(self, goal: str, context: Optional[Dict] = None) -> Optional[str]:
        """Create an agent execution plan"""
        if not self.agent_planner:
            self.logger.error("Agent planner not available")
            return None

        try:
            plan = self.agent_planner.create_plan_from_goal(goal, context)

            # Save plan to database if available
            if self.database_manager and plan:
                plan_data = {
                    'id': plan.id,
                    'name': plan.name,
                    'description': plan.description,
                    'goal': plan.goal,
                    'status': plan.status.value,
                    'created_by': context.get('username', '') if context else '',
                    'plan_data': plan
                }
                self.database_manager.save_agent_plan(plan_data)

            return plan.id if plan else None

        except Exception as e:
            self.logger.error(f"Failed to create agent plan: {e}")
            return None

    def execute_agent_plan(self, plan_id: str,
                          progress_callback: Optional[Callable] = None) -> bool:
        """Execute an agent plan"""
        if not self.agent_executor or not self.agent_planner:
            self.logger.error("Agent execution not available")
            return False

        try:
            plan = self.agent_planner.get_plan(plan_id)
            if not plan:
                self.logger.error(f"Plan not found: {plan_id}")
                return False

            # Create snapshot before execution if enabled
            if (self.system_snapshot and
                self.config_manager.get('snapshots.auto_snapshot_before_agent', False)):
                snapshot_id = self.system_snapshot.create_snapshot(
                    f"before_plan_{plan_id[:8]}",
                    f"Snapshot before executing plan: {plan.name}"
                )
                self.logger.info(f"Created snapshot {snapshot_id} before plan execution")

            # Execute plan
            success = self.agent_executor.execute_plan(plan, progress_callback)

            # Update plan in database
            if self.database_manager:
                plan_data = {
                    'id': plan.id,
                    'name': plan.name,
                    'description': plan.description,
                    'goal': plan.goal,
                    'status': plan.status.value,
                    'plan_data': plan
                }
                self.database_manager.save_agent_plan(plan_data)

            return success

        except Exception as e:
            self.logger.error(f"Failed to execute agent plan: {e}")
            return False

    def get_system_recommendations(self) -> List[Dict[str, Any]]:
        """Get system optimization recommendations"""
        recommendations = []

        # System monitoring recommendations
        if self.system_monitor:
            analysis = self.system_monitor.analyze_performance()
            if analysis.get('recommendations'):
                for rec in analysis['recommendations']:
                    recommendations.append({
                        'category': 'performance',
                        'type': 'system',
                        'message': rec,
                        'priority': 'medium'
                    })

        # Disk management recommendations
        if self.disk_manager:
            disk_recs = self.disk_manager.get_disk_recommendations()
            for disk_rec in disk_recs:
                for rec in disk_rec.get('recommendations', []):
                    recommendations.append({
                        'category': 'disk',
                        'type': rec['category'],
                        'message': rec['message'],
                        'action': rec['action'],
                        'priority': rec['priority'],
                        'device': disk_rec['device']
                    })

        return recommendations

    def perform_system_optimization(self) -> Dict[str, Any]:
        """Perform automated system optimization"""
        optimization_result = {
            'success': True,
            'optimizations': [],
            'errors': []
        }

        try:
            # Disk cleanup if enabled
            if self.disk_manager:
                cleanup_result = self.disk_manager.auto_cleanup_if_needed()
                if cleanup_result:
                    optimization_result['optimizations'].append({
                        'type': 'disk_cleanup',
                        'result': cleanup_result
                    })

            # Database cleanup
            if self.database_manager:
                cleanup_stats = self.database_manager.cleanup_old_data(days=30)
                if cleanup_stats:
                    optimization_result['optimizations'].append({
                        'type': 'database_cleanup',
                        'result': cleanup_stats
                    })

            return optimization_result

        except Exception as e:
            optimization_result['success'] = False
            optimization_result['errors'].append(str(e))
            self.logger.error(f"System optimization failed: {e}")
            return optimization_result
