#!/usr/bin/env python3
"""
Cybex Futuristic GUI Launcher
Interfaccia grafica futuristica con terminale integrato
"""

import os
import sys
import time
import tkinter as tk
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def check_requirements():
    """Check system requirements"""
    print("🔍 Checking system requirements for Futuristic GUI...")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    
    # Check required modules
    required_modules = [
        'tkinter', 'psutil', 'requests', 'threading', 'queue'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            if module == 'tkinter':
                import tkinter
                import tkinter.ttk
                import tkinter.scrolledtext
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError:
            missing_modules.append(module)
            print(f"❌ {module} missing")
    
    if missing_modules:
        print(f"\n📦 Install missing modules:")
        for module in missing_modules:
            if module == 'tkinter':
                print("   • tkinter: Usually included with Python, try reinstalling Python")
            else:
                print(f"   • pip install {module}")
        return False
    
    return True

def show_banner():
    """Show futuristic banner"""
    banner = """
╔═══════════════════════════════════════════════════════════════════════════════╗
║                                                                               ║
║  ◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤◢◤  ║
║                                                                               ║
║   ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗    ███████╗██╗   ██╗████████╗██╗   ║
║   ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝    ██╔════╝██║   ██║╚══██╔══╝██║   ║
║   ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝     █████╗  ██║   ██║   ██║   ██║   ║
║   ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗     ██╔══╝  ██║   ██║   ██║   ╚═╝   ║
║   ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗    ██║     ╚██████╔╝   ██║   ██╗   ║
║   ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝    ╚═╝      ╚═════╝    ╚═╝   ╚═╝   ║
║                                                                               ║
║  ◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣◥◣  ║
║                                                                               ║
║                        🚀 FUTURISTIC AI TERMINAL                             ║
║                      Next-Gen Graphical Interface                            ║
║                                                                               ║
║  🎯 Features:                                                                 ║
║    • Cyberpunk/Futuristic Design                                             ║
║    • Integrated CMD Terminal                                                  ║
║    • Real-time System Monitoring                                             ║
║    • AI Command Processing                                                    ║
║    • Animated UI Elements                                                     ║
║    • Quick Action Buttons                                                     ║
║    • Advanced Tool Integration                                                ║
║                                                                               ║
║  💡 Experience:                                                               ║
║    • Neon Colors & Glowing Effects                                           ║
║    • Command History & Auto-complete                                         ║
║    • Split-panel Layout                                                       ║
║    • Real-time Status Updates                                                ║
║    • Typewriter Animation Effects                                            ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝
"""
    print(banner)

def check_display():
    """Check display capabilities"""
    print("\n🖥️  Checking display capabilities...")
    
    try:
        # Test basic tkinter
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        # Check screen resolution
        width = root.winfo_screenwidth()
        height = root.winfo_screenheight()
        
        print(f"✅ Display resolution: {width}x{height}")
        
        if width < 1024 or height < 768:
            print("⚠️  Low resolution detected. Interface may not display optimally.")
            print("💡 Recommended: 1400x900 or higher")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Display check failed: {e}")
        print("💡 Make sure you have a graphical environment available")
        return False

def check_fonts():
    """Check available fonts"""
    print("\n🔤 Checking fonts...")
    
    try:
        import tkinter.font as tkfont
        
        root = tk.Tk()
        root.withdraw()
        
        available_fonts = tkfont.families()
        
        # Check for preferred fonts
        preferred_fonts = ['Consolas', 'Monaco', 'Courier New', 'Orbitron']
        found_fonts = []
        
        for font in preferred_fonts:
            if font in available_fonts:
                found_fonts.append(font)
                print(f"✅ {font}")
            else:
                print(f"⚠️  {font} not available")
        
        if not found_fonts:
            print("⚠️  No preferred fonts found, using system defaults")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Font check failed: {e}")
        return True  # Non-critical

def main():
    """Main launcher"""
    show_banner()
    
    print("🔧 Initializing Cybex Futuristic GUI...")
    
    # Check requirements
    if not check_requirements():
        print("\n❌ System requirements not met")
        input("Press Enter to exit...")
        return
    
    # Check display
    if not check_display():
        print("\n❌ Display requirements not met")
        choice = input("Continue anyway? (Y/n): ").strip().lower()
        if choice in ['n', 'no']:
            return
    
    # Check fonts (non-critical)
    check_fonts()
    
    # Check Ollama (optional)
    print("\n🤖 Checking AI capabilities...")
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=3)
        if response.status_code == 200:
            print("✅ Ollama AI server available")
        else:
            print("⚠️  Ollama server not responding")
    except:
        print("⚠️  Ollama not available - AI features will be limited")
    
    print("\n🚀 Starting Cybex Futuristic GUI...")
    print("💡 Loading interface components...")
    
    # Small delay for effect
    for i in range(3):
        print(".", end="", flush=True)
        time.sleep(0.5)
    print()
    
    try:
        # Import and start GUI
        from cybex.interfaces.ui_futuristic_gui import CybexFuturisticGUI
        
        print("✅ GUI components loaded")
        print("🎨 Initializing futuristic interface...")
        
        app = CybexFuturisticGUI()
        
        print("🎉 Cybex Futuristic GUI started!")
        print("💡 Use the terminal on the left and controls on the right")
        print("🎯 Try: 'scansiona disco C' or click Quick Actions")
        
        app.run()
        
    except ImportError as e:
        print(f"❌ Failed to import GUI components: {e}")
        print("💡 Make sure all Cybex files are in the correct location")
        input("Press Enter to exit...")
    
    except Exception as e:
        print(f"❌ Failed to start GUI: {e}")
        print("💡 Try running from command line to see detailed errors")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")
    
    print("\n👋 Cybex Futuristic GUI session ended")


if __name__ == "__main__":
    main()
