#!/usr/bin/env python3
"""
Cybex Enterprise Agent Tools
Integrazione dei tool enterprise nell'agent
"""

import json
import time
from typing import Dict, Any, Optional
from pathlib import Path

try:
    from .enterprise_tools import (
        EnterpriseSecurityManager,
        EnterpriseBackupManager,
        EnterprisePerformanceAnalyzer,
        EnterpriseNetworkManager
    )
except ImportError:
    # Fallback for testing
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent))
    from enterprise_tools import (
        EnterpriseSecurityManager,
        EnterpriseBackupManager,
        EnterprisePerformanceAnalyzer,
        EnterpriseNetworkManager
    )

class EnterpriseAgentTools:
    """Tool enterprise per l'agent AI"""
    
    def __init__(self, log_manager):
        self.log_manager = log_manager
        
        # Initialize enterprise managers
        self.security_manager = EnterpriseSecurityManager(log_manager)
        self.backup_manager = EnterpriseBackupManager(log_manager)
        self.performance_analyzer = EnterprisePerformanceAnalyzer(log_manager)
        self.network_manager = EnterpriseNetworkManager(log_manager)
        
        # Register enterprise tools
        self.enterprise_tools = {
            "security_audit": self.security_audit,
            "security_report": self.security_report,
            "create_backup": self.create_backup,
            "execute_backup": self.execute_backup,
            "list_backups": self.list_backups,
            "performance_analysis": self.performance_analysis,
            "performance_report": self.performance_report,
            "network_security_scan": self.network_security_scan,
            "bandwidth_monitor": self.bandwidth_monitor,
            "system_hardening": self.system_hardening,
            "compliance_check": self.compliance_check,
            "enterprise_health_check": self.enterprise_health_check
        }
    
    def get_available_tools(self) -> Dict[str, str]:
        """Ottieni lista tool enterprise disponibili"""
        return {
            "security_audit": "Comprehensive security audit of the system",
            "security_report": "Generate detailed security report",
            "create_backup": "Create backup job for files/directories",
            "execute_backup": "Execute existing backup job",
            "list_backups": "List all backup jobs and their status",
            "performance_analysis": "Analyze system performance metrics",
            "performance_report": "Generate performance analysis report",
            "network_security_scan": "Scan network for security issues",
            "bandwidth_monitor": "Monitor network bandwidth usage",
            "system_hardening": "Apply security hardening recommendations",
            "compliance_check": "Check system compliance with standards",
            "enterprise_health_check": "Complete enterprise system health check"
        }
    
    def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Esegui tool enterprise"""
        try:
            if tool_name not in self.enterprise_tools:
                return {
                    "success": False,
                    "error": f"Unknown enterprise tool: {tool_name}",
                    "available_tools": list(self.enterprise_tools.keys())
                }
            
            self.log_manager.log_info(f"Executing enterprise tool: {tool_name}")
            
            # Execute tool
            result = self.enterprise_tools[tool_name](parameters)
            
            self.log_manager.log_info(f"Enterprise tool {tool_name} completed successfully")
            
            return {
                "success": True,
                "tool": tool_name,
                "result": result,
                "timestamp": time.time()
            }
            
        except Exception as e:
            self.log_manager.log_error(f"Enterprise tool {tool_name} failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name
            }
    
    def security_audit(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Esegui audit sicurezza"""
        try:
            audit_results = self.security_manager.security_audit()
            
            return {
                "audit_results": [
                    {
                        "category": result.category,
                        "status": result.status,
                        "description": result.description,
                        "recommendation": result.recommendation,
                        "risk_level": result.risk_level
                    }
                    for result in audit_results
                ],
                "total_checks": len(audit_results),
                "critical_issues": len([r for r in audit_results if r.status == "critical"]),
                "warnings": len([r for r in audit_results if r.status == "warning"]),
                "secure_items": len([r for r in audit_results if r.status == "secure"])
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def security_report(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Genera report sicurezza"""
        try:
            report = self.security_manager.generate_security_report()
            return {
                "report": report,
                "format": "text"
            }
        except Exception as e:
            return {"error": str(e)}
    
    def create_backup(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Crea job backup"""
        try:
            source = parameters.get("source", "")
            destination = parameters.get("destination")
            schedule = parameters.get("schedule", "manual")
            
            if not source:
                return {"error": "Source path is required"}
            
            job_id = self.backup_manager.create_backup_job(source, destination, schedule)
            
            return {
                "job_id": job_id,
                "source": source,
                "destination": destination,
                "schedule": schedule,
                "status": "created"
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def execute_backup(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Esegui backup"""
        try:
            job_id = parameters.get("job_id", "")
            
            if not job_id:
                return {"error": "Job ID is required"}
            
            success = self.backup_manager.execute_backup(job_id)
            
            return {
                "job_id": job_id,
                "success": success,
                "status": "completed" if success else "failed"
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def list_backups(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Lista backup"""
        try:
            backups = self.backup_manager.list_backups()
            return {
                "backups": backups,
                "total_jobs": len(backups)
            }
        except Exception as e:
            return {"error": str(e)}
    
    def performance_analysis(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Analisi performance"""
        try:
            metrics = self.performance_analyzer.analyze_performance()
            
            return {
                "metrics": [
                    {
                        "name": metric.name,
                        "value": metric.value,
                        "unit": metric.unit,
                        "status": metric.status,
                        "threshold": metric.threshold
                    }
                    for metric in metrics
                ],
                "critical_metrics": len([m for m in metrics if m.status == "critical"]),
                "warning_metrics": len([m for m in metrics if m.status == "warning"]),
                "good_metrics": len([m for m in metrics if m.status == "good"])
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def performance_report(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Report performance"""
        try:
            report = self.performance_analyzer.generate_performance_report()
            return {
                "report": report,
                "format": "text"
            }
        except Exception as e:
            return {"error": str(e)}
    
    def network_security_scan(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Scansione sicurezza rete"""
        try:
            scan_results = self.network_manager.network_security_scan()
            return scan_results
        except Exception as e:
            return {"error": str(e)}
    
    def bandwidth_monitor(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Monitor bandwidth"""
        try:
            duration = parameters.get("duration", 10)
            bandwidth_data = self.network_manager.bandwidth_monitor(duration)
            return bandwidth_data
        except Exception as e:
            return {"error": str(e)}
    
    def system_hardening(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Hardening sistema"""
        try:
            # This would implement actual hardening steps
            # For now, return recommendations
            return {
                "status": "analysis_complete",
                "recommendations": [
                    "Enable Windows Defender or install antivirus",
                    "Enable Windows Firewall",
                    "Enable User Account Control (UAC)",
                    "Install latest security updates",
                    "Review and disable unnecessary services",
                    "Configure strong password policy",
                    "Enable audit logging",
                    "Review user accounts and permissions"
                ],
                "note": "Hardening recommendations generated. Manual implementation required for safety."
            }
        except Exception as e:
            return {"error": str(e)}
    
    def compliance_check(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Controllo compliance"""
        try:
            standard = parameters.get("standard", "basic")
            
            # Basic compliance check implementation
            checks = []
            
            # Security audit as part of compliance
            audit_results = self.security_manager.security_audit()
            
            for result in audit_results:
                checks.append({
                    "name": f"Security - {result.category}",
                    "status": "pass" if result.status == "secure" else "fail",
                    "description": result.description,
                    "recommendation": result.recommendation
                })
            
            passed = len([c for c in checks if c["status"] == "pass"])
            total = len(checks)
            compliance_score = (passed / total * 100) if total > 0 else 0
            
            return {
                "standard": standard,
                "compliance_score": compliance_score,
                "checks": checks,
                "passed": passed,
                "failed": total - passed,
                "recommendations": [c["recommendation"] for c in checks if c["status"] == "fail"]
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def enterprise_health_check(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Health check completo enterprise"""
        try:
            health_report = {
                "timestamp": time.time(),
                "overall_status": "unknown",
                "components": {}
            }
            
            # Security check
            try:
                security_results = self.security_audit({})
                critical_security = security_results.get("critical_issues", 0)
                health_report["components"]["security"] = {
                    "status": "critical" if critical_security > 0 else "good",
                    "critical_issues": critical_security,
                    "details": security_results
                }
            except Exception as e:
                health_report["components"]["security"] = {"status": "error", "error": str(e)}
            
            # Performance check
            try:
                performance_results = self.performance_analysis({})
                critical_performance = performance_results.get("critical_metrics", 0)
                health_report["components"]["performance"] = {
                    "status": "critical" if critical_performance > 0 else "good",
                    "critical_metrics": critical_performance,
                    "details": performance_results
                }
            except Exception as e:
                health_report["components"]["performance"] = {"status": "error", "error": str(e)}
            
            # Network check
            try:
                network_results = self.network_security_scan({})
                security_issues = len(network_results.get("security_issues", []))
                health_report["components"]["network"] = {
                    "status": "warning" if security_issues > 0 else "good",
                    "security_issues": security_issues,
                    "details": network_results
                }
            except Exception as e:
                health_report["components"]["network"] = {"status": "error", "error": str(e)}
            
            # Determine overall status
            component_statuses = [comp.get("status", "error") for comp in health_report["components"].values()]
            
            if "critical" in component_statuses:
                health_report["overall_status"] = "critical"
            elif "warning" in component_statuses:
                health_report["overall_status"] = "warning"
            elif "error" in component_statuses:
                health_report["overall_status"] = "error"
            else:
                health_report["overall_status"] = "good"
            
            return health_report
            
        except Exception as e:
            return {"error": str(e)}
