#!/usr/bin/env python3
"""
CYBEX Enterprise Excel Demo
Demonstrates Excel creation capabilities with sample data
"""

import sys
from pathlib import Path
import json
from datetime import datetime, timedelta
import random

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from cybex.modules.excel_tools import ExcelTools
from cybex.modules.log_manager import LogManager


def generate_sample_sales_data():
    """Generate sample sales data for demonstration"""
    products = ['Laptop', 'Desktop', 'Monitor', 'Keyboard', 'Mouse', 'Printer', 'Scanner', 'Tablet']
    regions = ['North', 'South', 'East', 'West', 'Central']
    sales_reps = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>']
    
    data = []
    base_date = datetime.now() - timedelta(days=365)
    
    for i in range(100):  # Generate 100 sales records
        record = {
            'Date': (base_date + timedelta(days=random.randint(0, 365))).strftime('%Y-%m-%d'),
            'Product': random.choice(products),
            'Region': random.choice(regions),
            'Sales_Rep': random.choice(sales_reps),
            'Quantity': random.randint(1, 50),
            'Unit_Price': round(random.uniform(50, 2000), 2),
            'Total_Sales': 0,  # Will be calculated
            'Commission_Rate': round(random.uniform(0.05, 0.15), 3),
            'Commission': 0  # Will be calculated
        }
        
        # Calculate totals
        record['Total_Sales'] = round(record['Quantity'] * record['Unit_Price'], 2)
        record['Commission'] = round(record['Total_Sales'] * record['Commission_Rate'], 2)
        
        data.append(record)
    
    return data


def generate_sample_financial_data():
    """Generate sample financial data for demonstration"""
    months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    
    data = []
    base_revenue = 100000
    
    for i, month in enumerate(months):
        # Simulate seasonal variations
        seasonal_factor = 1 + 0.2 * random.random() - 0.1
        revenue = round(base_revenue * seasonal_factor * (1 + i * 0.05), 2)
        
        record = {
            'Month': month,
            'Revenue': revenue,
            'Costs': round(revenue * random.uniform(0.6, 0.8), 2),
            'Marketing': round(revenue * random.uniform(0.05, 0.15), 2),
            'Operations': round(revenue * random.uniform(0.1, 0.2), 2),
            'Profit': 0,  # Will be calculated
            'Profit_Margin': 0  # Will be calculated
        }
        
        # Calculate profit
        total_expenses = record['Costs'] + record['Marketing'] + record['Operations']
        record['Profit'] = round(record['Revenue'] - total_expenses, 2)
        record['Profit_Margin'] = round((record['Profit'] / record['Revenue']) * 100, 2)
        
        data.append(record)
    
    return data


def generate_sample_system_data():
    """Generate sample system performance data"""
    servers = ['WEB-01', 'WEB-02', 'DB-01', 'DB-02', 'APP-01', 'APP-02']
    
    data = []
    base_time = datetime.now() - timedelta(hours=24)
    
    for i in range(144):  # 24 hours * 6 servers = 144 records (every 10 minutes)
        for server in servers:
            record = {
                'Timestamp': (base_time + timedelta(minutes=i*10)).strftime('%Y-%m-%d %H:%M:%S'),
                'Server': server,
                'CPU_Usage': round(random.uniform(10, 90), 1),
                'Memory_Usage': round(random.uniform(30, 85), 1),
                'Disk_Usage': round(random.uniform(40, 95), 1),
                'Network_In': round(random.uniform(1, 100), 2),
                'Network_Out': round(random.uniform(1, 80), 2),
                'Response_Time': round(random.uniform(50, 500), 0),
                'Status': random.choice(['OK', 'OK', 'OK', 'OK', 'WARNING', 'OK'])
            }
            data.append(record)
    
    return data[:50]  # Limit to 50 records for demo


def demo_excel_creation():
    """Demonstrate Excel creation capabilities"""
    print("🎯 CYBEX ENTERPRISE - EXCEL CREATION DEMO")
    print("=" * 60)
    
    # Initialize Excel tools
    log_manager = LogManager()
    excel_tools = ExcelTools(log_manager)
    
    # Demo 1: Sales Analysis Excel
    print("\n📊 Creating Sales Analysis Excel...")
    sales_data = generate_sample_sales_data()
    
    result = excel_tools.create_professional_excel(
        data=sales_data,
        file_path="demo_sales_analysis.xlsx",
        analysis_type="sales",
        style_theme="corporate_blue"
    )
    
    if result['success']:
        print(f"✅ Sales Excel created: {result['file_path']}")
        print(f"   Sheets: {', '.join(result['sheets_created'])}")
        print(f"   Records: {result['rows_processed']:,}")
    else:
        print(f"❌ Sales Excel failed: {result['error']}")
    
    # Demo 2: Financial Analysis Excel
    print("\n💰 Creating Financial Analysis Excel...")
    financial_data = generate_sample_financial_data()
    
    result = excel_tools.create_professional_excel(
        data=financial_data,
        file_path="demo_financial_analysis.xlsx",
        analysis_type="financial",
        style_theme="modern_green"
    )
    
    if result['success']:
        print(f"✅ Financial Excel created: {result['file_path']}")
        print(f"   Sheets: {', '.join(result['sheets_created'])}")
        print(f"   Records: {result['rows_processed']:,}")
    else:
        print(f"❌ Financial Excel failed: {result['error']}")
    
    # Demo 3: System Performance Excel
    print("\n🖥️ Creating System Performance Excel...")
    system_data = generate_sample_system_data()
    
    result = excel_tools.create_professional_excel(
        data=system_data,
        file_path="demo_system_analysis.xlsx",
        analysis_type="system",
        style_theme="executive"
    )
    
    if result['success']:
        print(f"✅ System Excel created: {result['file_path']}")
        print(f"   Sheets: {', '.join(result['sheets_created'])}")
        print(f"   Records: {result['rows_processed']:,}")
    else:
        print(f"❌ System Excel failed: {result['error']}")
    
    # Demo 4: General Analysis Excel
    print("\n📈 Creating General Analysis Excel...")
    general_data = [
        {'Category': 'A', 'Value1': 100, 'Value2': 150, 'Value3': 200},
        {'Category': 'B', 'Value1': 120, 'Value2': 180, 'Value3': 220},
        {'Category': 'C', 'Value1': 90, 'Value2': 130, 'Value3': 170},
        {'Category': 'D', 'Value1': 110, 'Value2': 160, 'Value3': 210},
        {'Category': 'E', 'Value1': 95, 'Value2': 140, 'Value3': 185}
    ]
    
    result = excel_tools.create_professional_excel(
        data=general_data,
        file_path="demo_general_analysis.xlsx",
        analysis_type="general",
        style_theme="corporate_blue"
    )
    
    if result['success']:
        print(f"✅ General Excel created: {result['file_path']}")
        print(f"   Sheets: {', '.join(result['sheets_created'])}")
        print(f"   Records: {result['rows_processed']:,}")
    else:
        print(f"❌ General Excel failed: {result['error']}")
    
    print("\n" + "=" * 60)
    print("🎉 EXCEL DEMO COMPLETED!")
    print("\nFiles created:")
    print("• demo_sales_analysis.xlsx - Sales data with corporate blue theme")
    print("• demo_financial_analysis.xlsx - Financial data with modern green theme")
    print("• demo_system_analysis.xlsx - System data with executive theme")
    print("• demo_general_analysis.xlsx - General data with corporate blue theme")
    print("\n💡 Open these files in Excel to see the professional formatting!")


def demo_excel_analysis():
    """Demonstrate Excel analysis capabilities"""
    print("\n🔍 EXCEL ANALYSIS DEMO")
    print("=" * 40)
    
    log_manager = LogManager()
    excel_tools = ExcelTools(log_manager)
    
    # Analyze created files
    files_to_analyze = [
        "demo_sales_analysis.xlsx",
        "demo_financial_analysis.xlsx",
        "demo_system_analysis.xlsx",
        "demo_general_analysis.xlsx"
    ]
    
    for file_path in files_to_analyze:
        if Path(file_path).exists():
            print(f"\n📋 Analyzing: {file_path}")
            result = excel_tools.analyze_existing_excel(file_path)
            
            if result['success']:
                analysis = result['analysis']
                print(f"   File Size: {analysis['file_size']:,} bytes")
                print(f"   Sheets: {analysis['total_sheets']}")
                for sheet in analysis['sheets']:
                    print(f"     • {sheet['name']}: {sheet['max_row']} rows, {sheet['max_column']} cols")
            else:
                print(f"   ❌ Analysis failed: {result['error']}")
        else:
            print(f"\n⚠️  File not found: {file_path}")


if __name__ == "__main__":
    try:
        demo_excel_creation()
        demo_excel_analysis()
        
        print("\n🚀 CYBEX ENTERPRISE EXCEL CAPABILITIES DEMONSTRATED!")
        print("✨ Ready to create professional Excel files with natural language commands!")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")
        import traceback
        traceback.print_exc()
