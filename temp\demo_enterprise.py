#!/usr/bin/env python3
"""
Demo Enterprise UI
Dimostrazione dell'interfaccia Enterprise di Cybex
"""

import sys
from pathlib import Path

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent))

def demo_colors():
    """Demo color palette"""
    print("🎨 Cybex Enterprise - Sunrise Color Palette Demo")
    print("=" * 60)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        
        print(f"\n🌅 Sunrise Color Palette:")
        print(f"   {SunriseColors.BLUE}■ Blue (75bde0) - Primary/Success{SunriseColors.RESET}")
        print(f"   {SunriseColors.YELLOW}■ Yellow (f8d49b) - Warning{SunriseColors.RESET}")
        print(f"   {SunriseColors.ORANGE}■ Orange (f8bc9b) - Info{SunriseColors.RESET}")
        print(f"   {SunriseColors.RED}■ Red (fb9b9b) - Error{SunriseColors.RESET}")
        print(f"   {SunriseColors.WHITE}■ White - Accent{SunriseColors.RESET}")
        print(f"   {SunriseColors.GRAY}■ Gray - Secondary{SunriseColors.RESET}")
        
        return True
    except Exception as e:
        print(f"❌ Color demo failed: {e}")
        return False


def demo_banner():
    """Demo banner"""
    print(f"\n🎯 Cybex Enterprise Banner Demo")
    print("=" * 40)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        
        # Simplified banner
        banner = f"""
{SunriseColors.BLUE}╔══════════════════════════════════════════════════════════════════════════════╗
║                                                                              ║
║   {SunriseColors.WHITE}  ██████╗██╗   ██╗██████╗ ███████╗██╗  ██╗                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██╔════╝╚██╗ ██╔╝██╔══██╗██╔════╝╚██╗██╔╝                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██║      ╚████╔╝ ██████╔╝█████╗   ╚███╔╝                                 {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ██║       ╚██╔╝  ██╔══██╗██╔══╝   ██╔██╗                                 {SunriseColors.BLUE}║
║   {SunriseColors.WHITE} ╚██████╗   ██║   ██████╔╝███████╗██╔╝ ██╗                                {SunriseColors.BLUE}║
║   {SunriseColors.WHITE}  ╚═════╝   ╚═╝   ╚═════╝ ╚══════╝╚═╝  ╚═╝                                {SunriseColors.BLUE}║
║                                                                              ║
║   {SunriseColors.ORANGE}                    Enterprise Edition                                    {SunriseColors.BLUE}║
║   {SunriseColors.GRAY}                      by AGTECHdesigne                                     {SunriseColors.BLUE}║
║                                                                              ║
╚══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
        print(banner)
        return True
    except Exception as e:
        print(f"❌ Banner demo failed: {e}")
        return False


def demo_quick_actions():
    """Demo quick actions menu"""
    print(f"\n🚀 Quick Actions Menu Demo")
    print("=" * 30)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors, QuickAction
        
        # Create sample quick actions
        quick_actions = {
            '1': QuickAction('1', 'System Status', 'Complete system overview', 'stato del sistema', '🖥️'),
            '2': QuickAction('2', 'Memory Status', 'RAM usage and recommendations', 'dammi la situazione memoria del computer', '🧠'),
            '3': QuickAction('3', 'Disk Status', 'Storage analysis and health', 'come sta il disco?', '💾'),
            '4': QuickAction('4', 'CPU Status', 'Processor usage and performance', 'situazione CPU', '⚡'),
            '5': QuickAction('5', 'Network Status', 'Network connectivity and traffic', 'stato della rete', '🌐'),
            '6': QuickAction('6', 'Active Processes', 'Running processes analysis', 'processi attivi', '🔄'),
            '7': QuickAction('7', 'System Cleanup', 'Clean temporary files', 'pulisci i file temporanei', '🧹'),
            '8': QuickAction('8', 'Optimize System', 'Performance optimization', 'ottimizza il sistema', '⚡'),
            '9': QuickAction('9', 'Performance Analysis', 'Detailed performance report', 'analizza le performance', '📊'),
        }
        
        print(f"{SunriseColors.ACCENT}╔═══════════════════════════════════════════════════════════════════════════════╗")
        print(f"║                            {SunriseColors.SUCCESS}🚀 QUICK ACTIONS{SunriseColors.ACCENT}                                    ║")
        print(f"╠═══════════════════════════════════════════════════════════════════════════════╣")
        
        # Print actions in 3 columns
        actions = list(quick_actions.values())
        for i in range(0, len(actions), 3):
            row_actions = actions[i:i+3]
            line = "║ "
            
            for action in row_actions:
                action_text = f"{action.icon} [{SunriseColors.WARNING}{action.key}{SunriseColors.ACCENT}] {action.name}"
                line += f"{action_text:<25} "
            
            line += " " * (77 - len(line.replace(SunriseColors.WARNING, '').replace(SunriseColors.ACCENT, '').replace(SunriseColors.RESET, ''))) + "║"
            print(line)
        
        print(f"╠═══════════════════════════════════════════════════════════════════════════════╣")
        print(f"║ {SunriseColors.INFO}⚙️  [C] Configuration{SunriseColors.ACCENT}  │  {SunriseColors.WARNING}🔄 [M] Switch Mode{SunriseColors.ACCENT}  │  {SunriseColors.ERROR}❌ [Q] Quit{SunriseColors.ACCENT}           ║")
        print(f"╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}")
        
        return True
    except Exception as e:
        print(f"❌ Quick actions demo failed: {e}")
        return False


def demo_features():
    """Demo enterprise features"""
    print(f"\n🌟 Enterprise Features Demo")
    print("=" * 35)
    
    try:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        
        features = f"""
{SunriseColors.SUCCESS}╔═══════════════════════════════════════════════════════════════════════════════╗
║                        {SunriseColors.ACCENT}🎯 CYBEX ENTERPRISE FEATURES{SunriseColors.SUCCESS}                              ║
╠═══════════════════════════════════════════════════════════════════════════════╣
║                                                                               ║
║ {SunriseColors.ACCENT}🎨 VISUAL DESIGN:{SunriseColors.SUCCESS}                                                               ║
║   ✅ Sunrise Color Palette (75bde0, f8d49b, f8bc9b, fb9b9b)                  ║
║   ✅ Professional ASCII Art Title                                            ║
║   ✅ "by AGTECHdesigne" Branding                                              ║
║   ✅ Structured Menu Layout                                                   ║
║                                                                               ║
║ {SunriseColors.ACCENT}🚀 QUICK ACTIONS (9 Preset Functions):{SunriseColors.SUCCESS}                                         ║
║   ✅ [1-9] Instant System Analysis                                           ║
║   ✅ One-Click Memory, Disk, CPU Status                                      ║
║   ✅ Automated Cleanup & Optimization                                        ║
║   ✅ Performance Analytics                                                    ║
║                                                                               ║
║ {SunriseColors.ACCENT}🔄 MODE SWITCHING:{SunriseColors.SUCCESS}                                                              ║
║   ✅ Instant Chat ↔ Agent Mode Toggle                                        ║
║   ✅ Visual Mode Indicators                                                   ║
║   ✅ Context-Aware Processing                                                 ║
║                                                                               ║
║ {SunriseColors.ACCENT}⚙️  CONFIGURATION MANAGEMENT:{SunriseColors.SUCCESS}                                                   ║
║   ✅ AI Model Selection (Ollama)                                             ║
║   ✅ 5 Pre-configured Models                                                  ║
║   ✅ Display Settings Control                                                 ║
║   ✅ System Information Access                                                ║
║                                                                               ║
║ {SunriseColors.ACCENT}🗣️  NATURAL LANGUAGE PROCESSING:{SunriseColors.SUCCESS}                                               ║
║   ✅ Italian Language Support                                                ║
║   ✅ Instant Local Processing                                                 ║
║   ✅ Structured Response Format                                               ║
║   ✅ Smart Recommendations                                                    ║
║                                                                               ║
╚═══════════════════════════════════════════════════════════════════════════════╝{SunriseColors.RESET}
"""
        print(features)
        return True
    except Exception as e:
        print(f"❌ Features demo failed: {e}")
        return False


def main():
    """Run Enterprise UI demo"""
    print("🎯 Cybex Enterprise UI - Complete Demo")
    print("=" * 60)
    
    demos = [
        ("Color Palette", demo_colors),
        ("ASCII Banner", demo_banner),
        ("Quick Actions Menu", demo_quick_actions),
        ("Enterprise Features", demo_features)
    ]
    
    passed = 0
    total = len(demos)
    
    for demo_name, demo_func in demos:
        try:
            if demo_func():
                print(f"✅ {demo_name}: SUCCESS")
                passed += 1
            else:
                print(f"❌ {demo_name}: FAILED")
        except Exception as e:
            print(f"❌ {demo_name}: ERROR - {e}")
    
    print(f"\n{'='*60}")
    print(f"🎯 Demo Results: {passed}/{total} demos successful")
    
    if passed == total:
        from cybex.interfaces.ui_cli_enterprise import SunriseColors
        print(f"\n{SunriseColors.SUCCESS}🎉 Cybex Enterprise UI Demo Complete!{SunriseColors.RESET}")
        print(f"{SunriseColors.INFO}🚀 Ready to launch: python main_enterprise.py{SunriseColors.RESET}")
        print(f"{SunriseColors.GRAY}Enterprise Edition by AGTECHdesigne{SunriseColors.RESET}")
        
        print(f"\n{SunriseColors.ACCENT}💬 Try these natural language commands:{SunriseColors.RESET}")
        print(f"   • dammi la situazione memoria del computer")
        print(f"   • come sta il disco?")
        print(f"   • stato del sistema")
        print(f"   • ottimizza il sistema")
        
        print(f"\n{SunriseColors.ACCENT}🚀 Or use Quick Actions [1-9] for instant results!{SunriseColors.RESET}")
    else:
        print(f"⚠️  {total - passed} demos failed")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
