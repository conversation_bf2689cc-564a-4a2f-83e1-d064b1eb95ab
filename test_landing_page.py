#!/usr/bin/env python3
"""
Test script per verificare la capacità di CYBEX di creare landing page AI
"""

import sys
import os
sys.path.insert(0, '.')

try:
    print("🚀 TESTING CYBEX LANDING PAGE CREATION CAPABILITY")
    print("=" * 60)
    
    # Import CYBEX agent tools
    from cybex.modules.agent_tools import AgentTools
    from cybex.core.cybex_core import SystemConfig, LogManager
    
    print("✅ CYBEX modules imported successfully")
    
    # Initialize components
    config = SystemConfig()
    log_manager = LogManager(config)
    agent_tools = AgentTools(log_manager)
    
    print(f"✅ Agent tools initialized with {len(agent_tools.get_available_tools())} tools")
    
    # Check if landing page tool is available
    available_tools = agent_tools.get_available_tools()
    
    if 'create_landing_page' in available_tools:
        print("✅ create_landing_page tool is available!")
        
        # Test creating a landing page
        print("\n🎨 Creating AI landing page...")
        
        result = agent_tools.execute_tool_direct('create_landing_page', {
            'title': 'CYBEX AI Solutions',
            'description': 'Advanced AI Assistant with 57+ Enterprise Tools for Professional Use',
            'filename': 'cybex_ai_landing.html',
            'theme': 'modern'
        })
        
        if result.success:
            print("✅ Landing page created successfully!")
            print(f"📄 Output: {result.output}")
            
            if result.metadata:
                print("📋 Metadata:")
                for key, value in result.metadata.items():
                    print(f"   • {key}: {value}")
            
            # Check if file exists
            if os.path.exists('cybex_ai_landing.html'):
                file_size = os.path.getsize('cybex_ai_landing.html')
                print(f"📁 File created: cybex_ai_landing.html ({file_size} bytes)")
                print("🌐 You can open this file in your browser to view the landing page")
            else:
                print("❌ File was not created")
                
        else:
            print(f"❌ Landing page creation failed: {result.error}")
    
    else:
        print("❌ create_landing_page tool not found")
        print("Available web development tools:")
        web_tools = [t for t in available_tools if 'web' in t or 'html' in t or 'create' in t]
        for tool in web_tools:
            print(f"   • {tool}")
    
    # Test other web development capabilities
    print(f"\n🛠️ CYBEX WEB DEVELOPMENT CAPABILITIES:")
    print(f"Total Tools Available: {len(available_tools)}")
    
    # Categorize tools for web development
    web_dev_tools = [
        'create_landing_page', 'create_html_file', 'create_css_file',
        'web_search', 'fetch_webpage', 'analyze_webpage'
    ]
    
    print("\n🌐 Web Development Tools:")
    for tool in web_dev_tools:
        if tool in available_tools:
            print(f"   ✅ {tool}")
        else:
            print(f"   ❌ {tool}")
    
    # Check AI integration tools
    ai_tools = [t for t in available_tools if 'ollama' in t or 'ai' in t]
    if ai_tools:
        print(f"\n🤖 AI Integration Tools ({len(ai_tools)}):")
        for tool in ai_tools:
            print(f"   ✅ {tool}")
    
    print(f"\n🎯 CYBEX LANDING PAGE CREATION CAPABILITY: {'✅ FULLY OPERATIONAL' if 'create_landing_page' in available_tools else '❌ NOT AVAILABLE'}")
    
    if 'create_landing_page' in available_tools:
        print("\n💡 USAGE EXAMPLES:")
        print("   • execute create_landing_page title='My AI Company' description='Advanced AI Solutions'")
        print("   • execute create_html_file filename='about.html' title='About Us'")
        print("   • execute create_css_file filename='custom.css'")
        print("\n🚀 CYBEX can create professional AI-themed landing pages with:")
        print("   • Modern responsive design")
        print("   • AI-themed color scheme (Sunrise palette)")
        print("   • Interactive animations")
        print("   • Professional layout")
        print("   • Mobile-friendly responsive design")
        print("   • Smooth scrolling navigation")
        print("   • Gradient backgrounds and effects")
    
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
