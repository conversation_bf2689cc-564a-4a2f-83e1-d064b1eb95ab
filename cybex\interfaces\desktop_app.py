#!/usr/bin/env python3
"""
CYBEX Enterprise Desktop App
Native desktop application using CEF Python for modern web UI
"""

import sys
import os
import threading
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from cefpython3 import cefpython as cef
    CEF_AVAILABLE = True
except ImportError:
    CEF_AVAILABLE = False

from cybex.interfaces.web_server import CybexWebServer

class CybexDesktopApp:
    """Desktop application wrapper for CYBEX Enterprise"""
    
    def __init__(self):
        self.web_server = None
        self.server_thread = None
        self.host = "127.0.0.1"
        self.port = 8080
        self.url = f"http://{self.host}:{self.port}"
        
    def start_web_server(self):
        """Start the web server in a separate thread"""
        def run_server():
            try:
                self.web_server = CybexWebServer(host=self.host, port=self.port)
                self.web_server.run()
            except Exception as e:
                print(f"Web server error: {e}")
        
        self.server_thread = threading.Thread(target=run_server, daemon=True)
        self.server_thread.start()
        
        # Wait for server to start
        self.wait_for_server()
    
    def wait_for_server(self, timeout=30):
        """Wait for web server to be ready"""
        import socket
        
        print("⏳ Waiting for web server to start...")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(1)
                result = sock.connect_ex((self.host, self.port))
                sock.close()
                
                if result == 0:
                    print("✅ Web server is ready!")
                    return True
                    
            except Exception:
                pass
            
            time.sleep(0.5)
        
        print("❌ Web server failed to start")
        return False
    
    def create_cef_app(self):
        """Create CEF application for native desktop experience"""
        if not CEF_AVAILABLE:
            print("❌ CEF Python not available")
            print("💡 Install with: pip install cefpython3")
            print("💡 Falling back to browser...")
            import webbrowser
            webbrowser.open(self.url)
            return False
        
        # CEF settings
        settings = {
            "debug": False,
            "log_severity": cef.LOGSEVERITY_INFO,
            "log_file": "cybex_cef.log",
            "multi_threaded_message_loop": False,
        }
        
        # Window settings
        window_info = cef.WindowInfo()
        window_info.SetAsChild(0, [0, 0, 1400, 900])
        
        # Browser settings
        browser_settings = {
            "web_security_disabled": True,
            "file_access_from_file_urls_allowed": True,
            "universal_access_from_file_urls_allowed": True,
        }
        
        # Initialize CEF
        cef.Initialize(settings)
        
        # Create browser
        browser = cef.CreateBrowserSync(
            window_info=window_info,
            url=self.url,
            window_title="CYBEX Enterprise - AI Assistant",
            settings=browser_settings
        )
        
        # Set client handlers
        client_handler = ClientHandler()
        browser.SetClientHandler(client_handler)
        
        return True
    
    def run_desktop_app(self):
        """Run the desktop application"""
        print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🚀 CYBEX ENTERPRISE DESKTOP APP                         ║
║                      Native Desktop Experience                              ║
║                        Powered by CEF Python                               ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)
        
        # Start web server
        self.start_web_server()
        
        # Create desktop app
        if self.create_cef_app():
            print("🖥️ Desktop app started successfully")
            print("💡 Close the window to exit")
            
            # Message loop
            cef.MessageLoop()
            
            # Shutdown
            cef.Shutdown()
        
        print("👋 CYBEX Enterprise Desktop App closed")
    
    def run_browser_fallback(self):
        """Run in browser if CEF is not available"""
        print("""
╔══════════════════════════════════════════════════════════════════════════════╗
║                    🌐 CYBEX ENTERPRISE WEB INTERFACE                       ║
║                      Browser-based Experience                               ║
║                        Modern Web Technologies                              ║
╚══════════════════════════════════════════════════════════════════════════════╝
        """)
        
        # Start web server
        self.start_web_server()
        
        # Open in browser
        import webbrowser
        print(f"🌐 Opening CYBEX Enterprise at: {self.url}")
        webbrowser.open(self.url)
        
        print("💡 Press Ctrl+C to stop the server")
        
        try:
            # Keep server running
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n🛑 Server stopped by user")

class ClientHandler:
    """CEF client handler for desktop app"""
    
    def OnBeforeClose(self, browser):
        """Called before browser closes"""
        print("🔄 Browser closing...")
    
    def OnLoadEnd(self, browser, frame, httpStatusCode):
        """Called when page finishes loading"""
        if httpStatusCode == 200:
            print("✅ CYBEX Enterprise loaded successfully")
        else:
            print(f"⚠️ Page loaded with status: {httpStatusCode}")
    
    def OnLoadError(self, browser, frame, errorCode, errorText, failedUrl):
        """Called when page fails to load"""
        print(f"❌ Load error: {errorText} ({errorCode})")
        print(f"Failed URL: {failedUrl}")

def install_cef_python():
    """Install CEF Python for desktop app support"""
    print("📦 Installing CEF Python for desktop app support...")
    try:
        import subprocess
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'cefpython3'])
        print("✅ CEF Python installed successfully")
        print("🔄 Please restart the application")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install CEF Python")
        print("💡 You can still use the web interface")
        return False

def main():
    """Main entry point"""
    app = CybexDesktopApp()
    
    if CEF_AVAILABLE:
        # Run as desktop app
        app.run_desktop_app()
    else:
        print("⚠️ CEF Python not available for desktop app")
        print("💡 Would you like to install it? (y/n)")
        
        try:
            choice = input().lower().strip()
            if choice in ['y', 'yes']:
                if install_cef_python():
                    return
        except KeyboardInterrupt:
            print("\n🛑 Installation cancelled")
        
        # Fallback to browser
        app.run_browser_fallback()

if __name__ == "__main__":
    main()
