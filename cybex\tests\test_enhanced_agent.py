#!/usr/bin/env python3
"""
Comprehensive Test Suite for Enhanced CYBEX Agent
Tests all enhanced features and enterprise-grade functionality
"""

import unittest
import time
import json
import tempfile
import shutil
from pathlib import Path
import sys
import os

# Add cybex to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from cybex.modules.enhanced_ai_integration import EnhancedAIIntegration
from cybex.modules.advanced_context_manager import AdvancedContextManager
from cybex.modules.security_first_architecture import SecurityFirstArchitecture
from cybex.modules.performance_optimizer import PerformanceOptimizer
from cybex.modules.agent_tools import AgentTools
from cybex.modules.natural_language_processor import NaturalLanguageProcessor
from cybex.modules.log_manager import LogManager


class TestEnhancedAIIntegration(unittest.TestCase):
    """Test enhanced AI integration functionality"""
    
    def setUp(self):
        self.ai_integration = EnhancedAIIntegration()
    
    def test_natural_language_parsing(self):
        """Test natural language command parsing"""
        test_commands = [
            "Esegui security audit completo",
            "Analizza performance sistema",
            "Scansiona sicurezza rete",
            "Pulisci file temporanei"
        ]
        
        for command in test_commands:
            with self.subTest(command=command):
                result = self.ai_integration.parse_natural_language_command(command)
                
                self.assertIsInstance(result, dict)
                self.assertIn('tool', result)
                self.assertIn('intent', result)
                self.assertIn('priority', result)
                self.assertIn('parameters', result)
    
    def test_enterprise_response_formatting(self):
        """Test enterprise response formatting"""
        mock_result = {
            "success": True,
            "output": "Security audit completed successfully"
        }
        mock_context = {
            "operation": "Security Audit",
            "priority": "high",
            "intent": "security_analysis"
        }
        
        formatted_response = self.ai_integration.format_enterprise_response(mock_result, mock_context)
        
        self.assertIsInstance(formatted_response, str)
        self.assertIn("CYBEX ENTERPRISE", formatted_response)
        self.assertIn("EXECUTIVE SUMMARY", formatted_response)
        self.assertIn("RECOMMENDATIONS", formatted_response)


class TestAdvancedContextManager(unittest.TestCase):
    """Test advanced context management"""
    
    def setUp(self):
        self.context_manager = AdvancedContextManager()
    
    def test_context_initialization(self):
        """Test context manager initialization"""
        self.assertIsNotNone(self.context_manager.current_context)
        self.assertIn('system', self.context_manager.current_context)
        self.assertIn('security', self.context_manager.current_context)
        self.assertIn('performance', self.context_manager.current_context)
        self.assertIn('metadata', self.context_manager.current_context)
    
    def test_context_update(self):
        """Test context update functionality"""
        initial_update_count = self.context_manager.current_context['metadata']['update_count']
        
        updated_context = self.context_manager.update_context(force=True)
        
        self.assertGreater(
            updated_context['metadata']['update_count'],
            initial_update_count
        )
        self.assertIsInstance(updated_context['system'], dict)
        self.assertIn('cpu_percent', updated_context['system'])
    
    def test_context_components_retrieval(self):
        """Test specific context components retrieval"""
        components = ['system', 'performance']
        context = self.context_manager.get_context(components)
        
        self.assertEqual(len(context), 2)
        self.assertIn('system', context)
        self.assertIn('performance', context)


class TestSecurityFirstArchitecture(unittest.TestCase):
    """Test security-first architecture"""
    
    def setUp(self):
        self.security_arch = SecurityFirstArchitecture()
    
    def test_operation_validation_approved(self):
        """Test operation validation for approved operations"""
        user_context = {
            'user_id': 'test_user',
            'session_id': 'test_session',
            'expertise_level': 'EXPERT'
        }
        
        approved, message, details = self.security_arch.validate_operation(
            'get_system_info', user_context
        )
        
        self.assertTrue(approved)
        self.assertIsInstance(details, dict)
        self.assertIn('security_level', details)
        self.assertIn('risk_score', details)
    
    def test_operation_validation_blocked(self):
        """Test operation validation for blocked operations"""
        user_context = {
            'user_id': 'test_user',
            'session_id': 'test_session',
            'expertise_level': 'BEGINNER'
        }
        
        # Test with high-risk operation
        approved, message, details = self.security_arch.validate_operation(
            'execute_command', user_context, data='format C:'
        )
        
        self.assertFalse(approved)
        self.assertIn('blocked', message.lower())
    
    def test_security_event_logging(self):
        """Test security event logging"""
        initial_events = len(self.security_arch.active_security_events)
        
        self.security_arch._log_security_event(
            event_type="TEST_EVENT",
            severity="INFO",
            source="test",
            target="test_target",
            description="Test security event",
            user_context={'user_id': 'test'},
            risk_score=10
        )
        
        self.assertEqual(
            len(self.security_arch.active_security_events),
            initial_events + 1
        )
    
    def test_security_status(self):
        """Test security status retrieval"""
        status = self.security_arch.get_security_status()
        
        self.assertIsInstance(status, dict)
        self.assertIn('threat_level', status)
        self.assertIn('active_events', status)
        self.assertIn('compliance_mode', status)


class TestPerformanceOptimizer(unittest.TestCase):
    """Test performance optimization"""
    
    def setUp(self):
        self.optimizer = PerformanceOptimizer()
    
    def test_cache_functionality(self):
        """Test intelligent caching"""
        # Test cache set and get
        self.optimizer.cache.set('test_key', 'test_value', ttl=60)
        cached_value = self.optimizer.cache.get('test_key')
        
        self.assertEqual(cached_value, 'test_value')
        
        # Test cache miss
        missing_value = self.optimizer.cache.get('nonexistent_key')
        self.assertIsNone(missing_value)
    
    def test_operation_optimization(self):
        """Test operation optimization"""
        def test_operation(x, y):
            time.sleep(0.01)  # Simulate work
            return x + y
        
        # First execution (cache miss)
        result1 = self.optimizer.optimize_operation('test_add', test_operation, 5, 10)
        self.assertEqual(result1, 15)
        
        # Second execution (should be cached)
        start_time = time.time()
        result2 = self.optimizer.optimize_operation('test_add', test_operation, 5, 10)
        execution_time = time.time() - start_time
        
        self.assertEqual(result2, 15)
        self.assertLess(execution_time, 0.005)  # Should be much faster due to caching
    
    def test_performance_report(self):
        """Test performance report generation"""
        # Execute some operations to generate metrics
        def dummy_operation():
            time.sleep(0.001)
            return "done"
        
        for i in range(5):
            self.optimizer.optimize_operation(f'test_op_{i}', dummy_operation)
        
        report = self.optimizer.get_performance_report()
        
        self.assertIsInstance(report, dict)
        self.assertIn('cache_performance', report)
        self.assertIn('resource_status', report)
        self.assertIn('overall_metrics', report)
        self.assertIn('optimization_suggestions', report)


class TestAgentToolsEnhanced(unittest.TestCase):
    """Test enhanced agent tools functionality"""
    
    def setUp(self):
        self.log_manager = LogManager()
        self.agent_tools = AgentTools(self.log_manager)
    
    def test_enhanced_tool_execution(self):
        """Test enhanced tool execution with optimization"""
        # Test basic tool execution
        result = self.agent_tools.execute_tool_optimized('get_system_info')
        
        self.assertIsNotNone(result)
        self.assertTrue(hasattr(result, 'success'))
        self.assertTrue(hasattr(result, 'output'))
        self.assertTrue(hasattr(result, 'risk_level'))
    
    def test_security_validation(self):
        """Test security validation in tool execution"""
        # Test with potentially risky parameters
        result = self.agent_tools._perform_security_check(
            'execute_command',
            {'command': 'dir'},
            'MEDIUM'
        )
        
        self.assertIsNotNone(result)
        self.assertTrue(hasattr(result, 'success'))
    
    def test_tool_registry(self):
        """Test tool registry functionality"""
        available_tools = self.agent_tools.get_available_tools()
        
        self.assertIsInstance(available_tools, dict)
        self.assertGreater(len(available_tools), 0)
        
        # Check for essential tools
        essential_tools = ['execute_command', 'get_system_info', 'security_audit']
        for tool in essential_tools:
            self.assertIn(tool, available_tools)


class TestIntegrationScenarios(unittest.TestCase):
    """Test integration scenarios and end-to-end functionality"""
    
    def setUp(self):
        self.log_manager = LogManager()
        self.agent_tools = AgentTools(self.log_manager)
        self.ai_integration = EnhancedAIIntegration()
    
    def test_natural_language_to_tool_execution(self):
        """Test complete flow from natural language to tool execution"""
        # Parse natural language command
        command = "Mostra informazioni sistema"
        parsed = self.ai_integration.parse_natural_language_command(command)
        
        # Execute corresponding tool
        tool_name = parsed.get('tool', 'get_system_info')
        result = self.agent_tools.execute_tool_optimized(tool_name)
        
        self.assertIsNotNone(result)
        self.assertTrue(result.success)
    
    def test_security_and_performance_integration(self):
        """Test integration between security and performance systems"""
        # Execute a security-sensitive operation
        result = self.agent_tools.execute_tool_optimized(
            'security_audit',
            {'scope': 'basic'}
        )
        
        self.assertIsNotNone(result)
        # Should have security validation
        self.assertTrue(hasattr(result, 'security_validated'))
        # Should have performance metrics
        self.assertTrue(hasattr(result, 'execution_time'))
    
    def test_context_aware_operations(self):
        """Test context-aware operation execution"""
        # This would test how operations adapt based on system context
        # For now, just verify context is available
        if hasattr(self.agent_tools, 'context_manager') and self.agent_tools.context_manager:
            context = self.agent_tools.context_manager.get_context()
            self.assertIsInstance(context, dict)
            self.assertIn('system', context)


class TestEnterpriseFeatures(unittest.TestCase):
    """Test enterprise-specific features"""
    
    def test_audit_trail_generation(self):
        """Test audit trail generation"""
        security_arch = SecurityFirstArchitecture()
        
        initial_audit_count = len(security_arch.audit_trail)
        
        # Trigger an auditable event
        security_arch._log_security_event(
            event_type="AUDIT_TEST",
            severity="INFO",
            source="test_system",
            target="test_resource",
            description="Test audit event",
            user_context={'user_id': 'test_user'},
            risk_score=5
        )
        
        self.assertGreater(len(security_arch.audit_trail), initial_audit_count)
    
    def test_compliance_checking(self):
        """Test compliance checking functionality"""
        security_arch = SecurityFirstArchitecture()
        
        compliance_result = security_arch.compliance_checker.check_operation_compliance(
            'test_operation',
            {'user_id': 'test'},
            security_arch._determine_security_level('test_operation'),
            50
        )
        
        self.assertIsInstance(compliance_result, dict)
        self.assertIn('compliant', compliance_result)
    
    def test_enterprise_reporting(self):
        """Test enterprise reporting capabilities"""
        optimizer = PerformanceOptimizer()
        
        # Generate some activity
        def test_op():
            return "test"
        
        optimizer.optimize_operation('enterprise_test', test_op)
        
        report = optimizer.get_performance_report()
        
        # Verify enterprise-grade reporting structure
        self.assertIn('cache_performance', report)
        self.assertIn('resource_status', report)
        self.assertIn('optimization_suggestions', report)
        self.assertIsInstance(report['optimization_suggestions'], list)


def run_comprehensive_tests():
    """Run all tests and generate comprehensive report"""
    print("🚀 CYBEX ENTERPRISE - COMPREHENSIVE TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add all test classes
    test_classes = [
        TestEnhancedAIIntegration,
        TestAdvancedContextManager,
        TestSecurityFirstArchitecture,
        TestPerformanceOptimizer,
        TestAgentToolsEnhanced,
        TestIntegrationScenarios,
        TestEnterpriseFeatures
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Generate summary report
    print("\n" + "=" * 60)
    print("📊 TEST EXECUTION SUMMARY")
    print("=" * 60)
    print(f"Tests Run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success Rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n❌ FAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  • {test}: {traceback.split('AssertionError: ')[-1].split('\\n')[0] if 'AssertionError:' in traceback else 'Unknown failure'}")
    
    if result.errors:
        print(f"\n🔥 ERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  • {test}: {traceback.split('\\n')[-2] if traceback else 'Unknown error'}")
    
    # Overall assessment
    if len(result.failures) == 0 and len(result.errors) == 0:
        print(f"\n🎉 ALL TESTS PASSED!")
        print("✅ CYBEX ENTERPRISE is ready for production deployment!")
        return True
    else:
        print(f"\n⚠️  Some tests failed. Review and fix issues before deployment.")
        return False


if __name__ == "__main__":
    success = run_comprehensive_tests()
    sys.exit(0 if success else 1)
