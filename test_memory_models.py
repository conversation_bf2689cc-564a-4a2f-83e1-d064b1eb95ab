#!/usr/bin/env python3
"""
Test modelli con memoria limitata
"""

import requests
import json
import time

def test_small_models():
    """Test modelli più piccoli che dovrebbero funzionare con 7.4GB RAM"""
    
    print("🧠 TESTING SMALL MODELS FOR LIMITED MEMORY")
    print("=" * 50)
    print("💾 System RAM: ~7.4 GB")
    print("🎯 Testing models that should work with limited memory\n")
    
    # Modelli ordinati per dimensione (più piccoli prima)
    models_to_test = [
        ("deepseek-r1:1.5b", "1.8B parameters - Should work"),
        ("gemma3:4b", "4.3B parameters - Might work"),
        ("AGtech:latest", "7.6B parameters - Risky"),
        ("deepseek-r1:latest", "7.6B parameters - Risky")
    ]
    
    working_models = []
    
    for model_name, description in models_to_test:
        print(f"🧠 Testing {model_name}")
        print(f"   📋 {description}")
        
        try:
            payload = {
                "model": model_name,
                "prompt": "Hi! Just say 'Hello' to test if you work.",
                "stream": False,
                "options": {
                    "temperature": 0.1,
                    "max_tokens": 10,
                    "num_predict": 10
                }
            }
            
            print("   📤 Sending request...")
            start_time = time.time()
            
            response = requests.post(
                "http://localhost:11434/api/generate",
                json=payload,
                timeout=45  # Shorter timeout
            )
            
            end_time = time.time()
            response_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                
                if 'response' in result and result['response'].strip():
                    print(f"   ✅ SUCCESS! Response time: {response_time:.1f}s")
                    print(f"   💬 Response: {result['response'][:50]}...")
                    working_models.append(model_name)
                else:
                    print(f"   ❌ Empty response")
            else:
                error_msg = response.text
                if "memory" in error_msg.lower():
                    print(f"   ❌ MEMORY ERROR: Not enough RAM")
                else:
                    print(f"   ❌ ERROR: {response.status_code}")
                    print(f"   📝 {error_msg[:100]}...")
                    
        except requests.exceptions.Timeout:
            print(f"   ⏰ TIMEOUT: Model too slow to load (45s)")
        except Exception as e:
            print(f"   ❌ EXCEPTION: {e}")
        
        print()  # Empty line
        time.sleep(2)  # Wait between tests
    
    print("🎯 RESULTS:")
    print(f"   ✅ Working models: {len(working_models)}")
    for model in working_models:
        print(f"      • {model}")
    
    if not working_models:
        print("   ❌ No models working - possible issues:")
        print("      • Insufficient RAM (need more than 7.4GB)")
        print("      • Ollama server issues")
        print("      • Model loading problems")
    
    return working_models

def recommend_solutions():
    """Raccomanda soluzioni per il problema memoria"""
    print("\n💡 RECOMMENDED SOLUTIONS:")
    print("=" * 30)
    
    print("🔧 IMMEDIATE FIXES:")
    print("   1. Use smaller models (1.5B parameters)")
    print("   2. Close other applications to free RAM")
    print("   3. Restart Ollama service")
    print("   4. Use models with Q4_K_M quantization")
    
    print("\n🚀 LONG-TERM SOLUTIONS:")
    print("   1. Upgrade system RAM to 16GB+")
    print("   2. Use cloud-based AI models")
    print("   3. Configure virtual memory/swap")
    print("   4. Use CPU-only inference with smaller models")
    
    print("\n⚙️ CYBEX CONFIGURATION:")
    print("   1. Set default model to smallest working model")
    print("   2. Increase timeout values for slow loading")
    print("   3. Add memory checks before model switching")
    print("   4. Implement graceful fallback to smaller models")

if __name__ == "__main__":
    working_models = test_small_models()
    recommend_solutions()
    
    if working_models:
        print(f"\n🎉 GOOD NEWS: {len(working_models)} model(s) working!")
        print("   Configure CYBEX to use these models as default.")
    else:
        print("\n⚠️ CRITICAL: No models working with current memory.")
        print("   System needs more RAM or smaller models.")
