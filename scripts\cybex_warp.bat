@echo off
REM ============================================================================
REM Cybex Warp Launcher
REM Advanced AI Terminal - Next Generation of Warp
REM ============================================================================

title Cybex Warp - Advanced AI Terminal

REM Set colors for better visibility
color 0F

echo.
echo ╔══════════════════════════════════════════════════════════════════════════════╗
echo ║                                                                              ║
echo ║                    🚀 CYBEX WARP LAUNCHER                                   ║
echo ║                   Advanced AI Terminal                                      ║
echo ║                                                                              ║
echo ╚══════════════════════════════════════════════════════════════════════════════╝
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+ first.
    echo 💡 Download from: https://python.org
    pause
    exit /b 1
)

echo ✅ Python detected
echo.

REM Check if main file exists
if not exist "cybex_warp.py" (
    echo ❌ cybex_warp.py not found
    echo 💡 Make sure you're in the correct directory
    pause
    exit /b 1
)

echo ✅ Cybex Warp files found
echo.

REM Launch Cybex Warp
echo 🚀 Starting Cybex Warp...
echo.

python cybex_warp.py

REM Handle exit
echo.
echo 👋 Cybex Warp session ended
pause
